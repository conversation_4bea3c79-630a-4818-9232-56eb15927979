module Jchem
  # Location of the jchem server we are using
#  SITE = "http://jchem.wishartlab.com/"
  SITE = "http://137.184.206.181:8080"
  # Build the full uri
  def self.full_uri(path)
    URI.join(SITE,"/jchem/",path)
  end

  # For getting requests
  def self.get(path,*options)
    Typhoeus.get(self.full_uri(path),*options)
  end

  # For posting requests
  def self.post(path,body)
    Typhoeus.post self.full_uri(path),
      headers: { "Content-type" => "application/json" },
      body: body.to_json
  end

  # Example:
  #
  # Jchem.structure_to_inchi("InChI=1S/C3H8O2/c1-2-3-5-4/h4H,2-3H2,1H3\nAuxInfo=1/0/N:1,2,3,5,4/rA:5CCCOO/rB:s1;s2;s3;s4;/rC:4.62,-2.6674,0;3.85,-1.3337,0;2.31,-1.3337,0;1.54,0,0;;\n")
  # #=> "CCCOO"
  #
  def self.structure_to_inchi(structure)
    response = self.post( "rest-v0/util/calculate/stringMolExport",
       { structure: structure, parameters: "inchi"}  )
    response.response_body
  end

  # Example:
  #
  # Jchem.structure_to_inchikey("InChI=1S/C3H8O2/c1-2-3-5-4/h4H,2-3H2,1H3\nAuxInfo=1/0/N:1,2,3,5,4/rA:5CCCOO/rB:s1;s2;s3;s4;/rC:4.62,-2.6674,0;3.85,-1.3337,0;2.31,-1.3337,0;1.54,0,0;;\n")
  # #=> "InChIKey=TURGQPDWYFJEDY-UHFFFAOYSA-N"
  #
  def self.structure_to_inchikey(structure)
    response = self.post( "rest-v0/util/calculate/stringMolExport",
       { structure: structure, parameters: "inchikey"}  )
    response.response_body
  end

  # Example:
  #
  # Jchem.structure_to_inchi("CCCOO")
  # #=> "InChI=1S/C3H8O2/c1-2-3-5-4/h4H,2-3H2,1H3\nAuxInfo=1/0/N:1,2,3,5,4/rA:5CCCOO/rB:s1;s2;s3;s4;/rC:4.62,-2.6674,0;3.85,-1.3337,0;2.31,-1.3337,0;1.54,0,0;;\n"
  #
  def self.structure_to_smiles(structure)
    response = self.post( "rest-v0/util/calculate/stringMolExport",
       { structure: structure, parameters: "smiles"}  )
    response.response_body
  end

  # Example:
  #
  # Jchem.structure_to_name("CCCOO")
  # #=> "propane-1-peroxol"
  #
  def self.structure_to_name(structure)
    response = self.post( "rest-v0/util/calculate/stringMolExport",
       { structure: structure, parameters: "name"}  )
    response.response_body
  end

  # Example
  #
  # Jchem.structure_to_jpeg("CCCOO")["binaryStructure"]
  # #=> image data string
  #
  def self.structure_to_jpeg(structure)
    response = self.post( "rest-v0/util/calculate/molExport",
       { structure: structure, parameters: "jpeg"}  )
    JSON.parse(response.response_body)
  end

  def self.structure_to_jpeg_file(structure,filename)
    data = self.structure_to_jpeg(structure)["binaryStructure"]
    File.open(filename,"wb" ){|f| f.puts Base64.decode64(data)}
  end

  # Use in the same way as structure_to_jpeg
  def self.structure_to_png(structure)
    response = self.post( "rest-v0/util/calculate/molExport",
       { structure: structure, parameters: "jpeg"}  )
    JSON.parse(response.response_body)
  end

  def self.structure_to_png_file(structure,filename)
    data = self.structure_to_png(structure)["binaryStructure"]
    File.open(filename,"wb" ){|f| f.puts Base64.decode64(data)}
  end


  def self.test
    self.get("rest-v0", verbose: true)
  end
end
