module ChemConvert
  
  def self.convert(input, format)
    convert_url = "#{JCHEM_CONFIG[:url]}/util/calculate/stringMolExport"
    options = { structure: input, parameters: format.to_s }
    RestClient.post convert_url, options.to_json, content_type: :json
  end

  def self.inchikey(structure)
    # A standard inchikey is generated with the options:
    # inchikey:AuxNone,Woff,SAbs
    # This is batshit insane that JChem doesn't output standard inchi/inchikey
    # by default, but alas that's the way it is...
    begin
      self.convert(structure, STRUCTURE_INCHIKEY).to_s.sub('InChIKey=', '')
    rescue Exception => e
      # logger.error "Could not grab inchikey - invalid structure"
      nil
    end
  end

  def self.inchi(structure)
    # A standard inchi is generated with the options:
    # inchi:AuxNone,Woff,SAbs
    # This is batshit insane that <PERSON><PERSON><PERSON> doesn't output standard inchi/inchikey
    # by default, but alas that's the way it is...
    begin
      self.convert(structure, STRUCTURE_FORMATS[:inchi]).to_s.strip
    rescue Exception => e
      # logger.error "Could not grab inchi - invalid structure"
      nil
    end
  end
end
