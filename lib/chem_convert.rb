module ChemConvert
  def self.convert(input, format)
    script = <<~PYTH<PERSON>
      import sys
      import json
      import base64
      from rdkit import Chem
      from rdkit.Chem import AllChem, Draw
      from rdkit.Chem.Draw import rdMolDraw2D
      
      print("DEBUG: Starting conversion process")
      print(f"DEBUG: Python version: {sys.version}")
      
      with open(sys.argv[1], 'r') as f:
          data = json.loads(f.read().strip())
          structure = data["structure"]
          format_param = data["format"]
      
      print(f"DEBUG: Input structure: {structure[:20]}...")
      print(f"DEBUG: Format parameter: {format_param}")
      
      # Parse the input structure
      mol = None
      if structure.startswith('InChI='):
          print("DEBUG: Parsing InChI structure")
          mol = Chem.MolFromInchi(structure)
      else:
          print("DEBUG: Parsing SMILES structure")
          mol = Chem.MolFromSmiles(structure)
      
      if mol:
          print(f"DEBUG: Structure parsed successfully. Atom count: {mol.GetNumAtoms()}")
      else:
          print("DEBUG: Failed to parse structure")
      
      result = ""
      if mol:
          print(f"DEBUG: Converting structure to {format_param} format")
          print(f"DEBUG: is a string? {isinstance(format_param, str)}")
          # Handle different format types
          if "smiles" in format_param:
              print("DEBUG: Converting to SMILES")
              result = Chem.MolToSmiles(mol, isomericSmiles=True)
              print(f"DEBUG: SMILES result: {result}")
          elif "inchi" in format_param:
              print("DEBUG: Converting to InChI")
              result = AllChem.MolToInchi(mol)
              print(f"DEBUG: InChI result: {result[:30]}...")
          elif "inchikey" in format_param:
              print("DEBUG: Converting to InChIKey")
              inchi = AllChem.MolToInchi(mol)
              result = "InChIKey=" + AllChem.InchiToInchiKey(inchi)
              print(f"DEBUG: InChIKey result: {result}")
          elif "mol" in format_param:
              print("DEBUG: Converting to MOL format")
              result = Chem.MolToMolBlock(mol)
              print(f"DEBUG: MOL result length: {len(result)} characters")
          elif "pdb" in format_param:
              print("DEBUG: Converting to PDB format")
              # Generate 3D coordinates if needed
              mol_with_h = Chem.AddHs(mol)
              print(f"DEBUG: Added hydrogens. New atom count: {mol_with_h.GetNumAtoms()}")
              AllChem.EmbedMolecule(mol_with_h)
              print("DEBUG: Embedded molecule in 3D")
              AllChem.MMFFOptimizeMolecule(mol_with_h)
              print("DEBUG: Optimized 3D structure")
              result = Chem.MolToPDBBlock(mol_with_h)
              print(f"DEBUG: PDB result length: {len(result)} characters")
          elif "svg" in format_param:
              print("DEBUG: Converting to SVG format")
              # Parse options from format string
              width = 500
              height = 500
              if "w" in format_param:
                  # Extract width from format string (e.g., w500)
                  import re
                  width_match = re.search(r'w(\d+)', format_param)
                  if width_match:
                      width = int(width_match.group(1))
              if "h" in format_param:
                  # Extract height from format string (e.g., h500)
                  import re
                  height_match = re.search(r'h(\d+)', format_param)
                  if height_match:
                      height = int(height_match.group(1))
              
              print(f"DEBUG: SVG dimensions: {width}x{height}")
              
              # Create SVG
              drawer = rdMolDraw2D.MolDraw2DSVG(width, height)
              drawer.DrawMolecule(mol)
              drawer.FinishDrawing()
              svg = drawer.GetDrawingText()
              print(f"DEBUG: SVG generated. Length: {len(svg)} characters")
              
              # Check if base64 encoding is requested
              if "base64" in format_param:
                  print("DEBUG: Base64 encoding SVG")
                  svg_bytes = svg.encode('utf-8')
                  result = base64.b64encode(svg_bytes).decode('utf-8')
                  print(f"DEBUG: Base64 result length: {len(result)} characters")
              else:
                  result = svg
          elif "png" in format_param:
              print("DEBUG: Converting to PNG format")
              # Parse options from format string
              width = 150
              height = 150
              if "w" in format_param:
                  # Extract width from format string (e.g., w500)
                  import re
                  width_match = re.search(r'w(\d+)', format_param)
                  if width_match:
                      width = int(width_match.group(1))
              if "h" in format_param:
                  # Extract height from format string (e.g., h500)
                  import re
                  height_match = re.search(r'h(\d+)', format_param)
                  if height_match:
                      height = int(height_match.group(1))
              
              print(f"DEBUG: PNG dimensions: {width}x{height}")
              
              # Generate PNG image
              img = Draw.MolToImage(mol, size=(width, height))
              print("DEBUG: PNG image generated")
              
              # Convert to base64
              import io
              buffer = io.BytesIO()
              img.save(buffer, format="PNG")
              result = base64.b64encode(buffer.getvalue()).decode('utf-8')
              print(f"DEBUG: PNG base64 result length: {len(result)} characters")
          else:
              print(f"DEBUG: Unsupported format: {format_param}")
      else:
          print("DEBUG: No valid molecule to convert")
      
      print(f"DEBUG: Final result type: {type(result)}")
      print(f"DEBUG: Final result length: {len(result)} characters")
      
      with open(sys.argv[2], 'w') as f:
          f.write(result)
      
      print("DEBUG: Result written to output file")
    PYTHON
    
    # Prepare input data
    input_data = {
      "structure" => input,
      "format" => format.to_s
    }
    
    puts "Ruby: Starting conversion with format: #{format}"
    result = Jchem.run_rdkit(script, input_data.to_json)
    puts "Ruby: Conversion completed. Result length: #{result.length} characters"
    result
  end

  # Add a method specifically for SVG generation
  def self.svg(structure, options = {})
    begin
      width = options[:width] || 500
      height = options[:height] || 500
      base64_encode = options[:base64] || false
      
      puts "Ruby: Starting SVG generation with options: width=#{width}, height=#{height}, base64=#{base64_encode}"
      
      script = <<~PYTHON
        import sys
        import json
        import base64
        from rdkit import Chem
        from rdkit.Chem.Draw import rdMolDraw2D
        
        print("DEBUG: Starting SVG generation")
        
        with open(sys.argv[1], 'r') as f:
            data = json.loads(f.read().strip())
            structure = data["structure"]
            width = data["width"]
            height = data["height"]
            base64_encode = data["base64_encode"]
        
        print(f"DEBUG: Input structure: {structure[:20]}...")
        print(f"DEBUG: Dimensions: {width}x{height}, Base64: {base64_encode}")
        
        # Parse the input structure
        mol = None
        if structure.startswith('InChI='):
            print("DEBUG: Parsing InChI structure")
            mol = Chem.MolFromInchi(structure)
        else:
            print("DEBUG: Parsing SMILES structure")
            mol = Chem.MolFromSmiles(structure)
        
        if mol:
            print(f"DEBUG: Structure parsed successfully. Atom count: {mol.GetNumAtoms()}")
        else:
            print("DEBUG: Failed to parse structure")
        
        result = ""
        if mol:
            # Create SVG
            print("DEBUG: Creating SVG drawer")
            drawer = rdMolDraw2D.MolDraw2DSVG(width, height)
            print("DEBUG: Drawing molecule")
            drawer.DrawMolecule(mol)
            print("DEBUG: Finishing drawing")
            drawer.FinishDrawing()
            svg = drawer.GetDrawingText()
            print(f"DEBUG: SVG generated. Length: {len(svg)} characters")
            
            # Base64 encode if requested
            if base64_encode:
                print("DEBUG: Base64 encoding SVG")
                svg_bytes = svg.encode('utf-8')
                result = base64.b64encode(svg_bytes).decode('utf-8')
                print(f"DEBUG: Base64 result length: {len(result)} characters")
            else:
                result = svg
        else:
            print("DEBUG: No valid molecule to convert to SVG")
        
        print(f"DEBUG: Final result length: {len(result)} characters")
        
        with open(sys.argv[2], 'w') as f:
            f.write(result)
        
        print("DEBUG: Result written to output file")
      PYTHON
      
      # Prepare input data
      input_data = {
        "structure" => structure,
        "width" => width,
        "height" => height,
        "base64_encode" => base64_encode
      }
      
      result = Jchem.run_rdkit(script, input_data.to_json)
      puts "Ruby: SVG generation completed. Result length: #{result.length} characters"
      result
    rescue Exception => e
      puts "Ruby: Error generating SVG: #{e.message}"
      puts e.backtrace.join("\n")
      # logger.error "Could not generate SVG - invalid structure"
      nil
    end
  end

  # Add a method specifically for PNG generation
  def self.png(structure, options = {})
    begin
      width = options[:width] || 500
      height = options[:height] || 500
      
      puts "Ruby: Starting PNG generation with options: width=#{width}, height=#{height}"
      
      script = <<~PYTHON
        import sys
        import json
        import base64
        from rdkit import Chem
        from rdkit.Chem import Draw
        
        print("DEBUG: Starting PNG generation")
        
        with open(sys.argv[1], 'r') as f:
            data = json.loads(f.read().strip())
            structure = data["structure"]
            width = data["width"]
            height = data["height"]
        
        print(f"DEBUG: Input structure: {structure[:20]}...")
        print(f"DEBUG: Dimensions: {width}x{height}")
        
        # Parse the input structure
        mol = None
        if structure.startswith('InChI='):
            print("DEBUG: Parsing InChI structure")
            mol = Chem.MolFromInchi(structure)
        else:
            print("DEBUG: Parsing SMILES structure")
            mol = Chem.MolFromSmiles(structure)
        
        if mol:
            print(f"DEBUG: Structure parsed successfully. Atom count: {mol.GetNumAtoms()}")
        else:
            print("DEBUG: Failed to parse structure")
        
        result = ""
        if mol:
            # Generate PNG image
            print("DEBUG: Generating PNG image")
            img = Draw.MolToImage(mol, size=(width, height))
            print("DEBUG: PNG image generated")
            
            # Convert to base64
            import io
            buffer = io.BytesIO()
            img.save(buffer, format="PNG")
            result = base64.b64encode(buffer.getvalue()).decode('utf-8')
            print(f"DEBUG: PNG base64 result length: {len(result)} characters")
        else:
            print("DEBUG: No valid molecule to convert to PNG")
        
        print(f"DEBUG: Final result length: {len(result)} characters")
        
        with open(sys.argv[2], 'w') as f:
            f.write(result)
        
        print("DEBUG: Result written to output file")
      PYTHON
      
      # Prepare input data
      input_data = {
        "structure" => structure,
        "width" => width,
        "height" => height
      }
      
      result = Jchem.run_rdkit(script, input_data.to_json)
      puts "Ruby: PNG generation completed. Result length: #{result.length} characters"
      result
    rescue Exception => e
      puts "Ruby: Error generating PNG: #{e.message}"
      puts e.backtrace.join("\n")
      # logger.error "Could not generate PNG - invalid structure"
      nil
    end
  end

  def self.inchikey(structure)
    # A standard inchikey is generated with the options:
    # inchikey:AuxNone,Woff,SAbs
    begin
      puts "Ruby: Starting InChIKey generation"
      
      script = <<~PYTHON
        import sys
        from rdkit import Chem
        from rdkit.Chem import AllChem
        
        print("DEBUG: Starting InChIKey generation")
        
        with open(sys.argv[1], 'r') as f:
            structure = f.read().strip()
        
        print(f"DEBUG: Input structure: {structure[:20]}...")
        
        # Parse the input structure
        mol = None
        if structure.startswith('InChI='):
            print("DEBUG: Parsing InChI structure")
            mol = Chem.MolFromInchi(structure)
        else:
            print("DEBUG: Parsing SMILES structure")
            mol = Chem.MolFromSmiles(structure)
        
        if mol:
            print(f"DEBUG: Structure parsed successfully. Atom count: {mol.GetNumAtoms()}")
        else:
            print("DEBUG: Failed to parse structure")
        
        result = ""
        if mol:
            print("DEBUG: Generating InChI")
            inchi = AllChem.MolToInchi(mol)
            print(f"DEBUG: InChI: {inchi[:30]}...")
            print("DEBUG: Generating InChIKey")
            inchikey = AllChem.InchiToInchiKey(inchi)
            result = inchikey
            print(f"DEBUG: InChIKey: {result}")
        
        with open(sys.argv[2], 'w') as f:
            f.write(result)
        
        print("DEBUG: Result written to output file")
      PYTHON
      
      result = Jchem.run_rdkit(script, structure)
      puts "Ruby: InChIKey generation completed. Result: #{result}"
      # Remove the "InChIKey=" prefix if present
      result.sub('InChIKey=', '')
    rescue Exception => e
      puts "Ruby: Error generating InChIKey: #{e.message}"
      # logger.error "Could not grab inchikey - invalid structure"
      nil
    end
  end

  def self.inchi(structure)
    # A standard inchi is generated with the options:
    # inchi:AuxNone,Woff,SAbs
    begin
      puts "Ruby: Starting InChI generation"
      
      script = <<~PYTHON
        import sys
        from rdkit import Chem
        from rdkit.Chem import AllChem
        
        print("DEBUG: Starting InChI generation")
        
        with open(sys.argv[1], 'r') as f:
            structure = f.read().strip()
        
        print(f"DEBUG: Input structure: {structure[:20]}...")
        
        # Parse the input structure
        mol = None
        if structure.startswith('InChI='):
            print("DEBUG: Parsing InChI structure")
            mol = Chem.MolFromInchi(structure)
        else:
            print("DEBUG: Parsing SMILES structure")
            mol = Chem.MolFromSmiles(structure)
        
        if mol:
            print(f"DEBUG: Structure parsed successfully. Atom count: {mol.GetNumAtoms()}")
        else:
            print("DEBUG: Failed to parse structure")
        
        result = ""
        if mol:
            print("DEBUG: Generating InChI")
            result = AllChem.MolToInchi(mol)
            print(f"DEBUG: InChI result: {result[:30]}...")
        
        with open(sys.argv[2], 'w') as f:
            f.write(result)
        
        print("DEBUG: Result written to output file")
      PYTHON
      
      result = Jchem.run_rdkit(script, structure)
      puts "Ruby: InChI generation completed. Result length: #{result.length} characters"
      result.strip
    rescue Exception => e
      puts "Ruby: Error generating InChI: #{e.message}"
      # logger.error "Could not grab inchi - invalid structure"
      nil
    end
  end
  
  # Add a new method for SMILES conversion
  def self.smiles(structure)
    begin
      puts "Ruby: Starting SMILES conversion"
      
      script = <<~PYTHON
        import sys
        from rdkit import Chem
        
        print("DEBUG: Starting SMILES conversion")
        
        with open(sys.argv[1], 'r') as f:
            structure = f.read().strip()
        
        print(f"DEBUG: Input structure: {structure[:20]}...")
        
        # Parse the input structure
        mol = None
        if structure.startswith('InChI='):
            print("DEBUG: Parsing InChI structure")
            mol = Chem.MolFromInchi(structure)
        else:
            print("DEBUG: Parsing SMILES structure")
            mol = Chem.MolFromSmiles(structure)
        
        if mol:
            print(f"DEBUG: Structure parsed successfully. Atom count: {mol.GetNumAtoms()}")
        else:
            print("DEBUG: Failed to parse structure")
        
        result = ""
        if mol:
            print("DEBUG: Converting to SMILES")
            result = Chem.MolToSmiles(mol, isomericSmiles=True)
            print(f"DEBUG: SMILES result: {result}")
        
        with open(sys.argv[2], 'w') as f:
            f.write(result)
        
        print("DEBUG: Result written to output file")
      PYTHON
      
      result = Jchem.run_rdkit(script, structure)
      puts "Ruby: SMILES conversion completed. Result: #{result}"
      result.strip
    rescue Exception => e
      puts "Ruby: Error converting to SMILES: #{e.message}"
      # logger.error "Could not convert to SMILES - invalid structure"
      nil
    end
  end
  
  # Add a method for 3D structure generation
  def self.generate_3d(structure)
    begin
      puts "Ruby: Starting 3D structure generation"
      
      script = <<~PYTHON
        import sys
        from rdkit import Chem
        from rdkit.Chem import AllChem
        
        print("DEBUG: Starting 3D structure generation")
        
        with open(sys.argv[1], 'r') as f:
            structure = f.read().strip()
        
        print(f"DEBUG: Input structure: {structure[:20]}...")
        
        # Parse the input structure
        mol = None
        if structure.startswith('InChI='):
            print("DEBUG: Parsing InChI structure")
            mol = Chem.MolFromInchi(structure)
        else:
            print("DEBUG: Parsing SMILES structure")
            mol = Chem.MolFromSmiles(structure)
        
        if mol:
            print(f"DEBUG: Structure parsed successfully. Atom count: {mol.GetNumAtoms()}")
        else:
            print("DEBUG: Failed to parse structure")
        
        result = ""
        if mol:
            # Add hydrogens
            print("DEBUG: Adding hydrogens")
            mol_with_h = Chem.AddHs(mol)
            print(f"DEBUG: Added hydrogens. New atom count: {mol_with_h.GetNumAtoms()}")
            
            # Generate 3D coordinates
            print("DEBUG: Generating 3D coordinates")
            embed_result = AllChem.EmbedMolecule(mol_with_h)
            print(f"DEBUG: Embed result: {embed_result}")
            
            # Optimize the structure
            print("DEBUG: Optimizing 3D structure")
            optimize_result = AllChem.MMFFOptimizeMolecule(mol_with_h)
            print(f"DEBUG: Optimization result: {optimize_result}")
            
            # Convert to MOL format
            print("DEBUG: Converting to MOL format")
            result = Chem.MolToMolBlock(mol_with_h)
            print(f"DEBUG: MOL result length: {len(result)} characters")
        
        with open(sys.argv[2], 'w') as f:
            f.write(result)
        
        print("DEBUG: Result written to output file")
      PYTHON
      
      result = Jchem.run_rdkit(script, structure)
      puts "Ruby: 3D structure generation completed. Result length: #{result.length} characters"
      result
    rescue Exception => e
      puts "Ruby: Error generating 3D structure: #{e.message}"
      puts e.backtrace.join("\n")
      # logger.error "Could not generate 3D structure"
      nil
    end
  end
end
