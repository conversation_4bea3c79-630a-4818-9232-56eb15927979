require 'tempfile'
require 'json'
require 'base64'

module <PERSON><PERSON><PERSON>
  # Logger helper
  def self.log(message)
    if defined?(Rails) && Rails.logger
      Rails.logger.info "[RDKit-Jchem] #{message}"
    else
      puts "[RDKit-Jchem] #{message}"
    end
  end

  # Execute RDKit Python script
  def self.run_rdkit_script(script, structure)
    log("Running RDKit script for structure: #{structure}")

    begin
      result = `python3 -c "#{script}" "#{structure}" 2>/dev/null`.strip
      log("RDKit script result: #{result}")

      # Try to parse as JSON, fallback to raw string
      begin
        parsed_result = JSON.parse(result)
        parsed_result == "null" ? nil : parsed_result
      rescue JSON::ParserError
        result.empty? ? nil : result
      end
    rescue => e
      log("Error running RDKit script: #{e.message}")
      nil
    end
  end

  # Mock response class to maintain compatibility
  class MockResponse
    attr_reader :code, :body

    def initialize(body, code = "200")
      @body = body
      @code = code
    end
  end

  # Compatibility methods - these are no longer used but kept for backward compatibility
  def self.get(path, options = {})
    log("GET request (RDKit mode): path=#{path}, options=#{options}")
    # Return a mock successful response
    MockResponse.new("RDKit mode - GET not implemented", "200")
  end

  def self.post(path, body)
    log("POST request (RDKit mode): path=#{path}, body=#{body.inspect}")
    # Return a mock successful response
    MockResponse.new("RDKit mode - POST not implemented", "200")
  end

  # Convert structure to InChI using RDKit
  def self.structure_to_inchi(structure)
    log("Converting structure to InChI: #{structure}")

    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem

      try:
          # Try to parse as SMILES first
          mol = Chem.MolFromSmiles(sys.argv[1])
          if mol is None:
              # Try to parse as MOL block
              mol = Chem.MolFromMolBlock(sys.argv[1])
          if mol is None:
              # Try to parse as InChI
              mol = Chem.MolFromInchi(sys.argv[1])

          if mol is None:
              print("null")
          else:
              result = Chem.MolToInchi(mol)
              print(json.dumps(result))
      except Exception as e:
          print("null")
    PYTHON

    result = run_rdkit_script(script, structure)
    log("InChI conversion result: #{result}")
    result
  end

  # Convert structure to InChIKey using RDKit
  def self.structure_to_inchikey(structure)
    log("Converting structure to InChIKey: #{structure}")

    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem

      try:
          # Try to parse as SMILES first
          mol = Chem.MolFromSmiles(sys.argv[1])
          if mol is None:
              # Try to parse as MOL block
              mol = Chem.MolFromMolBlock(sys.argv[1])
          if mol is None:
              # Try to parse as InChI
              mol = Chem.MolFromInchi(sys.argv[1])

          if mol is None:
              print("null")
          else:
              result = Chem.MolToInchiKey(mol)
              print(json.dumps(result))
      except Exception as e:
          print("null")
    PYTHON

    result = run_rdkit_script(script, structure)
    log("InChIKey conversion result: #{result}")
    result
  end

  # Convert structure to SMILES using RDKit
  def self.structure_to_smiles(structure)
    log("Converting structure to SMILES: #{structure}")

    script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem

      try:
          # Try to parse as SMILES first
          mol = Chem.MolFromSmiles(sys.argv[1])
          if mol is None:
              # Try to parse as MOL block
              mol = Chem.MolFromMolBlock(sys.argv[1])
          if mol is None:
              # Try to parse as InChI
              mol = Chem.MolFromInchi(sys.argv[1])

          if mol is None:
              print("null")
          else:
              result = Chem.MolToSmiles(mol, canonical=True)
              print(json.dumps(result))
      except Exception as e:
          print("null")
    PYTHON

    result = run_rdkit_script(script, structure)
    log("SMILES conversion result: #{result}")
    result
  end

  # Convert structure to name using RDKit (limited functionality)
  # Note: RDKit doesn't have built-in IUPAC naming, so this returns a placeholder
  def self.structure_to_name(structure)
    log("Converting structure to name: #{structure}")
    log("Warning: RDKit doesn't support IUPAC naming - returning placeholder")

    # For now, return a placeholder since RDKit doesn't have IUPAC naming
    # In a real implementation, you might want to integrate with another service
    # or library that provides chemical naming
    result = "Chemical name not available (RDKit mode)"
    log("Name conversion result: #{result}")
    result
  end

  # Generate JPEG image using RDKit
  def self.structure_to_jpeg(structure)
    log("Converting structure to JPEG: #{structure}")

    script = <<~PYTHON
      import sys
      import json
      import base64
      from rdkit import Chem
      from rdkit.Chem import Draw
      from io import BytesIO

      try:
          # Try to parse as SMILES first
          mol = Chem.MolFromSmiles(sys.argv[1])
          if mol is None:
              # Try to parse as MOL block
              mol = Chem.MolFromMolBlock(sys.argv[1])
          if mol is None:
              # Try to parse as InChI
              mol = Chem.MolFromInchi(sys.argv[1])

          if mol is None:
              print("null")
          else:
              img = Draw.MolToImage(mol, size=(300, 300))
              buffer = BytesIO()
              img.save(buffer, format='JPEG')
              img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
              result = {"binaryStructure": img_data}
              print(json.dumps(result))
      except Exception as e:
          print("null")
    PYTHON

    result = run_rdkit_script(script, structure)
    log("JPEG conversion result: #{result ? 'success' : 'failed'}")
    result || {}
  end

  def self.structure_to_jpeg_file(structure, filename)
    log("Saving structure as JPEG file: #{filename}")
    data = self.structure_to_jpeg(structure)
    if data && data["binaryStructure"]
      File.open(filename, "wb") { |f| f.write(Base64.decode64(data["binaryStructure"])) }
      log("JPEG file saved: #{filename}")
    else
      log("Failed to generate JPEG for file: #{filename}")
    end
  end

  # Generate PNG image using RDKit
  def self.structure_to_png(structure)
    log("Converting structure to PNG: #{structure}")

    script = <<~PYTHON
      import sys
      import json
      import base64
      from rdkit import Chem
      from rdkit.Chem import Draw
      from io import BytesIO

      try:
          # Try to parse as SMILES first
          mol = Chem.MolFromSmiles(sys.argv[1])
          if mol is None:
              # Try to parse as MOL block
              mol = Chem.MolFromMolBlock(sys.argv[1])
          if mol is None:
              # Try to parse as InChI
              mol = Chem.MolFromInchi(sys.argv[1])

          if mol is None:
              print("null")
          else:
              img = Draw.MolToImage(mol, size=(300, 300))
              buffer = BytesIO()
              img.save(buffer, format='PNG')
              img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
              result = {"binaryStructure": img_data}
              print(json.dumps(result))
      except Exception as e:
          print("null")
    PYTHON

    result = run_rdkit_script(script, structure)
    log("PNG conversion result: #{result ? 'success' : 'failed'}")
    result || {}
  end

  def self.structure_to_png_file(structure, filename)
    log("Saving structure as PNG file: #{filename}")
    data = self.structure_to_png(structure)
    if data && data["binaryStructure"]
      File.open(filename, "wb") { |f| f.write(Base64.decode64(data["binaryStructure"])) }
      log("PNG file saved: #{filename}")
    else
      log("Failed to generate PNG for file: #{filename}")
    end
  end


  def self.test
    log("Running RDKit-Jchem test...")

    # Test RDKit functionality with a simple molecule
    test_smiles = "CCO"  # Ethanol

    begin
      # Test SMILES conversion
      smiles_result = structure_to_smiles(test_smiles)
      log("SMILES test result: #{smiles_result}")

      # Test InChI conversion
      inchi_result = structure_to_inchi(test_smiles)
      log("InChI test result: #{inchi_result}")

      # Test InChIKey conversion
      inchikey_result = structure_to_inchikey(test_smiles)
      log("InChIKey test result: #{inchikey_result}")

      if smiles_result && inchi_result && inchikey_result
        log("RDKit-Jchem test completed successfully")
        MockResponse.new("RDKit test passed", "200")
      else
        log("RDKit-Jchem test failed - some conversions returned nil")
        MockResponse.new("RDKit test failed", "500")
      end
    rescue => e
      log("RDKit-Jchem test error: #{e.message}")
      MockResponse.new("RDKit test error: #{e.message}", "500")
    end
  end
end
