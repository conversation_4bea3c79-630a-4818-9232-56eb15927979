require 'net/http'
require 'uri'
require 'json'

module Jchem
  # Location of the jchem server we are using
  SITE = "http://137.184.206.181:8080"

  # Logger helper
  def self.log(message)
    if defined?(Rails) && Rails.logger
      Rails.logger.info "[Jchem] #{message}"
    else
      puts "[Jchem] #{message}"
    end
  end

  # Build the full uri
  def self.full_uri(path)
    uri = URI.join(SITE,"/jchem/",path)
    log("Building URI: SITE=#{SITE}, path=#{path}, result=#{uri}")
    uri
  end

  # For getting requests
  # def self.get(path,*options)
  #   Typhoeus.get(self.full_uri(path),*options)
  # end

  # # For posting requests
  # def self.post(path,body)
  #   Typhoeus.post self.full_uri(path),
  #     headers: { "Content-type" => "application/json" },
  #     body: body.to_json
  # end

  # For getting requests
  def self.get(path, options = {})
    log("GET request: path=#{path}, options=#{options}")

    uri = self.full_uri(path)
    log("Initial URI: #{uri}")

    # Build the full URL with query parameters if needed
    if options.any?
      query_string = URI.encode_www_form(options)
      full_url = "#{uri}?#{query_string}"
      log("Adding query parameters: #{query_string}")
      log("Full URL with query: #{full_url}")
      uri = URI.parse(full_url)
      log("Parsed URI: #{uri}")
    end

    log("Creating GET request for: #{uri}")
    request = Net::HTTP::Get.new(uri)

    log("Making HTTP request to #{uri.hostname}:#{uri.port}")
    response = Net::HTTP.start(uri.hostname, uri.port) do |http|
      http.request(request)
    end

    log("Response code: #{response.code}, body length: #{response.body&.length || 0}")
    response
  end

  # For posting requests
  def self.post(path, body)
    log("POST request: path=#{path}")
    log("POST body: #{body.inspect}")

    uri = self.full_uri(path)
    log("POST URI: #{uri}")

    request = Net::HTTP::Post.new(uri, 'Content-Type' => 'application/json')
    json_body = body.to_json
    log("JSON body: #{json_body}")
    request.body = json_body

    log("Making POST request to #{uri.hostname}:#{uri.port}")
    response = Net::HTTP.start(uri.hostname, uri.port) do |http|
      http.request(request)
    end

    log("POST response code: #{response.code}, body length: #{response.body&.length || 0}")
    log("POST response body: #{response.body}")
    response
  end

  # Example:
  #
  # Jchem.structure_to_inchi("InChI=1S/C3H8O2/c1-2-3-5-4/h4H,2-3H2,1H3\nAuxInfo=1/0/N:1,2,3,5,4/rA:5CCCOO/rB:s1;s2;s3;s4;/rC:4.62,-2.6674,0;3.85,-1.3337,0;2.31,-1.3337,0;1.54,0,0;;\n")
  # #=> "CCCOO"
  #
  def self.structure_to_inchi(structure)
    log("Converting structure to InChI: #{structure}")
    response = self.post( "rest-v0/util/calculate/stringMolExport",
       { structure: structure, parameters: "inchi"}  )
    log("InChI conversion result: #{response.body}")
    response.body
  end

  # Example:
  #
  # Jchem.structure_to_inchikey("InChI=1S/C3H8O2/c1-2-3-5-4/h4H,2-3H2,1H3\nAuxInfo=1/0/N:1,2,3,5,4/rA:5CCCOO/rB:s1;s2;s3;s4;/rC:4.62,-2.6674,0;3.85,-1.3337,0;2.31,-1.3337,0;1.54,0,0;;\n")
  # #=> "InChIKey=TURGQPDWYFJEDY-UHFFFAOYSA-N"
  #
  def self.structure_to_inchikey(structure)
    log("Converting structure to InChIKey: #{structure}")
    response = self.post( "rest-v0/util/calculate/stringMolExport",
       { structure: structure, parameters: "inchikey"}  )
    log("InChIKey conversion result: #{response.body}")
    response.body
  end

  # Example:
  #
  # Jchem.structure_to_inchi("CCCOO")
  # #=> "InChI=1S/C3H8O2/c1-2-3-5-4/h4H,2-3H2,1H3\nAuxInfo=1/0/N:1,2,3,5,4/rA:5CCCOO/rB:s1;s2;s3;s4;/rC:4.62,-2.6674,0;3.85,-1.3337,0;2.31,-1.3337,0;1.54,0,0;;\n"
  #
  def self.structure_to_smiles(structure)
    log("Converting structure to SMILES: #{structure}")
    response = self.post( "rest-v0/util/calculate/stringMolExport",
       { structure: structure, parameters: "smiles"}  )
    log("SMILES conversion result: #{response.body}")
    response.body
  end

  # Example:
  #
  # Jchem.structure_to_name("CCCOO")
  # #=> "propane-1-peroxol"
  #
  def self.structure_to_name(structure)
    response = self.post( "rest-v0/util/calculate/stringMolExport",
       { structure: structure, parameters: "name"}  )
    response.body
  end

  # Example
  #
  # Jchem.structure_to_jpeg("CCCOO")["binaryStructure"]
  # #=> image data string
  #
  def self.structure_to_jpeg(structure)
    response = self.post( "rest-v0/util/calculate/molExport",
       { structure: structure, parameters: "jpeg"}  )
    JSON.parse(response.body)
  end

  def self.structure_to_jpeg_file(structure,filename)
    data = self.structure_to_jpeg(structure)["binaryStructure"]
    File.open(filename,"wb" ){|f| f.puts Base64.decode64(data)}
  end

  # Use in the same way as structure_to_jpeg
  def self.structure_to_png(structure)
    response = self.post( "rest-v0/util/calculate/molExport",
       { structure: structure, parameters: "jpeg"}  )
    JSON.parse(response.body)
  end

  def self.structure_to_png_file(structure,filename)
    data = self.structure_to_png(structure)["binaryStructure"]
    File.open(filename,"wb" ){|f| f.puts Base64.decode64(data)}
  end


  def self.test
    log("Running Jchem test...")
    result = self.get("rest-v0", verbose: true)
    log("Test completed with response code: #{result.code}")
    result
  end
end
