module Jchem
  # Location of the jchem server we are using
#  SITE = "http://jchem.wishartlab.com/"
  SITE = "http://***************:8080"
  
  # Use python-rdkit for chemical structure conversions
  def self.run_rdkit(script, input_data)
    require 'tempfile'
    
    # Create temporary files for input and output
    input_file = Tempfile.new(['rdkit_input', '.txt'])
    input_file.write(input_data)
    input_file.close
    
    output_file = Tempfile.new(['rdkit_output', '.txt'])
    output_file.close
    
    # Run the Python script with RDKit
    system("python3", "-c", script, input_file.path, output_file.path)
    
    # Read the result
    result = File.read(output_file.path)
    
    # Clean up temporary files
    input_file.unlink
    output_file.unlink
    
    result.strip
  end

  # Example:
  #
  # Jchem.structure_to_inchi("InChI=1S/C3H8O2/c1-2-3-5-4/h4H,2-3H2,1H3\nAuxInfo=1/0/N:1,2,3,5,4/rA:5CCCOO/rB:s1;s2;s3;s4;/rC:4.62,-2.6674,0;3.85,-1.3337,0;2.31,-1.3337,0;1.54,0,0;;\n")
  # #=> "CCCOO"
  #
  def self.structure_to_inchi(structure)
    script = <<~PYTHON
      import sys
      from rdkit import Chem
      from rdkit.Chem import AllChem
      
      with open(sys.argv[1], 'r') as f:
          structure = f.read().strip()
      
      # Handle different input formats
      mol = None
      if structure.startswith('InChI='):
          mol = Chem.MolFromInchi(structure)
      else:
          mol = Chem.MolFromSmiles(structure)
      
      if mol:
          inchi = AllChem.MolToInchi(mol)
          with open(sys.argv[2], 'w') as f:
              f.write(inchi)
      else:
          with open(sys.argv[2], 'w') as f:
              f.write("")
    PYTHON
    puts "Running RDKit script for structure to InChI conversion"
    run_rdkit(script, structure)
  end

  # Example:
  #
  # Jchem.structure_to_inchikey("InChI=1S/C3H8O2/c1-2-3-5-4/h4H,2-3H2,1H3\nAuxInfo=1/0/N:1,2,3,5,4/rA:5CCCOO/rB:s1;s2;s3;s4;/rC:4.62,-2.6674,0;3.85,-1.3337,0;2.31,-1.3337,0;1.54,0,0;;\n")
  # #=> "InChIKey=TURGQPDWYFJEDY-UHFFFAOYSA-N"
  #
  def self.structure_to_inchikey(structure)
    script = <<~PYTHON
      import sys
      from rdkit import Chem
      from rdkit.Chem import AllChem
      
      with open(sys.argv[1], 'r') as f:
          structure = f.read().strip()
      
      # Handle different input formats
      mol = None
      if structure.startswith('InChI='):
          mol = Chem.MolFromInchi(structure)
      else:
          mol = Chem.MolFromSmiles(structure)
      
      if mol:
          inchi = AllChem.MolToInchi(mol)
          inchikey = AllChem.InchiToInchiKey(inchi)
          with open(sys.argv[2], 'w') as f:
              f.write(inchikey)
      else:
          with open(sys.argv[2], 'w') as f:
              f.write("")
    PYTHON
    
    run_rdkit(script, structure)
  end

  # Example:
  #
  # Jchem.structure_to_smiles("CCCOO")
  # #=> "CCCOO"
  #
  def self.structure_to_smiles(structure)
    script = <<~PYTHON
      import sys
      from rdkit import Chem
      
      with open(sys.argv[1], 'r') as f:
          structure = f.read().strip()
      
      # Handle different input formats
      mol = None
      if structure.startswith('InChI='):
          mol = Chem.MolFromInchi(structure)
      else:
          mol = Chem.MolFromSmiles(structure)
      
      if mol:
          smiles = Chem.MolToSmiles(mol)
          with open(sys.argv[2], 'w') as f:
              f.write(smiles)
      else:
          with open(sys.argv[2], 'w') as f:
              f.write("")
    PYTHON
    
    run_rdkit(script, structure)
  end

  # Example:
  #
  # Jchem.structure_to_name("CCCOO")
  # #=> "propane-1-peroxol"
  #
  def self.structure_to_name(structure)
    # Note: RDKit doesn't have built-in naming capabilities like JChem
    # You might need to use a different service or library for this
    script = <<~PYTHON
      import sys
      from rdkit import Chem
      
      # This is a placeholder - RDKit doesn't have built-in naming
      # You might need to use a different service or library
      with open(sys.argv[1], 'r') as f:
          structure = f.read().strip()
      
      with open(sys.argv[2], 'w') as f:
          f.write("Name not available with RDKit")
    PYTHON
    
    run_rdkit(script, structure)
  end

  # Example
  #
  # Jchem.structure_to_jpeg("CCCOO")["binaryStructure"]
  # #=> image data string
  #
  def self.structure_to_jpeg(structure)
    script = <<~PYTHON
      import sys
      import json
      import base64
      from rdkit import Chem
      from rdkit.Chem import Draw
      
      with open(sys.argv[1], 'r') as f:
          structure = f.read().strip()
      
      # Handle different input formats
      mol = None
      if structure.startswith('InChI='):
          mol = Chem.MolFromInchi(structure)
      else:
          mol = Chem.MolFromSmiles(structure)
      
      if mol:
          img = Draw.MolToImage(mol)
          import io
          buffer = io.BytesIO()
          img.save(buffer, format="JPEG")
          img_str = base64.b64encode(buffer.getvalue()).decode()
          
          result = {"binaryStructure": img_str}
          with open(sys.argv[2], 'w') as f:
              f.write(json.dumps(result))
      else:
          with open(sys.argv[2], 'w') as f:
              f.write(json.dumps({"binaryStructure": ""}))
    PYTHON
    
    result = run_rdkit(script, structure)
    JSON.parse(result)
  end

  def self.structure_to_jpeg_file(structure, filename)
    data = self.structure_to_jpeg(structure)["binaryStructure"]
    File.open(filename, "wb") {|f| f.puts Base64.decode64(data)}
  end

  # Use in the same way as structure_to_jpeg
  def self.structure_to_png(structure)
    script = <<~PYTHON
      import sys
      import json
      import base64
      from rdkit import Chem
      from rdkit.Chem import Draw
      
      with open(sys.argv[1], 'r') as f:
          structure = f.read().strip()
      
      # Handle different input formats
      mol = None
      if structure.startswith('InChI='):
          mol = Chem.MolFromInchi(structure)
      else:
          mol = Chem.MolFromSmiles(structure)
      
      if mol:
          img = Draw.MolToImage(mol)
          import io
          buffer = io.BytesIO()
          img.save(buffer, format="PNG")
          img_str = base64.b64encode(buffer.getvalue()).decode()
          
          result = {"binaryStructure": img_str}
          with open(sys.argv[2], 'w') as f:
              f.write(json.dumps(result))
      else:
          with open(sys.argv[2], 'w') as f:
              f.write(json.dumps({"binaryStructure": ""}))
    PYTHON
    
    result = run_rdkit(script, structure)
    JSON.parse(result)
  end

  def self.structure_to_png_file(structure, filename)
    data = self.structure_to_png(structure)["binaryStructure"]
    File.open(filename, "wb") {|f| f.puts Base64.decode64(data)}
  end

  def self.test
    self.structure_to_smiles("CCCOO")
  end
end
