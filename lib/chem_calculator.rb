module ChemCalculator
  require 'tempfile'
  require 'json'

  # Use RDKit via Python for chemical calculations
  def self.run_rdkit_calculation(structure, calculation_type)
    python_script = case calculation_type
    when 'molecular_weight'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import Descriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Descriptors.MolWt(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'exact_mass'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import Descriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Descriptors.ExactMolWt(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'formula'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import rdMolDescriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = rdMolDescriptors.CalcMolFormula(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'logp'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import Descriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Descriptors.MolLogP(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'acceptor_count'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import Descriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Descriptors.NumHAcceptors(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'donor_count'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import Descriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Descriptors.NumHDonors(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'atom_count'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = mol.GetNumAtoms()
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'rotatable_bond_count'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import Descriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Descriptors.NumRotatableBonds(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'polar_surface_area'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import Descriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Descriptors.TPSA(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'refractivity'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import Descriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Descriptors.BertzCT(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'formal_charge'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Chem.rdmolops.GetFormalCharge(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'ring_count'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import Descriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Descriptors.RingCount(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'bond_count'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = mol.GetNumBonds()
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    when 'fused_aromatic_ring_count'
      <<~PYTHON
        import sys
        import json
        from rdkit import Chem
        from rdkit.Chem import Descriptors

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                result = Descriptors.NumAromaticRings(mol)
                print(json.dumps(result))
        except Exception as e:
            print("null")
      PYTHON
    else
      raise ArgumentError, "Unknown calculation type: #{calculation_type}"
    end

    # Execute Python script with RDKit
    result = `python3 -c "#{python_script}" "#{structure}" 2>/dev/null`.strip

    # Parse JSON result, return nil if calculation failed
    begin
      parsed_result = JSON.parse(result)
      parsed_result == "null" ? nil : parsed_result
    rescue JSON::ParserError
      nil
    end
  end

  # Legacy method for compatibility with existing JChem term syntax
  def self.term(structure, term)
    case term
    when 'mass()'
      molecular_weight(structure)
    when 'exactMass()'
      exact_mass(structure)
    when 'formula()'
      formula(structure)
    when 'logp()', 'logP()'
      logp(structure)
    when 'acceptorCount()'
      acceptor_count(structure)
    when 'donorCount()'
      donor_count(structure)
    when 'atomCount()'
      atom_count(structure)
    when 'rotatableBondCount()'
      rotatable_bond_count(structure)
    when 'PSA()'
      polar_surface_area(structure)
    when 'refrac()'
      refractivity(structure)
    when 'formalCharge()'
      formal_charge(structure)
    when 'ringCount()'
      ring_count(structure)
    when 'bondCount()'
      bond_count(structure)
    when 'fusedAromaticRingCount()'
      fused_aromatic_ring_count(structure)
    else
      # For unknown terms, return nil to maintain compatibility
      nil
    end
  end

  def self.molecular_weight(structure)
    run_rdkit_calculation(structure, 'molecular_weight')
  end

  def self.exact_mass(structure)
    run_rdkit_calculation(structure, 'exact_mass')
  end

  def self.formula(structure)
    run_rdkit_calculation(structure, 'formula')
  end

  def self.logp(structure)
    run_rdkit_calculation(structure, 'logp')
  end

  def self.acceptor_count(structure)
    run_rdkit_calculation(structure, 'acceptor_count')
  end

  def self.donor_count(structure)
    run_rdkit_calculation(structure, 'donor_count')
  end

  def self.atom_count(structure)
    run_rdkit_calculation(structure, 'atom_count')
  end

  def self.rotatable_bond_count(structure)
    run_rdkit_calculation(structure, 'rotatable_bond_count')
  end

  def self.polar_surface_area(structure)
    run_rdkit_calculation(structure, 'polar_surface_area')
  end

  def self.refractivity(structure)
    run_rdkit_calculation(structure, 'refractivity')
  end

  def self.formal_charge(structure)
    run_rdkit_calculation(structure, 'formal_charge')
  end

  def self.ring_count(structure)
    run_rdkit_calculation(structure, 'ring_count')
  end

  def self.bond_count(structure)
    run_rdkit_calculation(structure, 'bond_count')
  end

  def self.fused_aromatic_ring_count(structure)
    run_rdkit_calculation(structure, 'fused_aromatic_ring_count')
  end
end
