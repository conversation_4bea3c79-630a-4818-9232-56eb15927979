module ChemClient
  require 'tempfile'
  require 'json'
  require 'digest'

  # In-memory structure storage to replace JChem database
  @@structure_store = {}
  @@next_id = 1

  def self.create_structure(structure)
    begin
      # Validate and standardize structure using RDKit
      standardized_structure = standardize_structure(structure)
      return nil if standardized_structure.nil?

      # Generate a unique ID for this structure
      structure_id = @@next_id
      @@next_id += 1

      # Store the structure
      @@structure_store[structure_id] = {
        'cd_id' => structure_id,
        'cd_structure' => {
          'structureData' => {
            'structure' => standardized_structure
          }
        },
        'original_structure' => structure
      }

      puts "Structure inserted successfully with ID #{structure_id}"
      structure_id
    rescue => e
      puts "Error creating structure: #{e.message}"
      nil
    end
  end

  def self.update_structure(structure_id, structure)
    begin
      return false unless @@structure_store.key?(structure_id)

      # Validate and standardize structure using RDKit
      standardized_structure = standardize_structure(structure)
      return false if standardized_structure.nil?

      # Update the stored structure
      @@structure_store[structure_id]['cd_structure']['structureData']['structure'] = standardized_structure
      @@structure_store[structure_id]['original_structure'] = structure

      puts "Structure #{structure_id} updated successfully"
      true
    rescue => e
      puts "Error updating structure: #{e.message}"
      false
    end
  end

  def self.delete_structure(structure_id)
    begin
      if @@structure_store.key?(structure_id)
        @@structure_store.delete(structure_id)
        puts "Structure #{structure_id} deleted successfully"
        true
      else
        puts "Structure #{structure_id} not found"
        false
      end
    rescue => e
      puts "Error deleting structure: #{e.message}"
      false
    end
  end

  def self.get_structure(structure_id, included_fields:[], additional_fields:{})
    begin
      return nil unless @@structure_store.key?(structure_id)

      stored_data = @@structure_store[structure_id]
      entry = ActiveSupport::HashWithIndifferentAccess.new

      # Basic structure data
      entry[:id] = stored_data['cd_id']
      entry[:structure] = stored_data['cd_structure']['structureData']['structure']

      # Handle included fields
      included_fields.each do |field|
        case field
        when 'cd_id'
          entry[field.sub(/cd_/, '')] = stored_data['cd_id']
        when 'cd_structure'
          entry[field.sub(/cd_/, '')] = stored_data['cd_structure']
        else
          # For other fields, we might need to calculate them
          entry[field.sub(/cd_/, '')] = nil
        end
      end

      # Handle additional fields (chemical calculations)
      additional_fields.each do |field, term|
        begin
          # Use ChemCalculator to compute the requested chemical term
          value = ChemCalculator.term(entry[:structure], term)
          entry[field] = value
        rescue => e
          puts "Error calculating #{field}: #{e.message}"
          entry[field] = nil
        end
      end

      # Handle special cases
      entry['atom_count'] = nil if entry['atom_count'].to_i < 0

      entry
    rescue => e
      puts "Error getting structure: #{e.message}"
      nil
    end
  end

  def self.get_image(structure_id, width: 200, height: 200)
    begin
      structure_data = self.get_structure(structure_id)
      return nil if structure_data.nil?

      structure = structure_data[:structure]

      # Generate image using RDKit
      python_script = <<~PYTHON
        import sys
        import json
        import base64
        from rdkit import Chem
        from rdkit.Chem import Draw
        from io import BytesIO

        try:
            mol = Chem.MolFromSmiles(sys.argv[1])
            if mol is None:
                print("null")
            else:
                img = Draw.MolToImage(mol, size=(#{width}, #{height}))
                buffer = BytesIO()
                img.save(buffer, format='PNG')
                img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
                print(json.dumps(img_data))
        except Exception as e:
            print("null")
      PYTHON

      result = `python3 -c "#{python_script}" "#{structure}" 2>/dev/null`.strip

      begin
        parsed_result = JSON.parse(result)
        parsed_result == "null" ? nil : parsed_result
      rescue JSON::ParserError
        nil
      end
    rescue => e
      puts "Error generating image: #{e.message}"
      nil
    end
  end

  # Initialize the RDKit-based structure storage
  def self.initialize
    begin
      # Clear any existing structures
      @@structure_store.clear
      @@next_id = 1

      # Test RDKit functionality by creating a simple structure
      test_id = self.create_structure('CCC')
      if test_id.present?
        puts "RDKit ChemClient initialized successfully"
        self.delete_structure(test_id)
        true
      else
        raise 'Could not create test structure during initialization!'
      end
    rescue => e
      puts "Error initializing ChemClient: #{e.message}"
      raise 'Could not initialize RDKit ChemClient!'
    end
  end

  # Upgrade method - now just validates RDKit installation
  def self.upgrade
    begin
      # Test that RDKit is available
      python_script = <<~PYTHON
        import sys
        from rdkit import Chem
        print("RDKit available")
      PYTHON

      result = `python3 -c "#{python_script}" 2>/dev/null`.strip
      if result.include?("RDKit available")
        puts "RDKit upgrade check passed"
        true
      else
        raise "RDKit not available"
      end
    rescue => e
      puts "Error during upgrade check: #{e.message}"
      false
    end
  end

  private

  # Standardize structure using RDKit
  def self.standardize_structure(structure)
    python_script = <<~PYTHON
      import sys
      import json
      from rdkit import Chem

      try:
          # Try to parse as SMILES first
          mol = Chem.MolFromSmiles(sys.argv[1])
          if mol is None:
              # Try to parse as MOL block
              mol = Chem.MolFromMolBlock(sys.argv[1])
          if mol is None:
              # Try to parse as InChI
              mol = Chem.MolFromInchi(sys.argv[1])

          if mol is None:
              print("null")
          else:
              # Return canonical SMILES
              result = Chem.MolToSmiles(mol, canonical=True)
              print(json.dumps(result))
      except Exception as e:
          print("null")
    PYTHON

    result = `python3 -c "#{python_script}" "#{structure}" 2>/dev/null`.strip

    begin
      parsed_result = JSON.parse(result)
      parsed_result == "null" ? nil : parsed_result
    rescue JSON::ParserError
      nil
    end
  end
end