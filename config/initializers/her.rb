# require 'her'

# MOLDB_API = Her::API.new
# MOLDB_API.setup url: Moldbi.config.server do |c|
#   # Request
#   c.use Faraday::Request::UrlEncoded
#   c.use Her::Middleware::AcceptJSON
#   c.use Faraday::Request::BasicAuthentication,
#     Moldbi.config.username,
#     Moldbi.config.password

#   # Response
#   c.use Her::Middleware::DefaultParseJSON

#   # Adapter
#   c.use Faraday::Adapter::NetHttp
# end
require 'her'

# Custom Faraday logger for more detailed output
class VerboseFaradayLogger < Faraday::Response::Logger
  def on_request(env)
    Rails.logger.info "[FARADAY REQUEST] #{env.method.upcase} #{env.url}"
    Rails.logger.info "[FARADAY REQUEST HEADERS] #{env.request_headers}"
    Rails.logger.info "[FARADAY REQUEST BODY] #{env.body}" if env.body
    super
  end

  def on_complete(env)
    Rails.logger.info "[FARADAY RESPONSE] #{env.status} #{env.reason_phrase}"
    Rails.logger.info "[FARADAY RESPONSE HEADERS] #{env.response_headers}"
    Rails.logger.info "[FARADAY RESPONSE BODY] #{env.body}" if env.body
    super
  end
end

MOLDB_API = Her::API.new
MOLDB_API.setup url: Moldbi.config.server do |c|
  # Request
  c.use Faraday::Request::UrlEncoded
  c.use Her::Middleware::AcceptJSON
  c.use Faraday::Request::BasicAuthentication,
    Moldbi.config.username,
    Moldbi.config.password

  # Enhanced logging for debugging
  c.use Faraday::Response::Logger, Rails.logger, {
    bodies: true,           # Include request/response bodies
    headers: true,          # Include headers
    log_level: :debug       # Set log level explicitly
  }

  # Response
  c.use Her::Middleware::DefaultParseJSON

  # Adapter
  c.use Faraday::Adapter::NetHttp
end
