# require 'her'

# MOLDB_API = Her::API.new
# MOLDB_API.setup url: Moldbi.config.server do |c|
#   # Request
#   c.use Faraday::Request::UrlEncoded
#   c.use Her::Middleware::AcceptJSON
#   c.use Faraday::Request::BasicAuthentication,
#     Moldbi.config.username,
#     Moldbi.config.password

#   # Response
#   c.use Her::Middleware::DefaultParseJSON

#   # Adapter
#   c.use Faraday::Adapter::NetHttp
# end
require 'her'

MOLDB_API = Her::API.new
MOLDB_API.setup url: Moldbi.config.server do |c|
  # Request
  c.use Faraday::Request::UrlEncoded
  c.use Her::Middleware::AcceptJSON
  c.use Faraday::Request::BasicAuthentication,
    Moldbi.config.username,
    Moldbi.config.password

  # Logging the requests and responses
  c.use Faraday::Response::Logger, Rails.logger, { bodies: true }  # Log to <PERSON>s logger and include bodies in the logs

  # Response
  c.use Her::Middleware::DefaultParseJSON

  # Adapter
  c.use Faraday::Adapter::NetHttp
end
