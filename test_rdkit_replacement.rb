#!/usr/bin/env ruby

# Test script to verify RDKit replacement functionality
require_relative 'lib/chem_calculator'
require_relative 'lib/chem_client'

puts "Testing RDKit-based ChemCalculator and ChemClient..."

# Test ChemCalculator
puts "\n=== Testing ChemCalculator ==="
test_smiles = "CCO"  # Ethanol

puts "Testing molecular weight calculation..."
mw = ChemCalculator.molecular_weight(test_smiles)
puts "Molecular weight of #{test_smiles}: #{mw}"

puts "Testing exact mass calculation..."
em = ChemCalculator.exact_mass(test_smiles)
puts "Exact mass of #{test_smiles}: #{em}"

puts "Testing formula calculation..."
formula = ChemCalculator.formula(test_smiles)
puts "Formula of #{test_smiles}: #{formula}"

puts "Testing logP calculation..."
logp = ChemCalculator.logp(test_smiles)
puts "LogP of #{test_smiles}: #{logp}"

puts "Testing atom count calculation..."
atom_count = ChemCalculator.atom_count(test_smiles)
puts "Atom count of #{test_smiles}: #{atom_count}"

# Test ChemClient
puts "\n=== Testing ChemClient ==="

puts "Initializing ChemClient..."
ChemClient.initialize

puts "Creating structure..."
structure_id = ChemClient.create_structure(test_smiles)
puts "Created structure with ID: #{structure_id}"

if structure_id
  puts "Getting structure..."
  structure_data = ChemClient.get_structure(structure_id)
  puts "Retrieved structure: #{structure_data}"

  puts "Getting structure with additional fields..."
  structure_with_props = ChemClient.get_structure(structure_id, 
    additional_fields: { 
      'molecular_weight' => 'mass()',
      'formula' => 'formula()',
      'logp' => 'logp()'
    })
  puts "Structure with properties: #{structure_with_props}"

  puts "Updating structure..."
  updated = ChemClient.update_structure(structure_id, "CCCO")  # Propanol
  puts "Update successful: #{updated}"

  puts "Getting updated structure..."
  updated_structure = ChemClient.get_structure(structure_id)
  puts "Updated structure: #{updated_structure}"

  puts "Deleting structure..."
  deleted = ChemClient.delete_structure(structure_id)
  puts "Delete successful: #{deleted}"

  puts "Verifying deletion..."
  deleted_structure = ChemClient.get_structure(structure_id)
  puts "Structure after deletion: #{deleted_structure.nil? ? 'nil (correctly deleted)' : 'still exists'}"
end

puts "\n=== Test completed ==="
