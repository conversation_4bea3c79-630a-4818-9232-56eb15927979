class Carbonpred < ActiveRecord::Base


  def self.DrawMol(smiles,backend_dir,nmr_pred_dir,draw_mol_script_basename,nmr_pred,session_directory)

    draw_mol_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{draw_mol_script_basename}")
    
    # draw_mol_script=Rails.root.join("#{backend_dir}", "#{backend_dir}", "#{nmr_pred_dir}","#{draw_mol_script_basename}")
    
    session_name="#{nmr_pred.id}"+"_draw_mol_#{nmr_pred.user_id}"

    draw_mol_log_basename="#{session_name}_draw_mol.log"
    outputprefix="#{session_name}_utility_H2O_prediction"
    outputprefix_canonical="#{session_name}_draw_mol_canonical"
    draw_mol_log_abs_path=Rails.root.join("#{session_directory}",draw_mol_log_basename)
    draw_mol_log_abs_path_canonical=Rails.root.join("#{session_directory}","canonical_#{draw_mol_log_basename}")
    

    draw_mol_command=""
    draw_mol_command+="#{PYTHON_ENV["#{Rails.env}"]['python_path']} "
    draw_mol_command+="#{draw_mol_script} "

    draw_mol_arguments=""
    draw_mol_arguments+="--smiles '#{smiles}' "
    draw_mol_arguments+="--outputpath '#{session_directory}' "
    draw_mol_arguments+="--writemol "
    draw_mol_arguments+="--optmol "
    #draw_mol_arguments+="--showstereo "
    #draw_mol_arguments+="--showequiv "
    draw_mol_arguments+="--smilesorder "

    draw_mol_arguments_non_canonical=draw_mol_arguments
    draw_mol_arguments_non_canonical+="--outputprefix '#{outputprefix}' "
    draw_mol_arguments_non_canonical+=" > '#{draw_mol_log_abs_path}' "
    draw_mol_command_non_canonical=draw_mol_command
    draw_mol_command_non_canonical+="#{draw_mol_arguments_non_canonical} "
    Rails.logger.debug "draw_mol_command_non_canonical: #{draw_mol_command_non_canonical}"
    `#{draw_mol_command_non_canonical}`

    draw_mol_arguments_canonical=draw_mol_arguments
    draw_mol_arguments_canonical+="--canonicalorder "
    draw_mol_arguments_canonical+="--outputprefix '#{outputprefix_canonical}' "
    draw_mol_arguments_canonical+=" > '#{draw_mol_log_abs_path_canonical}' "
    draw_mol_command_canonical=draw_mol_command
    draw_mol_command_canonical+="#{draw_mol_arguments_canonical} "
    Rails.logger.debug "draw_mol_command_canonical: #{draw_mol_command_canonical}"
    # bll: these output files appear to not be currently used,
    #      and we want to avoid running the command twice.
    #      there is still ongoing discussion on the best/easiest way to get predictable numbering,
    #      so the output could also change
    # `#{draw_mol_command_canonical}`

    non_canonical_mol_basename="#{outputprefix}_output.mol"
    non_canonical_base_image_basename="#{outputprefix}_2d.png"
    non_canonical_equiv_image_basename="#{outputprefix}_equiv.png"

    canonical_mol_basename="#{outputprefix_canonical}_output.mol"
    canonical_base_image_basename="#{outputprefix_canonical}_2d.png"
    canonical_equiv_image_basename="#{outputprefix_canonical}_equiv.png"


    non_canonical_mol_abs_path=Rails.root.join("#{session_directory}",non_canonical_mol_basename)
    non_canonical_base_image_abs_path=Rails.root.join("#{session_directory}",non_canonical_base_image_basename)
    non_canonical_equiv_image_abs_path=Rails.root.join("#{session_directory}",non_canonical_equiv_image_basename)

    canonical_mol_abs_path=Rails.root.join("#{session_directory}",canonical_mol_basename)
    canonical_base_image_abs_path=Rails.root.join("#{session_directory}",canonical_base_image_basename)
    canonical_equiv_image_abs_path=Rails.root.join("#{session_directory}",canonical_equiv_image_basename)

    

    drawmolrdkit_script=Rails.root.join('public', 'python','drawmolrdkit.py')
    output_dir_for_mol=session_directory
    python_log_basename="#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path=Rails.root.join("#{session_directory}",python_log_basename)

    input_mol_file = "#{session_name}_draw_mol_output.mol"
    path_to_input_mol_file = Rails.root.join("#{session_directory}","#{input_mol_file}")

    temp_model_basename="#{session_name}_temp_3D.mol"
    temp_image_basename="#{session_name}_temp_3D.png"
    csv_basename="#{session_name}_mol.csv"

    path_to_temp_model=Rails.root.join("#{session_directory}","#{temp_model_basename}")
    path_to_temp_image=Rails.root.join("#{session_directory}","#{temp_image_basename}")
    path_to_csv=Rails.root.join("#{session_directory}","#{csv_basename}")
    nmr_pred_location=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}")
    # nmr_pred_location=Rails.root.join("#{backend_dir}", "#{backend_dir}", "#{nmr_pred_dir}")

    
    python_drawmolrdkit_command=""
    python_drawmolrdkit_command+="#{@PY_PATH} "
    python_drawmolrdkit_command+="#{drawmolrdkit_script} "
    python_drawmolrdkit_arguments=""
    python_drawmolrdkit_arguments+="-s '#{smiles}' "
    python_drawmolrdkit_arguments+="-sid '#{session_name}' "
    python_drawmolrdkit_arguments+="-odir_path '#{session_directory}' "
    python_drawmolrdkit_arguments+="-mol_path '#{path_to_temp_model}' "
    python_drawmolrdkit_arguments+="-cs_path '#{path_to_csv}' "
    python_drawmolrdkit_arguments+="-mol_img_path '#{path_to_temp_image}' "
    python_drawmolrdkit_arguments+="-nmrpred_loc '#{nmr_pred_location}' "
    python_drawmolrdkit_arguments+="-input_mol '#{path_to_input_mol_file}' " #USE THIS AS INPUT TO NMRPRED
    python_drawmolrdkit_arguments+="-input_img '#{non_canonical_base_image_abs_path}' " #COPY THIS AND USE AS DISPLAY
    python_drawmolrdkit_arguments+=" > '#{python_log_abs_path}' "
    python_drawmolrdkit_command+="#{python_drawmolrdkit_arguments} "

    Rails.logger.debug "Python log is:"
    Rails.logger.debug "#{python_log_abs_path}"
    Rails.logger.debug "Running drawmolrdkit Python script:"
    Rails.logger.debug "#{python_drawmolrdkit_command}"

    `#{python_drawmolrdkit_command}`

    return "#{session_directory}/#{outputprefix}_output.mol"
  end


  def self.run_nmrpred_intialprediction(input_mol_path, session_directory, session_name, predictor='nmrshiftdb')

    #
    # This uses a 3d mol to make intial nmrshiftdb prediction with nmrpred
    # Returns full paths
    #

    Rails.logger.debug "running nmrpred (nmrshiftdb) for intial prediction)"
    Rails.logger.debug "input mol file: #{input_mol_path}"
    Rails.logger.debug "output folder: #{session_directory}"
    Rails.logger.debug "output prefix: #{session_name}"

    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    nmrpred_script=Rails.root.join("backend", "nmr-pred", "nmrpred.py")
    Rails.logger.debug "nmrpred_script = #{nmrpred_script}"

    python_log_basename="#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path=Rails.root.join("#{session_directory}",python_log_basename)

    nmrpred_command="#{python_path} "
    nmrpred_command+="#{nmrpred_script} "

    nmrpred_command+="--mol #{input_mol_path} "
    nmrpred_command+="--outputpath #{session_directory} "
    nmrpred_command+="--outputprefix #{session_name} "
    nmrpred_command+="--writeassignmenttable "
    nmrpred_command+="--write1h  "
    nmrpred_command+="--write1hcoup "
    nmrpred_command+="--write13c "
    nmrpred_command+="--shiftpredc #{predictor} "
    nmrpred_command+=">>  #{python_log_abs_path} "

    Rails.logger.debug "nmrpred_command = #{nmrpred_command}"
    `#{nmrpred_command}`

    nmrpred_assignment=Rails.root.join("#{session_directory}","#{session_name}_13c_shifts.txt")
    Rails.logger.debug "nmrpred_assignment = #{nmrpred_assignment}"

    #cs_dictionary=parse_nmrpred_assignment(nmrpred_assignment)
    return nmrpred_assignment

  end

  def self.PredictionResult(predict_csv1)
    # Rails.logger.debug("predict_csv1 = #{predict_csv1}")
    # temp1 = predict_csv1.split("/")[-1].split("\"")[0]
    # predict_csv = File.join("/Users","zinatsayeeda","zach","nmrpred","created_files","#{temp1}") 
    # predict_csv = File.join("/apps","nmrpred","created_files","#{temp1}") 
    chemical_shift = []
    hydrogen_position = []
    lines = []
    @c_p = [[]]
    CSV.open(predict_csv1, 'r', :col_sep => "\t", :quote_char => "|").each do |row|
    # File.open(predict_csv, 'r') do |row|
      lines << row
    end
    print("lines = #{lines}")
   
    lines.each do |line|
      temp_line = line
      Rails.logger.debug("temp_line = #{temp_line}")
      Rails.logger.debug("temp_line type = #{temp_line.class}")
      chemical_shift << temp_line[0].split(",")[1]
      print("each time chemical_shift = #{chemical_shift}")
      hydrogen_position << (temp_line[0].split(",")[0].split(".")[0])
      print("each time hydrogen_position = #{hydrogen_position}")
    end

    #save data in table:which
    #n_p = NmrPred.find(nmr_pred_id)
    for j in 0..(chemical_shift.length)
      print("chemical_shift[j]=#{chemical_shift[j]}")
      print("hydrogen_position[j]=#{hydrogen_position[j]}")
      @c_p << [chemical_shift[j],hydrogen_position[j]]
      print("#{@c_p}")
    #  # if j >= 3
    #  #   n_p = NmrPred.create(:user_session_id => "#{@user_session_id}", :nucleus => "1H", :solvent => "H2O")
    #  #   n_p.save
    #  # end
    #  # n_p.atom_id = "#{hydrogen_position[j]}" #start from 0
    #  # n_p.shift_pred = "#{chemical_shift[j]}" #start from 0
    #  # n_patom_symbol = "H"

    end

    print("final c_p = #{@c_p}")

    # c_p.delete_at(0)

    #  print("after deletion final c_p = #{c_p}")
    
    return @c_p
  end

end