class User < ActiveRecord::Base
	has_many :external_submissions
	has_many :natural_products, through: :external_submissions

	acts_as_authentic do |c|
     c.login_field = :email
   	end
   	
	def deliver_password_reset_instructions!
		reset_perishable_token!
		Notifier.deliver_password_reset_instructions(self).deliver
	end
 
 	def deliver_verification_instructions!
	    reset_perishable_token!
	    Notifier.account_verification_instructions(self).deliver
 	end
  
	def verify!
		self.verified = true
		self.save
	end

	# Method to check if a given np_mrd_id exists for the user's natural products
  # Returns the natural product if it exists, or nil if it doesn't
  def find_natural_product_by_np_mrd_id(np_mrd_id)
		unless admin
    	natural_products.find_by(np_mrd_id: np_mrd_id)
		else
			NaturalProduct.find_by(np_mrd_id: np_mrd_id)
		end
  end
end
