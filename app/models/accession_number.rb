class AccessionNumber < ActiveRecord::Base
  belongs_to :element, polymorphic: true

  default_scope { order('number ASC') }

  validates :element, presence: true
  validates :element_type, presence: true
  validates_presence_of :number
  validates_format_of :number, with: /\A(?:NP)\d{7}\z/,
                               allow_blank: true,
                               if: "self.element_type == 'NaturalProduct'"
  validates_uniqueness_of :number, scope: :element_type

  scope :for_natural_products, -> { where(element_type: 'NaturalProduct') }

  def to_s
    self.number
  end
end
