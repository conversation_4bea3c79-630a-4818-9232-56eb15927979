class Structure < ApplicationRecord
  include Structures::Formats
  include Structures::Properties
  include Structures::StructureResource
  include ChemConvert
  include ChemStandardize
  include ChemCalculator
  acts_as_taggable

  
  has_many :database_registrations, dependent: :restrict_with_error
  with_options dependent: :restrict_with_error do |spectrum_assoc|
    spectrum_assoc.has_many :ms_ms
    spectrum_assoc.has_many :c_ms
    spectrum_assoc.has_many :ei_ms
    spectrum_assoc.has_many :nmr_one_d
    spectrum_assoc.has_many :nmr_two_d
  end

  has_many :bmrb_records, primary_key: :inchikey, foreign_key: :inchikey
  has_many :adducts, dependent: :destroy
  # has_many :database_registrations, dependent: :restrict_with_error
  has_many :registration_histories, dependent: :restrict_with_error
  has_many :sources, through: :database_registrations

  has_many :properties, -> { order('name, source') }, dependent: :delete_all
  has_one :exact_mass_property, -> { where(name: 'exact_mass') }, class_name: "Property"

  has_many :formats, -> { order(:name) }, dependent: :delete_all
  has_one :mol_format, -> { where(name: 'mol_text') }, class_name: "Format"
  has_one :mol_3d_format, -> {where(name: '3d_mol_text') }, class_name: "Format"
  has_one :pdb_format, -> { where(name: 'pdb_text') }, class_name: "Format"

  has_one :curation, dependent: :destroy
  has_many :synonyms, dependent: :destroy

  has_many :subset

  validates :original_structure, presence: true
  validates :inchikey,
    presence: true,
    uniqueness: true,
    length: { is: 27 }
  validates :formula, presence: true, length: { maximum: 100 }
  validates :molecular_weight,
    numericality: { greater_than: 0 },
    allow_blank: true
  validates :exact_mass,
    numericality: { greater_than: 0 },
    allow_blank: true

  before_validation :set_inchikey, :set_basic_properties, on: :create
  after_save :refresh
  after_create :wrangle, :add_adducts
  alias_attribute :structure, :original_structure

  attr_accessor :similarity

  def self.from_param!(id)
    self.from_param(id) || raise(ActiveRecord::RecordNotFound)
  end

  def self.from_param(id)
    if id.to_s.inchikey?
      self.find_by(inchikey: id)
    else
      DatabaseRegistration.find_by(database_id: id).try!(:structure)
    end
  end

  def self.from_database_id(id)
    DatabaseRegistration.find_by(database_id: id).try!(:structure)
  end

  def self.from_inchikey(key)
    self.from_param(key.to_s.sub(/InChIKey=/, '').strip)
  end

  def self.from_inchikey!(key)
    self.from_inchikey(key) || raise(ActiveRecord::RecordNotFound)
  end

  def to_param
    self.inchikey
  end

  def inchi
    self.formats.find_by(name: 'inchi').try(:value)
  end

  def smiles
    self.formats.find_by(name: 'smiles').try(:value)
  end

  def name
    self.properties.find_by(name: 'iupac').try(:value)
  end

  def traditional_name
    self.properties.find_by(name: 'traditional_iupac').try(:value)
  end

  def mol
    self.mol_format.try(:value)
  end

  def mol_3d
    self.mol_3d_format.try(:value)
  end

  def pdb
    self.pdb_format.try(:value)
  end

  def sdf
    formats = {}
    self.formats.each { |f| formats[f.name.to_sym] = f.value }
    props = {}
    self.properties.each { |p| props["#{p.source}_#{p.name}".to_sym] = p.value }

    Sdf.new(formats[:mol_text]).generate do |sdf|
      sdf.tag('SMILES', formats[:smiles])
      sdf.tag('INCHI_IDENTIFIER', formats[:inchi])
      sdf.tag('INCHI_KEY', inchikey)
      sdf.tag('FORMULA', formula)
      sdf.tag('MOLECULAR_WEIGHT', molecular_weight.to_s)
      sdf.tag('EXACT_MASS', exact_mass.to_s)
      props.each do |prop, value|
        sdf.tag(prop.to_s.upcase, value)
      end
    end
  end

  # Replace the properties and formats for this structure, regardless of whether
  # the structure has changed
  def refresh!
    self.refresh(true)
  end

  # Replace the properties and formats for this structure if the structure
  # has changed
  def refresh(force=false)
    self.update_formats!
    Rails.logger.info("Refreshing properties for #{self.id}")
    TermCalculatorJob.perform_later(self.id)
    Rails.logger.info("Refreshing adducts for #{self.id}")
    AdductJob.perform_later(self.id)
    self
  end

  def wrangle
    # Run Data-Wrangler on the new structure
    CurationJob.perform_later(self.id, false)
  end

  def add_adducts
    AdductJob.perform_later(self.id)
  end

  # Update the original structure by standardizing using the JChem standardizer.
  # The format is the JChem standardize simple string
  # see: http://www.chemaxon.com/jchem/doc/user/StandardizerConfiguration.html#actionstring
  def standardize!(format=nil)
    standardizer = ChemStandardize.new
    standardized_mol = standardizer.standardize(self.structure, nil, format)
    self.structure = standardized_mol
    self.save!
  end

  # Return all associated spectra
  def spectra
    spectra = Spectrum.all_spectra_types.map do |klass|
      self.send(klass.to_s.tableize)
    end.flatten
    SpectrumCollection.new(spectra)
  end

  def bust_cache
    database_registrations.pluck(:database_id).each do |database_id|
      Rails.cache.delete_matched("*#{database_id}*")
    end
    Rails.cache.delete_matched("*#{self.to_param}*")
  end

  # Return an array of synonyms info that combines the data for synonyms from (1) the
  # curations table and (2) the synonyms table in MolDB. Each array element is a hash with
  # the following fields:
  #   name - name of the synonym
  #   source - database or tool from which the synonym was obtained
  #   kind
  #   occurrence - Used in the curations table to count how many times the synonym was seen among sources DataWrangler used.
  #
  # Input parameter synonyms_from_curation: Array of synonyms info from the curations
  # table for the current structure, where each element in the array is a hash with the
  # same fields as mentioned just above.
  def get_combined_synonyms(synonyms_from_curation)

    # Put the synonyms from the curations table into combined_synonyms
    combined_synonyms = Array.new
    synonyms_from_curation.each { |curation_synonym|
      combined_synonyms.push(curation_synonym)
    }

    # Check the synonyms for the current structure from the synonyms table against those
    # from the curations table, and add any that are missing.
    synonyms_from_synonyms_table = self.synonyms
    synonyms_from_synonyms_table.each { |synonym|

      match = false
      synonym_name = synonym.name
      sources = synonym.sources.pluck(:human_name)
      if synonym.exclude
        combined_synonyms.reject!{|s| s["name"] == synonym_name}
        next
      end

      synonyms_from_curation.each { |curation_synonym|
        curation_synonym_name = curation_synonym['name']
        if synonym_name.downcase == curation_synonym_name.downcase
          match = true
          break
        end
      }
      if !match
        combined_synonyms.push({
          'name' => synonym_name,
          'source' => sources.join(", "),
          'kind' => 'Synonym',
          'occurrence' => 1
          })
      end
    }

    combined_synonyms
  end

  private

  def set_inchikey
    if self.inchikey.blank?
      self.inchikey = ChemConvert.inchikey(self.structure)
    end
  end

  def set_basic_properties
    [:formula, :molecular_weight, :exact_mass].each do |property|
      if self.original_structure_changed? || self.send(property).blank?
        self.send("#{property}=", ChemCalculator.send(property, self.structure))
      end
    end
  end
end
