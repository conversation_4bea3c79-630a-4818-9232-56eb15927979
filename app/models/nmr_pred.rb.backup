class NmrPred < ActiveRecord::Base
  require 'csv'
  require 'fileutils'
  

  belongs_to :natural_product
  has_attached_file :sdf_file 
  do_not_validate_attachment_file_type :sdf_file
  


  def self.FeatureCreation(molecule_name_with_path,out_dir,mol_prefix)
    python_testdataset_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    puts("python path = #{python_testdataset_path}")
    testdataset_script=Rails.root.join('backend', 'nmr-pred', 'NmrPred', 'get_descriptor', 'TestDataset.py')
    puts("test dataset script = #{testdataset_script}")
    python_log_basename="#{mol_prefix}_nmrpred_H2O_prediction_testdataset.log"
    puts("python log base name for testdataset = #{python_log_basename}")
    python_log_abs_path= "#{out_dir}/"+ "#{python_log_basename}"
    puts("test dataset log path = #{python_log_abs_path}")
    script_path = Rails.root.join('backend', 'nmr-pred', 'NmrPred', 'get_descriptor')
  
  
    python_testdataset_command=""
    python_testdataset_command+="#{python_testdataset_path} "
    python_testdataset_command+="#{testdataset_script} "
    python_testdataset_arguments=""
    python_testdataset_arguments+="-mol_prefix '#{mol_prefix}' " 
    python_testdataset_arguments+="-odir_path '#{out_dir}' "
    python_testdataset_arguments+="-mol_path '#{molecule_name_with_path}' "
    python_testdataset_arguments+="-python_path '#{python_testdataset_path}' "
    python_testdataset_arguments+="-script_path '#{script_path}' "
      
    python_testdataset_arguments+=" > '#{python_log_abs_path}' "
    python_testdataset_command+="#{python_testdataset_arguments} "
  
    puts "Running TestDataset.py script:"
    puts "#{python_testdataset_command}"
  
    `#{python_testdataset_command}`
    
  end

  def self.PredictionShift(train_file_with_path,test_file_with_path,out_put_dir_path,mol_prefix_name)

    print("entered into prediction")
    python_path = PYTHON_ENV["#{Rails.env}"]['python_path']
    main2_script_with_path = Rails.root.join('backend', 'nmr-pred', 'NmrPred', 'get_descriptor','main2.py')
    pred_script_with_path = Rails.root.join('backend', 'nmr-pred', 'NmrPred', 'get_descriptor','prediction_chiral.py')
    pred_path = Rails.root.join('backend', 'nmr-pred', 'NmrPred', 'get_descriptor')
    pred_cmd = "#{python_path}"+" #{main2_script_with_path} -odir_path #{out_put_dir_path} -train_file_with_path #{train_file_with_path}"
    pred_cmd = pred_cmd+" -test_file_with_path #{test_file_with_path} -py_path #{python_path} -mol_prefix #{mol_prefix_name}"
    pred_cmd = pred_cmd+" -pred_script_with_path #{pred_script_with_path} -pred_path #{pred_path}"+" >  #{out_put_dir_path}/#{mol_prefix_name}_H2O_prediction_main2_log.txt"

    puts "Running NmrPred script:"
    puts "#{pred_cmd}"
    Dir.chdir(pred_path)
    puts("current path =")
    system("pwd")
    `#{pred_cmd}`
    puts"H2O NmrPred command done"
    
  end


  def self.PredictionResult(predict_csv,user_session_id,nmr_pred_id)
    puts("predict_csv = #{predict_csv}")
    chemical_shift = []
    hydrogen_position = []
    lines = []
    @c_p = [[]]
    CSV.open(predict_csv, 'r', :col_sep => "\t", :quote_char => "|").each do |row|
      lines << row
    end
    print("lines = #{lines}")
   
    lines.each do |line|
      temp_line = line
      chemical_shift << temp_line[1]
      # print("each time chemical_shift = #{chemical_shift}")
      hydrogen_position << (temp_line[0].to_i)+1
      # print("each time hydrogen_position = #{hydrogen_position}")
    end

    #save data in table:
    n_p = NmrPred.find(nmr_pred_id)
    for j in 2..(chemical_shift.length)
      print("chemical_shift[j]=#{chemical_shift[j]}")
      print("hydrogen_position[j]=#{hydrogen_position[j]}")
      @c_p << [chemical_shift[j],hydrogen_position[j]]
      print("#{@c_p}")
      if j >= 3
        n_p = NmrPred.create(:user_session_id => "#{@user_session_id}", :nucleus => "1H", :solvent => "H2O")
        n_p.save
      end
      n_p.atom_id = "#{hydrogen_position[j]}" #start from 0
      n_p.shift_pred = "#{chemical_shift[j]}" #start from 0
      n_patom_symbol = "H"

    end

    print("final c_p = #{@c_p}")

    # c_p.delete_at(0)

    #  print("after deletion final c_p = #{c_p}")
    
    return @c_p
 end
end


