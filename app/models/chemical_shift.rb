class ChemicalShift < ActiveRecord::Base
  belongs_to :chemical_shift_submission
  belongs_to :submission

  def self.conditional_save(s)
    s.chemical_shifts.each do |a|
      a.update(:chemical_shift_true => "NA") if a.chemical_shift_true.blank?
      a.update(:multiplet_true => "#{a.multiplet_pred}") if a.multiplet_true.blank?
      a.update(:jcoupling_true => "NA") if a.jcoupling_true.blank?
      # if a.custom_atom_id.empty?
      #   a.update(:custom_atom_id => "#{a.atom_id}")
      # end
    end
  end
end
