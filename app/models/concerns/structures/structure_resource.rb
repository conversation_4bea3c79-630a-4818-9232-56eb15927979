module Structures
  module StructureResource
    extend ActiveSupport::Concern
    include ChemClient
    included do
      attr_writer :structure_resource
      attr_writer :force_structure_update
      after_validation :save_structure_resource
      after_destroy :delete_structure_resource
    end

    # Load the structure resource using the structure id. If it can't be found,
    # raise an exception
    def structure_resource
      return nil unless self.persisted? # Unsaved records shouldn't have a structure resource
      return @structure_resource if @structure_resource.present?
      # Load the structure resource using the API
      @structure_resource = ChemClient.get_structure(self.jchem_id) # Cache it
      if @structure_resource.blank?
        raise(ActiveRecord::RecordNotFound, 'structure resource record not found')
        @structure_resource.errors.append([ActiveRecord::RecordNotFound, 'structure resource record not found'])
      end
      @structure_resource
    end

    def force_structure_update?
      self.force_structure_update
    end

    def force_structure_update
      if @force_structure_update.nil?
        @force_structure_update = false
      end
      @force_structure_update
    end

    private

    # Save the structure to the database if it is valid, raise an
    # exception if an error occurs
    def save_structure_resource
      if self.original_structure_changed? || self.force_structure_update?
      #   # Update the structure on the JChem server
        if self.jchem_id.present? && ChemClient.get_structure(self.jchem_id).present?
          # Structure already exists, so update it
          ChemClient.update_structure(self.jchem_id, self.original_structure)
        else # Structure is new, so create it and set the structure ID
          console.log("trying to make jchem structure but we don't care")
          # self.jchem_id = ChemClient.create_structure(self.original_structure)
          # if self.jchem_id.blank?
          #   raise(ActiveRecord::RecordNotSaved, 'could not save structure resource')
          #   @structure_resource.errors.append([ActiveRecord::RecordNotFound, 'structure resource record not found'])
          # end
        end
        self.structure_resource = ChemClient.get_structure(self.jchem_id)
        # Double check to make sure it was actually saved
        # if self.original_structure != self.structure_resource.original_structure
        #   raise(ActiveRecord::RecordNotSaved, 'could not save structure resource')
        # end
      end
    end

    # Delete the structure resource from the server
    def delete_structure_resource
      ChemClient.delete_structure(self.jchem_id)

      if ChemClient.get_structure(self.jchem_id).present? # Ensure it was deleted
        raise(ActiveRecord::RecordNotDestroyed, 'structure resource not deleted')
        @structure_resource.errors.append([ActiveRecord::RecordNotFound, 'structure resource record not found'])
      end
    end
  end
end
