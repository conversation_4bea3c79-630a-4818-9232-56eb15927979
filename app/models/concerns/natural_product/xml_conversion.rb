class NaturalProduct
  module XmlConversion
    extend ActiveSupport::Concern
    include XmlConversionHelper

    included do
      alias_method :old_to_xml, :to_xml
      alias_method :to_xml, :complete_xml
    end

    def complete_xml
      Rails.cache.fetch([self, 'xml']) do
        old_to_xml(only: []) do |xml|
          generate_xml(xml)
        end
      end
    end

    def generate_xml(xml)
      xml.version Rails.application.config.version
      xml.creation_date created_at
      xml.update_date updated_at
      xml.accession np_mrd_id
      xml.secondary_accessions do
        accession_numbers.each do |acc|
          xml.accession acc
        end
      end
      xml.name name
      xml.description description
      xml.synonyms do
        if synonymified?
          synonymify["synonyms"].each do |syn|
            xml.synonym syn["name"]
          end
        end
      end
      xml.chemical_formula chemical_formula
      xml.average_molecular_weight average_mass
      xml.monisotopic_molecular_weight mono_mass
      xml.iupac_name iupac
      xml.traditional_iupac traditional_iupac
      xml.cas_registry_number cas
      xml.smiles smiles
      xml.inchi inchi
      xml.inchikey inchikey
      xml.taxonomy do
        if classyfired?
          xml.description classyfirecation.description
          xml.direct_parent classyfirecation.direct_parent_name
          xml.kingdom classyfirecation.kingdom_name
          xml.super_class classyfirecation.superklass_name
          xml.class classyfirecation.klass_name
          xml.sub_class classyfirecation.subklass_name
          xml.molecular_framework classyfirecation.molecular_framework
          xml.alternative_parents do
            classyfirecation.alternative_parent_names.each do |name|
              xml.alternative_parent name
            end
          end
          xml.substituents do
            classyfirecation.substituent_names.each do |s|
              xml.substituent s
            end
          end
          xml.external_descriptors do
            classyfirecation.external_descriptor_annotations.each do |ed|
              xml.external_descriptor ed
            end
          end
        end
      end

      xml.experimental_properties do
        [:water_solubility, :logp, :melting_point, :boiling_point].each do |field|
          if send("experimental_#{field}").present?
            xml.property do
              xml.kind field.to_s
              xml.value send("experimental_#{field}")
              xml.source send("experimental_#{field}_reference")
            end
          end
        end
      end

      xml.predicted_properties do
        [:logp, :logs, :solubility].each do |field|
          if send("moldb_alogps_#{field}").present?
            xml.property do
              xml.kind field.to_s
              xml.value send("moldb_alogps_#{field}")
              xml.source 'ALOGPS'
            end
          end
        end

        [:logp, :pka_strongest_acidic, :pka_strongest_basic, :iupac,
         :average_mass, :mono_mass, :smiles, :formula, :inchi,
         :inchikey, :polar_surface_area, :refractivity, :polarizability,
         :rotatable_bond_count, :acceptor_count, :donor_count,
         :physiological_charge, :formal_charge, :number_of_rings, :bioavailability,
         :rule_of_five, :ghose_filter, :veber_rule, :mddr_like_rule].each do |field|
          if send("moldb_#{field}").present?
            xml.property do
              xml.kind field.to_s
              if field.in? [:logp, :pka_strongest_acidic, :pka_strongest_basic, :polar_surface_area, :polarizability, :refractivity]
                xml.value send("moldb_#{field}").to_f.smart_round_to_s
              elsif field.in? [:rule_of_five, :ghose_filter, :veber_rule, :mddr_like_rule]
                xml.value send("moldb_#{field}") ? "Yes" : "No"
              else
                xml.value send("moldb_#{field}")
              end
              xml.source 'ChemAxon'
            end
          end
        end
      end

      if identifired?
        identifire["identifiers"].each do |id|
          if id[0] == 'drugbank_id'
            xml.drugbank_id id[1]
          elsif id[0] == 'phenol_id'
            xml.phenol_explorer_compound_id id[1]
          elsif id[0] == 'foodb_id'
            xml.foodb_id id[1]
          elsif id[0] == 'knapsack_id'
            xml.knapsack_id id[1]
          elsif id[0] == 'chemspider_id'
            xml.chemspider_id id[1]
          elsif id[0] == 'kegg_id'
            xml.kegg_id id[1]
          elsif id[0] == 'meta_cyc_id'
            xml.biocyc_id id[1]
          elsif id[0] == 'bigg_id'
            xml.bigg_id id[1]
          elsif id[0] == 'wikipedia_id'
            xml.wikipedia_id id[1]
          elsif id[0] == 'metlin_id'
            xml.metlin_id id[1]
          elsif id[0] == 'pubchem_id'
            xml.pubchem_compound_id id[1]
          elsif id[0] == 'pdbe_id'
            xml.pdb_id id[1]
          elsif id[0] == 'chebi_id'
            xml.chebi_id id[1]
          end
        end
      end

      xml.general_references do
        articles.each do |article|
          xml.reference do
            xml.reference_text article.citation
            xml.pubmed_id article.pubmed_id
          end
        end
        textbooks.each do |textbook|
          xml.reference do
            xml.reference_text textbook.title
          end
        end
        external_links.each do |external_link|
          xml.reference do
            xml.reference_text "#{external_link.name}: #{external_link.url}"
          end
        end
      end
    end
  end
end
