class UserSession < Authlogic::Session::Base
  include ActiveModel::Conversion

  def persisted?
    false
  end

  validate :check_if_validated

  private

  def check_if_validated
    errors.add(:base, 'You have not yet verified your account') unless attempted_record&.verified
    unless attempted_record&.verified
      errors.add(:base,
                 "To send verification link again click <a href='/user_verification_email/new'>here</a>".html_safe)
    end
  end
end
