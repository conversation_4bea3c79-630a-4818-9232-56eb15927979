class NaturalProductMixture < ActiveRecord::Base
  LIT_REF_TYPES = ['PMID', 'DOI', 'Book', 'Unpublished / Under Review'].freeze
  auto_strip_attributes :literature_reference, :composition, :description, :source_name, squish:true

  has_one :mixture_submission
  has_many :mixture_nmr_submissions, through: :mixture_submission

  scope :exported, -> {where(exported: 1)}
  scope :not_exported, -> {where(exported: 0)}

  validates :np_mrd_id, uniqueness: true, presence: true,
            format: { with: /\ANPM\d{7}\z/, message: "Must be in the format NPM0000001" },
            if: ->(npm) {npm.exported}

  before_save :evaluate_literature_reference_type
  validates :literature_reference_type, inclusion: { in: LIT_REF_TYPES }

  def to_param
    self.np_mrd_id
  end

  
  # create and assign a unique np-mrd id
  def create_np_mrd_id
    if self.np_mrd_id.blank?
      self.np_mrd_id = NaturalProductMixture.where.not(np_mrd_id: nil).exists? ? "NPM" + NaturalProductMixture.where.not(np_mrd_id: nil).last.np_mrd_id[3..-1].next : 'NPM0000001'
    end
  end

  # Reassign mis-attributed literature reference types (PMID and DOI only)
  def evaluate_literature_reference_type
    case literature_reference
    when /10.\d{4}.+/
      # DOI
      self.literature_reference_type = "DOI"
    else
      # PMID
      is_number = true if Float(literature_reference) rescue false
      if is_number
        self.literature_reference_type = "PMID"
      end
    end
  end

  # Creates,exports and saves the natural product mixture
  def publish
    self.create_np_mrd_id
    self.update(exported: true)
    self.save
  end
end
