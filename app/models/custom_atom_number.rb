class CustomAtomNumber < ActiveRecord::Base
  belongs_to :chemical_shift_submission
  belongs_to :submission

  def self.new_atom_order(c_shift_submission_id)
    cs = ChemicalShiftSubmission.find(c_shift_submission_id.to_i)
    np = NaturalProduct.find((cs.natural_product_id).to_i)
    original_atom_ids = []
    custom_atom_ids = []
    temp_mol_basename_out=cs.id.to_s + "_#{np.np_mrd_id}_" + cs.user_id.to_s + "_temp_3D.mol"
    threeD_mol_url = Rails.root.join("public","downloads",cs.user_session_id.to_s,temp_mol_basename_out.to_s)
    puts("threeD_mol_url = #{threeD_mol_url}")

    stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root}/public/python/number_of_atoms.py '#{threeD_mol_url}'")
    atom_length = stdout.gets(nil).to_s
    puts("atom_length = #{atom_length}")
    # atom_length = 4 ####### python program to fetch original atom length

    original_order = (0..atom_length.to_i-1).to_a
    puts("original_order = #{original_order}")
    cs.custom_atom_numbers.each do |s|
      original_atom_ids.push(s.atom_id)
      puts("original_atom_ids = #{original_atom_ids}")
      custom_atom_ids.push(s.custom_atom_id)
      puts("custom_atom_ids = #{custom_atom_ids}")
    end
    if original_atom_ids == custom_atom_ids
      puts("original ids and custom ids are equal")
      return original_order
    else
       puts("original_order = #{original_order}")
      for i in (0..(original_atom_ids.length-1)) do
        puts("i = #{i}")
        original_atom_ids[i] = original_atom_ids[i].to_i-1
        custom_atom_ids[i] = custom_atom_ids[i].to_i-1
      end
      puts("original_atom_ids = #{original_atom_ids}")
      puts("custom_atom_ids = #{custom_atom_ids}")
      for y in custom_atom_ids do
        index_of_y = custom_atom_ids.index(y)
        x_of_index_of_y = original_atom_ids[index_of_y]
        # index_of_o = original_order.index(x_of_index_of_y)
        # original_order[index_of_o] = y
        original_order[x_of_index_of_y] = y
        #original_order[y] = x_of_index_of_y
      end
      puts("after reordering original_order = #{original_order}")
      return original_order
    end
  end

 def self.new_atom_order_submission(c_shift_submission_id)
    cs = Submission.find(c_shift_submission_id.to_i)
    np = NaturalProduct.find((cs.natural_product_id).to_i)
    original_atom_ids = []
    custom_atom_ids = []
    temp_mol_basename_out=cs.id.to_s + "_#{np.np_mrd_id}_" + cs.user_id.to_s + "_temp_3D.mol"
    threeD_mol_url = Rails.root.join("public","downloads",cs.user_session_id.to_s,temp_mol_basename_out.to_s)
    puts("threeD_mol_url = #{threeD_mol_url}")

    stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root}/public/python/number_of_atoms.py '#{threeD_mol_url}'")
    atom_length = stdout.gets(nil).to_s
    puts("atom_length = #{atom_length}")
    # atom_length = 4 ####### python program to fetch original atom length
    original_order = (0..atom_length.to_i-1).to_a
    puts("original_order = #{original_order}")
    cs.custom_atom_numbers.each do |s|
      original_atom_ids.push(s.atom_id)
      puts("original_atom_ids = #{original_atom_ids}")
      custom_atom_ids.push(s.custom_atom_id)
      puts("custom_atom_ids = #{custom_atom_ids}")
    end
    if original_atom_ids == custom_atom_ids
      puts("original ids and custom ids are equal")
      return original_order
    else
       puts("original_order = #{original_order}")
      for i in (0..(original_atom_ids.length-1)) do
        puts("i = #{i}")
        original_atom_ids[i] = original_atom_ids[i].to_i-1
        custom_atom_ids[i] = custom_atom_ids[i].to_i-1
      end
      puts("original_atom_ids = #{original_atom_ids}")
      puts("custom_atom_ids = #{custom_atom_ids}")
      for y in custom_atom_ids do
        index_of_y = custom_atom_ids.index(y)
        x_of_index_of_y = original_atom_ids[index_of_y]
        # index_of_o = original_order.index(x_of_index_of_y)
        # original_order[index_of_o] = y
        original_order[x_of_index_of_y] = y
        #original_order[y] = x_of_index_of_y
      end
      puts("after reordering original_order = #{original_order}")
      return original_order
    end
  end

  def self.conditional_save(s)
    s.custom_atom_numbers.each do |a|
      a.update(custom_atom_id: a.atom_id.to_s) if a.custom_atom_id.empty?
    end
  end





end
