class ChemicalShiftSubmissionMetaData < ActiveRecord::Base
  include SubmissionsHelper
  LIT_REF_TYPES = ['PMID', 'DOI', 'Book', 'Unpublished / Under Review'].freeze
  auto_strip_attributes :genus, :species, :literature_reference, :temperature, squish: true

  belongs_to :chemical_shift_submission
  # belongs_to :submission

  # before_save :evaluate_literature_reference_type
  # validates :literature_reference_type, inclusion: { in: LIT_REF_TYPES }

  def to_s
    "Expt. #{superscript_spectrum(spectrum_type).split('-')[1]} assignment @#{spectrometer_frequency}, "\
      "#{subscript_solvent(solvent)} solvent (#{superscript_spectrum(spectrum_type)})".html_safe
  end

  def scientific_name
    "#{genus} #{species}"
  end

  private

  # Reassign mis-attributed literature reference types (PMID and DOI only)
  def evaluate_literature_reference_type
    case literature_reference
    when /10.\d{4}.+/
      # DOI
      self.literature_reference_type = "DOI"
    else
      # PMID
      is_number = true if Float(literature_reference) rescue false
      if is_number
        self.literature_reference_type = "PMID"
      end
    end
  end
end
