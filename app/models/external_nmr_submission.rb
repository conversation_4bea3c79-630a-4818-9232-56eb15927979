class ExternalNmrSubmission < ActiveRecord::Base
  belongs_to :external_submission, touch: true
  serialize :peaks, Array

  has_attached_file :fid_file
  has_attached_file :filtered_fid
  has_attached_file :nmrml_file
  has_attached_file :magmet_report
  do_not_validate_attachment_file_type :fid_file
  do_not_validate_attachment_file_type :filtered_fid
  do_not_validate_attachment_file_type :nmrml_file
  do_not_validate_attachment_file_type :magmet_report

  EXPERIMENT_FIELDS = %w[extracted_experiment_folder experiment_type vendor filetype spectrum_uuid].freeze

  scope :exported, -> { where(export: true).order("created_at DESC") }

  def formatted_frequency
    JSON.parse(spectrometer_frequency)[0].to_f.ceil
  end

  def spectrum_type
    "#{nmr_spectrum_type}-#{f1_nucleus.chomp '_'}"
  end

  def db_release_status
    if export
      "released"
    else
      "embargoed"
    end
  end

  def description
    desc = ''
    if f2_nucleus.present?
      f1_nuc =  if f1_nucleus.include? 'C'
                  '<sup>13</sup>C '
                else
                  '<sup>1</sup>H '
                end

      f2_nuc =  if f2_nucleus.include? 'C'
                  '<sup>13</sup>C '
                else
                  '<sup>1</sup>H '
                end
      desc << "[#{f1_nuc}, #{f2_nuc}]-#{nmr_spectrum_type} NMR Spectrum (1D, #{formatted_frequency} MHz, #{solvent}, experimental)"
    else
      desc << if f1_nucleus.include? 'C'
                '<sup>13</sup>C '
              else
                '<sup>1</sup>H '
              end
      desc << " NMR Spectrum (1D, #{formatted_frequency} MHz, #{solvent}, experimental)"
    end

  end

  def extract_peak_centers
    file_path = nmrml_file.path
    centers = []
    
    # Ensure the file exists to avoid reading errors
    return centers unless File.exist?(file_path)
    file_content = File.read(file_path)
    
    # Regular expression to match <multiplet center="xx"> where xx is the value we want to capture
    regex = /<multiplet center="([^"]+)"/
    
    # Use the regex to find all matches in the file content
    file_content.scan(regex) do |match|
      # match[0] contains the captured group, which is the center value in this case
      centers << match[0].to_f # Convert the center value to float
    end
    
    centers.sort.uniq
  rescue => e
    puts "Parsing NmrML Error: #{e.message}"
    []
  end

  def save_peak_centers
    peak_centers = extract_peak_centers
    unless peak_centers.empty?
      self.peaks = peak_centers
      self.save!
    end
  end
end