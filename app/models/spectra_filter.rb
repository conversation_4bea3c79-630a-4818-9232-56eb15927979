# This class is a materialized view that combines one_d_spectra and two_d_spectra
# This model is purely used to filtering in the browse page
# run "CALL refresh_spectra_now(@rc)" to refresh the view in mysql
class SpectraFilter < ActiveRecord::Base

  scope :exported, -> { where(exported: [true, nil]) }

  # Filter by solvent [H2O, D2O, CDCl3, CD3OD, C5H5N, DMSO, C3H6O]
  def self.filter_by_solvent(relation, applicable_filters)
    applicable_filters << :H2O if applicable_filters.include? :Water
    regex_string = applicable_filters.join('|')
    relation.where('solvent REGEXP ?', regex_string)
  end

  # Filter by spectra quality [Excellent, Satisfactory, Poor]
  def self.filter_by_spectra_quality(relation, applicable_filters)
    relation.where('spectra_assessment in (?)', applicable_filters)
  end

  # Filter by assignment quality
  def self.filter_by_assignment_quality(relation, applicable_filters)
    high, low = get_high_low_for_assignment(applicable_filters)
    relation.where('assignment_score BETWEEN ? AND ?', low, high)
  end

  # Filter by spectral type [predicted, simulated, experimental]
  def self.filter_by_spectral_type(relation, applicable_filters)
    case applicable_filters.length
    when 1
      case applicable_filters.first
      when :predicted
        relation.where('predicted = 1')
      when :simulated
        relation.where('simulated = 1')
      when :experimental
        relation.where(
          "spectra_type = 'NmrTwoD' OR "\
        '(predicted IS NULL OR predicted = 0)'\
        ' AND (simulated IS NULL OR simulated = 0)'
        )
      end
    when 2
      type1 = applicable_filters.first
      type2 = applicable_filters.second
      if %i[predicted simulated].include?(type1) && %i[predicted simulated].include?(type2)
        relation.where('predicted = 1 OR simulated = 1')
      elsif %i[predicted experimental].include?(type1) && %i[predicted experimental].include?(type2)
        relation.where(
          'predicted = 1 OR ((simulated IS NULL OR simulated = 0) AND (predicted IS NULL or predicted = 0))'
        )
      elsif %i[simulated experimental].include?(type1) && %i[simulated experimental].include?(type2)
        relation.where(
          'simulated = 1 OR ((simulated IS NULL OR simulated = 0) AND (predicted IS NULL or predicted = 0))'
        )
      end
    else
      relation
    end
  end

  # Filter by nucleus [1H, 13C, [1H, 13C], [1H, 1H], [!3C, 13C]]
  def self.filter_by_nucleus(relation, applicable_filters)
    one_d_proton_query = 'nucleus = "1H"'
    one_d_carbon_query = 'nucleus = "13C"'
    two_d_proton_query = 'nucleus_x = "1H" AND nucleus_y = "1H"'
    two_d_carbon_query = 'nucleus_x = "13C" AND nucleus_y = "13C"'
    hetero_carbon_proton_query = 'nucleus_x = "1H" AND nucleus_y = "13C"'
    nuclei_query_array = []

    # Loop through and add each part of the query
    applicable_filters.each do |nucleus|
      case nucleus
      when :one_d_proton
        nuclei_query_array << one_d_proton_query
      when :one_d_carbon
        nuclei_query_array << one_d_carbon_query
      when :two_d_proton
        nuclei_query_array << two_d_proton_query
      when :two_d_carbon
        nuclei_query_array << two_d_carbon_query
      when :hetero_carbon_proton
        nuclei_query_array << hetero_carbon_proton_query
      end
    end

    # Join the query with OR
    nuclei_query = nuclei_query_array.join(' OR ')
    relation.where(nuclei_query)
  end

  def self.get_high_low_for_assignment(applicable_filters)
    levels = {'level_one': {'high': 100, 'low': 75},
              'level_two': {'high': 74, 'low': 50},
              'level_three': {'high': 49, 'low': 25},
              'level_four': {'high': 24, 'low': 0}}
    low = 100
    high = 0
    if applicable_filters.length == 1
      return levels[applicable_filters.first][:high], levels[applicable_filters.first][:low]
    end
    applicable_filters.each do |level|
      if levels[level][:high] > high
        high = levels[level][:high]
      end

      if levels[level][:low] < low
        low = levels[level][:low]
      end
    end
    [high, low]
  end
end
