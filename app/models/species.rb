class Species < ActiveRecord::Base
  include AltAlias::Create<PERSON>lias
  create_alias 'class', 'class_name'


  has_many :species_mappings, dependent: :destroy
  has_many :natural_products, through: :species_mappings, source: :species_mappable, source_type: "NaturalProduct", dependent: :destroy
  has_many :external_submissions, through: :species_mappings, source: :species_mappable, source_type: "ExternalSubmission", dependent: :destroy

  scope :exported, -> { joins(:natural_products).merge(NaturalProduct.exported).distinct }
  scope :distinct_phylum, -> {select('MIN(id) as id, phylum').group(:phylum)}
  scope :distinct_kingdom, -> {select('MIN(id) as id, kingdom').group(:kingdom)}
  scope :distinct_species, -> {select('MIN(id) as id, scientific_name').group(:scientific_name)}
  scope :distinct_family, -> {select('MIN(id) as id, family').group(:family)}
  scope :distinct_order, -> {select('MIN(id) as id, species.order').group(:order)}

  validates :scientific_name, presence: true, uniqueness: true


  def genus
    scientific_name.split(" ")[0]
  end

  def species
    scientific_name.split(" ")[1]
  end

  def map_kingdom
    mapped_names = {"Viridiplantae" => "Plantae", "Metazoa" => "Animalia"}
    mapped_names[kingdom] || kingdom
  end

end
