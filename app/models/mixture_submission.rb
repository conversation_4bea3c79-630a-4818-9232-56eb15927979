class MixtureSubmission < ActiveRecord::Base

  before_destroy :delete_certificate

  belongs_to :natural_product_mixture
  has_attached_file :certificate_file
  validates_attachment_content_type :certificate_file, :content_type => "application/pdf"
  has_many :mixture_nmr_submissions, dependent: :destroy
  accepts_nested_attributes_for :mixture_nmr_submissions, allow_destroy: true
  attr_accessor :mixture_submission_type, :mixture_certificate_type

  def set_user(current_user,current_user_session)
    self.user_id = current_user.id
    self.user_session_id = current_user_session.id
  end

  def delete_certificate
    self.certificate_file = nil
    self.save
  end

end
