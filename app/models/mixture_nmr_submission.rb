class MixtureNmrSubmission < ActiveRecord::Base
  belongs_to :mixture_submission
  has_attached_file :fid_file
  has_attached_file :nmrml_file
  do_not_validate_attachment_file_type :fid_file
  do_not_validate_attachment_file_type :nmrml_file

  default_scope {order(nmr_spectrum_type: :asc, spectrometer_frequency: :asc)}

  def get_description
    dimension = 2
    spectrum_type = self.nmr_spectrum_type
    frequency = self.spectrometer_frequency
    solvent = self.lock_solvent
    description = ""
    if spectrum_type.include? '1D'
      dimension = 1
      if spectrum_type.include? '1H'
        description = "<sup>1</sup>H"
      else
        description = "<sup>13</sup>C"
      end
    elsif spectrum_type.include? 'HSQC'
      description = "[<sup>1</sup>H, <sup>13</sup>C]"
    elsif spectrum_type.include? 'TOCSY'
      description = "[<sup>1</sup>H, <sup>1</sup>H]"
    end
    description += " NMR Spectrum (#{dimension}D, #{frequency}, #{solvent}, experimental)"
    description
  end
end
