class ExternalSubmissionCollection < ActiveRecord::Base
  belongs_to :user
  has_many :external_submissions, dependent: :destroy
  validates :submission_id, presence: true, format: { with: /\ANPd\d{9}\z/, message: "Must be in the format NPd000000001" }
  before_validation :increment_submission_id, on: :create

  # Set the NP-MRD ID to the next available ID based on the natural product entries that already exist. This
  # runs pre-validation, and only sets the NP-MRD ID if it hasn't been set explicitely.
  def increment_submission_id
    if self.submission_id.blank?
      self.submission_id = ExternalSubmissionCollection.exists? ? "NPd" + ExternalSubmissionCollection.last.submission_id[3..-1].next : 'NPd000000001'
    end
  end

end
