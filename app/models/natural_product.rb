class NaturalProduct < ActiveRecord::Base
  include XmlConversion
  include SdfConversion
  include CiteThis::Referencer


  has_structure resource: 'NP-MRD',
                finder: :find_by_moldb_ids,
                id_field: :np_mrd_id,
                mass_scope: :moldb_filtered_scope,
                prefilter: :moldb_searchable_ids

  has_spectra # Associate the natural product with spectra through SpecDBi

  # Aliases for moldb fields (perhaps add this to moldbi)
  alias_attribute :chemical_formula, :moldb_formula
  alias_attribute :smiles, :moldb_smiles
  alias_attribute :average_mass, :moldb_average_mass
  alias_attribute :mono_mass, :moldb_mono_mass
  alias_attribute :traditional_iupac, :moldb_traditional_iupac
  alias_attribute :iupac, :moldb_iupac
  alias_attribute :inchi, :moldb_inchi
  alias_attribute :inchikey, :moldb_inchikey

  delegate :water_solubility, :water_solubility_reference, :logp,
    :logp_reference,
    :melting_point, :melting_point_reference, :boiling_point,
    :boiling_point_reference,
    to: :experimental_property_set, prefix: :experimental

  has_one :experimental_property_set, dependent: :destroy
  accepts_nested_attributes_for :experimental_property_set

  has_many :accession_numbers, dependent: :destroy, as: :element
  accepts_nested_attributes_for :accession_numbers, allow_destroy: true

  has_many :two_d_spectra, foreign_key: :np_mrd_id, primary_key: :np_mrd_id
  has_many :one_d_spectra, foreign_key: :np_mrd_id, primary_key: :np_mrd_id
  # Allows us the include valid submissions ordered by created at in the natural
  # product index page preventing N + 1 query
  has_many :valid_submissions, -> { exported }, class_name: "Submission"
  has_many :valid_cs_submissions, -> { exported }, class_name: "ChemicalShiftSubmission"
  has_many :submissions
  has_many :chemical_shift_submissions
  has_many :external_submissions, dependent: :destroy
  has_many :species_mappings, as: :species_mappable, dependent: :destroy
  has_many :species, -> { order "species.scientific_name" }, through: :species_mappings
  has_one :sdf_location
  has_many :nmr_preds

  # Allows us the include valid submissions ordered by created at in the natural
  # product show page preventing N + 1 query
  has_many :external_submissions_ordered, -> { order(created_at: :desc) }, class_name: 'ExternalSubmission'
  has_many :chemical_shift_submissions_ordered, -> { where(valid: true, export: true).order(created_at: :desc) }, class_name: 'ChemicalShiftSubmission'
  has_many :submissions_ordered, -> { where(valid: true).order(created_at: :desc) }, class_name: 'Submission'

  scope :exported, -> { where(export: 1) }
  scope :not_exported, -> { where(export: 0) }

  scope :duplicates_by_inchikey, -> {
    duplicated_inchikeys = NaturalProduct
                            .select("moldb_inchikey, COUNT(*) as total")
                            .where(export: true)
                            .group(:moldb_inchikey)
                            .having("COUNT(*) > 1")
  
    joins("JOIN (#{duplicated_inchikeys.to_sql}) dup ON dup.moldb_inchikey = natural_products.moldb_inchikey")
      .where(export: true)
      .order("natural_products.moldb_inchikey")
  }

  validates :name, presence: true, length: {minimum: 3, maximum: 511}
  validates :np_mrd_id, presence: true, format: { with: /\ANP\d{7}\z/, message: "Must be in the format NP0000001" }
  validates :cas, cas: true
  has_attached_file :thumb
  validates_attachment_content_type :thumb, content_type: "image/png"

  before_validation :increment_np_mrd_id, on: :create
  after_initialize :init_has_ones
  after_save :extract_description_references

  def label
    "#{self.name} (#{self.np_mrd_id})"
  end

  def to_param
    self.np_mrd_id
  end

  def load_thumb_from_url(url)
    self.thumb = URI.parse(url).open
  end

  def db_release_status
    if export
      "released"
    else
      "embargoed"
    end
  end

  # Only for admins and depositors to explain where there data sits now. Returns parent_num if exists
  def parent_accession_number
    parent_num = AccessionNumber.where(number: np_mrd_id).first
    if parent_num
      return NaturalProduct.find(parent_num.element_id).np_mrd_id
    else
      return parent_num
    end
  end

  # Method to gather and merge related data from accession numbers
  def get_related_submissions
    # Collect the accession numbers of the current compound
    accession_numbers_array = accession_numbers.pluck(:number)
  
    # Find all compound_ids linked through the same accession numbers
    related_compound_ids = NaturalProduct.where(np_mrd_id: accession_numbers_array).pluck(:id)
    related_compound_ids.push(self.id)
  
    # Initialize query conditions
    export_filter = export ? { export: true } : {}
    valid_filter = export ? { valid: true } : {}
  
    # Fetch and merge data from submissions for all related compounds
    external_submissions = ExternalSubmission.where(natural_product_id: related_compound_ids)
                                             .order(created_at: :desc)
  
    chemical_shifts = ChemicalShiftSubmission.where(natural_product_id: related_compound_ids)
                                             .where(export_filter)
                                             .where(valid_filter)
                                             .order(created_at: :desc)
  
    other_submissions = Submission.where(natural_product_id: related_compound_ids)
                                  .where(valid_filter)
                                  .order(created_at: :desc)
  
    return external_submissions, chemical_shifts, other_submissions
  end  

  # Previous function to get submissions without accession numbers
  def get_related_submissions_v0
    if export
      return external_submissions_ordered, chemical_shift_submissions_ordered, submissions_ordered
    else
      # Route taken by unexported natural products, embargoed and visible only to admins and people who deposited data
      return external_submissions_ordered, chemical_shift_submissions.order(created_at: :desc), submissions.order(created_at: :desc)
    end
  end

  def valid_thumb?
    return self.thumb.exists?
  end

  def same_structures
    NaturalProduct.where(moldb_inchikey: moldb_inchikey)
  end

  def same_names
    NaturalProduct.where(name: name)
  end

  def get_description
    if self.description?
      description = self.description
    else
      description = self.np_mrd_cs_description
    end

    ###### uncommit later ##########
    # description = nil
    # if description && (self.description.length < 400 || (description.length/self.description.length) > 1.95)
    #   return description
    # end
    # return self.description
    #######################
    #### for now we are not checking description length ####
    if description
      return description.gsub(/([a-z])([^.?!:]*)/i) { $1.upcase + $2.rstrip }
    elsif self.description
      return self.description.gsub(/([a-z])([^.?!:]*)/i) { $1.upcase + $2.rstrip }
    else
      return ""
    end
  end

  def bust_all_cache
    # This removes curation resource cache so that we can see classificationm
    # identifiers, references, and other metadata, synonyms, etc.
    Moldbi::CurationResource.bust_cache(self.np_mrd_id)
    Moldbi::StructureResource.bust_cache(self.np_mrd_id)
    # This removes the structure resource cache so that we can see the structure,
    # and related images
    # Moldbi::StructureResource.bust_cache(@natural_product.np_mrd_id)
    # This removes the show page cache so that we can display any data
    @natural_product.touch
  end

  def moldb_alogps_solubility
    value = self.read_attribute(:moldb_alogps_solubility)
    return if value.blank?
    value = value.to_f
    if value < 0.001
      "%.3E" % value
    else
      value.smart_round_to_s
    end.to_s + " g/L"
  end

  def title
    "#{self.np_mrd_id} (#{self.name})"
  end

  def to_s
    self.title
  end

  # Set the NP-MRD ID to the next available ID based on the natural product entries that already exist. This
  # runs pre-validation, and only sets the NP-MRD ID if it hasn't been set explicitely.
  def increment_np_mrd_id
    if self.np_mrd_id.blank?
      self.np_mrd_id = NaturalProduct.exists? ? "NP" + NaturalProduct.last.np_mrd_id[2..-1].next : 'NP0000001'
    end
  end

  def mol_file
    self.has_structure? ? self.structure_resource.mol : nil
  end

  def load_threeDmol
    return self.threeDmol if self.threeDmol
    generate_threeDmol
  end

  def generate_threeDmol
    error = false
    mol = nil
    message = nil
    begin
      stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/rdkit_smiles_to_mol.py '#{self.smiles}'")
      mol = stdout.gets(nil).to_s
      puts mol
    rescue Exception => e
      message = e.message
      error = true
    end
    self.threeDmol = mol
    self.save!
    self.threeDmol
  end

  def tmp_mol_file
    #return @tmp_mol_file if @tmp_mol_file
    if mol_file
      file = Tempfile.new('mol_file',"#{Rails.root}/app/assets/")
      file.path      # => A unique filename in the OS's temp directory,
                     #    e.g.: "/tmp/foo.24722.0"
                     #    This filename contains 'foo' in its basename.
      file.write(mol_file)
      @tmp_mol_file = file.path
    end
    puts @tmp_mol_file
    return @tmp_mol_file
  end

  def exported?
    export?
  end

  # Use this resource to get a list of filtered articles and remove faulty references from Pubmed
  # This also filters articles that are submitted via external submissions
  def filtered_articles
    synonyms = self.wrangler_synonyms&.map { |synonym| synonym["name"].strip } || []
    name_and_synonyms = [self.name.downcase.strip] + synonyms.map(&:downcase)
    name_and_synonyms.push(self.wrangler_identifiers['name']&.downcase&.strip) if self.wrangler_identifiers
    name_and_synonyms.compact!

    selected_articles = self.articles.select do |article|
      next if article.title.blank? && article.abstract.blank?
      title = article.title.to_s.downcase.strip
      abstract = article.abstract.to_s.downcase.strip
  
      name_and_synonyms.any? do |term|
        title.include?(term) || abstract.include?(term)
      end
    end
    puts selected_articles.map(&:title)
  
    external_submission_articles = self.external_submissions.flat_map(&:articles)
    (selected_articles + external_submission_articles).uniq
  end

  # These species mappings functions are depecrated as they cause N+1 queries. However, the non N+1 solution does
  # not produce exactly the same output, so they will be left for now.
  # Get species mappings ordered by species' scientific name and source
  def ordered_species_mappings
    species_mappings.joins(:species).order("species.scientific_name, species_mappings.source")
  end

  # Get species mappings which are species of origin ordered by species' scientific name and source
  def origin_species_mappings_v0
    species_mappings.includes(:species, :articles, :textbooks, :external_links)
                    .where(is_origin_species: true)
                    .order("species.scientific_name, species_mappings.source")
  end

  # Modified method to include species_mappings from both NaturalProduct and its ExternalSubmissions
  def origin_species_mappings
    # IDs of species_mappings directly associated with this NaturalProduct
    natural_product_species_mapping_ids = species_mappings.where(is_origin_species: true).ids
    
    # IDs of species_mappings associated with this NaturalProduct's ExternalSubmissions
    external_submission_species_mapping_ids = ExternalSubmission.where(natural_product_id: id)
                                                                .joins(:species_mappings)
                                                                .where(species_mappings: { is_origin_species: true })
                                                                .pluck('species_mappings.id')

    # Combine IDs
    combined_ids = natural_product_species_mapping_ids + external_submission_species_mapping_ids

    # Query for SpeciesMapping records by combined IDs, ensuring no duplicates
    SpeciesMapping.includes(:species, :articles, :textbooks, :external_links)
                  .where(id: combined_ids.uniq)
                  .order("species.scientific_name, species_mappings.source")
  end  

  # Get species mappings which are species detected ordered by species' scientific name and source
  def detected_species_mappings
    species_mappings.includes(:species, :articles, :textbooks, :external_links)
                    .where(is_origin_species: false)
                    .order("species.scientific_name, species_mappings.source")
  end

  def origin_species_array
    species_mappings.select { |species| species.is_origin_species == true }.sort_by { |s| s.species.scientific_name  }
  end

  # Get an array of MolDB spectrum summaries (for chemical shift submission alert)
  def moldb_nmr_spectra_summaries
    spectra_summaries = []
    puts("inside moldb_nmr_spectra_summaries, spectra= #{spectra}")
    
    if nmr_one_d_spectra.any? || nmr_two_d_spectra.any?
      nmr_spectra = spectra.select { |s| s.is_a?(Specdb::NmrOneD) || s.is_a?(Specdb::NmrTwoD) }
      earliest_creation_date = nmr_spectra.map{ |s| s.created_at.to_date }.sort.first.strftime("%Y-%m-%d")
      last_update_date = nmr_spectra.map{ |s| s.updated_at.to_date }.sort.last.strftime("%Y-%m-%d")

      nmr_spectra.each do |spectrum|
        if spectrum.is_a?(Specdb::NmrOneD)
          spectrum_summary = "Expt. #{spectrum.nucleus.gsub(/(\d+)/, '<sup>\0</sup>')} assignment"
        else spectrum.is_a?(Specdb::NmrTwoD)
          spectrum_summary =
            "Expt. #{"#{spectrum.nucleus_x}-#{spectrum.nucleus_y}".gsub(/(\d+)/, '<sup>\0</sup>')} assignment"
        end

        # Frequency
        if spectrum.frequency
          spectrum_summary += " @#{spectrum.frequency}"
        end

        # Solvent
        if spectrum.solvent
          spectrum_summary += ", #{self.subscript_solvent(spectrum.solvent)} solvent"
        end

        # Spectrum Type
        if spectrum.is_a?(Specdb::NmrOneD)
          spectrum_summary += " (#{self.superscript_spectrum("1D-#{spectrum.nucleus}")})"
        else spectrum.is_a?(Specdb::NmrTwoD)
          spectrum_summary += " (#{self.superscript_spectrum("2D-#{spectrum.nucleus_x}-#{spectrum.nucleus_y}")})"
        end


        # Store in array
        spectra_summaries.push(spectrum_summary.html_safe)
      end
      return spectra_summaries, earliest_creation_date, last_update_date
    end
    return spectra_summaries, nil, nil
  end

  def self.from_param(id)
    self.where(np_mrd_id: id).take!
  end

  # Find records based on the matching IDs returned from a
  # MolDB structure search
  def self.find_by_moldb_ids(ids)
    self.exported.where(np_mrd_id: ids).uniq
  end

  # The list of IDs that are passed to the MolDB server when a search is
  # sent
  def self.moldb_searchable_ids(filters={})
    self.exported.pluck(:np_mrd_id)
  end

  def self.moldb_filtered_scope(filters={})
    self.exported
  end

  # For specdbi as it expects that the inchi_key does not have InChIKey at the start
  def self.find_by_inchi_key(inchi_key)
    self.where(moldb_inchikey: "InChIKey=#{inchi_key.strip}").take
  end

  # Finder to find by NP-MRD ID (avoids method missing - we use this in many places)
  def self.find_by_np_mrd_id(np_mrd_id)
    self.where(np_mrd_id: np_mrd_id).take
  end

  def self.exported_ranges
    [
      ['NP0000001', 'NP0050000'],
      ['NP0050001', 'NP0100000'],
      ['NP0100001', 'NP0150000'],
      ['NP0150001', 'NP0200000'],
      ['NP0200001', 'NP0250000'],
      ['NP0250001', 'NP0300000'],
      ['NP0300001', 'NP0350000']
    ]
  end
  
  protected

  # Initialize in the model since the these models don't make sense outside
  # of a relationship with Natural Product
  def init_has_ones
    if self.new_record?
      self.experimental_property_set ||= self.build_experimental_property_set
    end
  end

  def self.to_csv
    attributes = %w{NP-MRD_ID NAME SMILES}
    CSV.generate(headers: true) do |csv|
      csv << attributes
      all.each do |natural_product|
        csv << attributes.map{ |attr| natural_product.send(attr.downcase)}
      end
    end
  end

  # Look for newly added PubMed references in the description text. If there
  # are new ones, add them to the general references.
  def extract_description_references
    if self.description_changed?
      matches = self.description.scan(/PMID:?\s*([\d\,\s]+)/i)
      if matches.present?
        matches.each do |match|
          match.first.split(/,\s*/).each do |id|
            next if id.blank?

            if self.articles.where(pubmed_id: id).blank?
              article = CiteThis::Article.where(pubmed_id: id).first_or_create

              if article.persisted?
                self.articles << article
              else
                self.errors[:description] << "contains invalid PubMed reference (#{id})"
                raise ActiveRecord::Rollback
              end
            end
          end
        end
      end
    end
  end

  # *** This function is defined in submissions_helper but redefined here because I don't believe NaturalProduct and
  # SubmissionsHelper should be related - i.e. Unnecessary coupling. It should be safe to use that method instead
  # if this code decision is deemed to be unnecessary ***
  #
  # Changes the spectrum type into a superscripted form
  # @param [String] spectrum A string of spectrum value
  # @return [String] the superscripted version of the spectrum type
  def superscript_spectrum(spectrum)
    # Hash with spectrum types as keys and values as the superscripted version
    superscript_table = {'1D-1H': '1D-<sup>1</sup>H'.html_safe, '1D-13C': '1D-<sup>13</sup>C'.html_safe,
                         '1D-1H-DEPT90': '1D-<sup>1</sup>H-DEPT90'.html_safe,
                         '1D-13C-DEPT90': '1D-<sup>13</sup>C-DEPT90'.html_safe,
                         '1D-31P': '1D-<sup>31</sup>P'.html_safe,
                         '2D-1H-1H': '2D-<sup>1</sup>H-<sup>1</sup>H'.html_safe,
                         '2D-1H-13C': '2D-<sup>1</sup>H-<sup>13</sup>C'.html_safe,
                         '2D-13C-13C': '2D-<sup>13</sup>C-<sup>13</sup>C'.html_safe,
                         '2D-1H-1H-COSY': '2D-<sup>1</sup>H-<sup>1</sup>H-COSY'.html_safe,
                         '2D-1H-13C-COSY': '2D-<sup>1</sup>H-<sup>13</sup>C-COSY'.html_safe,
                         '2D-13C-13C-COSY': '2D-<sup>13</sup>C-<sup>13</sup>C-COSY'.html_safe,
                         '2D-1H-13C-HSQC': '2D-<sup>1</sup>H-<sup>13</sup>C-HSQC'.html_safe,
                         '2D-1H-13C-HMQC': '2D-<sup>1</sup>H-<sup>13</sup>C-HMQC'.html_safe,
                         '2D-1H-13C-HMBC': '2D-<sup>1</sup>H-<sup>13</sup>C-HMBC'.html_safe,
                         '2D-1H-15N-HMBC': '2D-<sup>1</sup>H-<sup>15</sup>N-HMBC'.html_safe,
                         '2D-1H-1H-TOCSY': '2D-<sup>1</sup>H-<sup>1</sup>H-TOCSY'.html_safe,
                         '2D-1H-1H-ROESY': '2D-<sup>1</sup>H-<sup>1</sup>H-ROESY'.html_safe,
                         '2D-13C-13C-INADEQUATE': '2D-<sup>13</sup>C-<sup>13</sup>C-INADEQUATE'.html_safe}
                          .stringify_keys
    # Return the superscripted value
    superscript_table[spectrum]
  end

  # Note: This needs to be expanded when new solvents are put into moldb
  #
  # Changes the solvent into a subscripted form
  # @param [String] solvent A string of the solvent
  # @return [String] the subscripted version of the solvent
  def subscript_solvent(solvent)
    if solvent == 'CD2Cl2'
      'CD<sub>2</sub>Cl<sub>2</sub>'.html_safe
    elsif solvent == 'CDCl3'
      'CDCl<sub>3</sub>'.html_safe
    elsif solvent == "CD3OD"
      "CD<sub>3</sub>OD".html_safe
    elsif solvent == "CCl4"
      "CCl<sub>4</sub>".html_safe
    elsif solvent == "D2O"
      "D<sub>2</sub>0".html_safe
    elsif solvent == "CS2"
      "CS<sub>2</sub>".html_safe
    elsif solvent == "Sodium phosphate (pH 7.4) in 90% H2O /10% D2O"
      "Sodium phosphate (pH 7.4) in 90% H<sub>2</sub>O /10% D<sub>2</sub>O".html_safe
    else
      solvent
    end
  end
end
