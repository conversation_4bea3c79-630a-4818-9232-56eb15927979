class ExternalSubmission < ActiveRecord::Base
  include CiteThis::Referencer

  belongs_to :natural_product, touch: true
  belongs_to :external_submission_collection
  belongs_to :user
  has_many :external_nmr_submissions, dependent: :destroy
  has_many :chemical_shift_submissions, dependent: :destroy

  has_many :valid_cs_submissions, -> { exported }, class_name: "ChemicalShiftSubmission"
  has_many :valid_nmr_submissions, -> { exported }, class_name: "ExternalNmrSubmission"

  has_many :species_mappings, as: :species_mappable, dependent: :destroy
  has_many :species, -> { order "species.scientific_name" }, through: :species_mappings
  
  has_attached_file :quality_report
  do_not_validate_attachment_file_type :quality_report

  serialize :origin_private_collection, JSON
  serialize :submission_json, JSON

  scope :np_exported, -> { joins(:natural_product).where(natural_products: { export: true }) }
  scope :np_not_exported, -> { joins(:natural_product).where(natural_products: { export: false }) }

  scope :inchikey_matched, -> { where(npmrd_match_status: 'inchikey') }
  scope :name_matched, -> { where(npmrd_match_status: 'name') }

  after_save :update_chemical_shift_submissions

  # Define a mapping of external_submission attributes to their data sources
  SUBMISSION_MAPPINGS = {
    depositor_email: ['depositor_info', 'email'],
    depositor_account_id: ['depositor_info', 'account_id'],
    depositor_attribution_name: ['depositor_info', 'attribution_name'],
    depositor_attribution_organization: ['depositor_info', 'attribution_organization'],
    depositor_show_email_in_attribution: ['depositor_info', 'show_email_in_attribution'],
    depositor_show_name_in_attribution: ['depositor_info', 'show_name_in_attribution'],
    depositor_show_organization_in_attribution: ['depositor_info', 'show_organization_in_attribution'],
    citation_doi: ['citation', 'doi'],
    citation_pmid: ['citation', 'pmid'],
    citation_pii: ['citation', 'pii'],
    origin_genus: ['origin', 'genus'],
    origin_species: ['origin', 'species'],
    origin_private_collection: ['origin', 'private_collection'],
    submission_source: ['submission', 'source'],
    submission_type: ['submission', 'type'],
    compound_uuid: ['submission', 'compound_uuid'],
    submission_date: ['submission', 'submission_date'],
    embargo_status: ['submission', 'embargo_status'],
    embargo_date: ['submission', 'embargo_date'],
    submission_uuid: ['submission', 'submission_uuid'],
    compound_embargo_release_ready: ['submission', 'compound_embargo_release_ready']
}.freeze

  # Creating report_data with direct and nested fields correctly
  DIRECT_FIELDS = %w[inchikey compound_name smiles].freeze
  NESTED_REPORT_FIELDS = { 
    'submission_uuid' => ['submission', 'submission_uuid'], 
    'compound_uuid' => ['submission', 'compound_uuid'],
    'send_record_id' => ['submission', 'send_record_id']
  }.freeze

  MATCH_STATUS_VALUES = ["npmrd_id", "inchikey_name", "flat_inchikey_name", "inchikey", "name", "new_entry"].freeze
  FOR_REVIEW = ["inchikey", "name"].freeze

  # Parse JSON and return the value for DOI
  def doi
    origin_private_collection&.dig('doi')
  end

  # Parse JSON and return the commercial supplier
  def commercial_supplier
    origin_private_collection&.dig('commercial', 'supplier')
  end

  # Parse JSON and return the catalogue number
  def catalogue_number
    origin_private_collection&.dig('commercial', 'catalogue_number')
  end

  # Parse JSON and return the CAS number
  def cas_number
    origin_private_collection&.dig('commercial', 'cas_number')
  end

  # Parse JSON and return the compound library source (library name)
  def compound_library_source
    origin_private_collection&.dig('compound_library', 'library_name')
  end

  # Parse JSON and return the compound library description
  def compound_library_description
    origin_private_collection&.dig('compound_library', 'library_description')
  end

  # Parse JSON and return the compound library code
  def compound_library_code
    origin_private_collection&.dig('compound_library', 'library_compound_code')
  end

  # Parse JSON and return the biological material source from purified_in_house
  def biological_material_source
    if origin_private_collection&.dig('purified_in_house', 'biological_material_source').present?
      origin_private_collection&.dig('purified_in_house', 'biological_material_source')
    else
      origin_private_collection&.dig('other', 'biological_material_source')
    end
  end

  # Parse JSON and return the user-defined compound source
  def user_defined_compound_source
    origin_private_collection&.dig('other', 'user_specified_compound_source')
  end

  # compound_uuid will be null for previous depositions and for new API, check if it can be showed
  def show_depositor_email
    depositor_email if compound_uuid.nil? || depositor_show_email_in_attribution
  end

  def show_depositor_organization
    depositor_attribution_organization if depositor_show_organization_in_attribution
  end

  def show_depositor_name
    depositor_attribution_name if depositor_show_name_in_attribution
  end

  private

  def update_chemical_shift_submissions
    chemical_shift_submissions.update_all(natural_product_id: self.natural_product_id)
  end
end
