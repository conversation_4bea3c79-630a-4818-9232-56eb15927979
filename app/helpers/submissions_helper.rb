module SubmissionsHelper
  def literature_reference_types_collection
    %w(PMID DOI Book Unpublished\ /\ Under\ Review)
  end

  # def solvent_collection
  #   [['H&#8322;O'.html_safe, 'H2O'], ['CDCl&#8323;'.html_safe, 'CDCl3'], %w[DMSO DMSO], %w[Methanol Methanol],
  #    %w[Acetone Acetone], %w[Pyridine Pyridine]]
  # end

  def solvent_collection
    [['H&#8322;O'.html_safe, 'H2O'], ['CDCl&#8323;'.html_safe, 'CDCl3'], %w[DMSO DMSO], %w[Methanol Methanol],
     %w[Acetone Acetone]]
  end

  def spectrometer_frequency_collection
    # %w(300MHz 400MHz 500MHz 600MHz 700MHz 800MHz 850MHz 900Mz 1GHz)
    ["300 MHz", "400 MHz", "500 MHz", "600 MHz", "700 MHz", "800 MHz", "850 MHz", "900 Mz", "1 GHz"]
  end

  def chemical_shift_reference_collection
    # This uses unicode to display subscripted numbers
    # &#8322; is a subscripted 2
    # &#8323; is a subscripted 3
    [%w[Tetramethylsilane\ (TMS) TMS], %w[Sodium\ Trimethylsilylpropanesulfonate\ (DSS) DSS],
     ['Water (H&#8322;O) (Residual-Peak)'.html_safe, 'H2O (Residual-Peak)'],
     ['Chloroform (CHCl&#8323;) (Residual-Peak)'.html_safe, 'CHCl3 (Residual-Peak)'],
     ['Ethanol (C&#8322;H&#8326;O) (Residual-Peak)'.html_safe, 'Ethanol (Residual-Peak)'],
     ['Methanol (CH&#8323;OH) (Residual-Peak)'.html_safe, 'Methanol (Residual-Peak)']]
  end

  def spectrum_type_collection
    # This uses unicode to display superscripted numbers
    # &#185; is a superscripted 1
    # &#179; is a superscripted 3
    # &#8309; is a superscripted 5
    [['1D-&#185;H'.html_safe, '1D-1H'], ['1D-&#185;&#179;C'.html_safe, '1D-13C'],
     ['2D-&#185;H-&#185;&#179;C'.html_safe, '2D-1H-13C']]
  end

  def processed_spectrum_type_collection
    #['Not_Processed','Topspin_Bruker','.JSON','.txt']
    [['Not processed (1D only)', 'Not_Processed'],
     ['Topspin processed', 'Topspin_Bruker'],
     'VNMR processed',
     '.JSON','.txt']
  end


  def fid_spectrum_type_collection
    [['1D-&#185;H'.html_safe, '1D-1H'], ['1D-&#185;&#179;C'.html_safe, '1D-13C'],
     ['1D-&#185;H-DEPT90'.html_safe, '1D-1H-DEPT90'], ['1D-&#185;&#179;C-DEPT90'.html_safe, '1D-13C-DEPT90'],
     ['1D-&#179;&#185;P'.html_safe, '1D-31P'], ['2D-&#185;H-&#185;H-COSY'.html_safe, '2D-1H-1H-COSY'],
     ['2D-&#185;H-&#185;&#179;C-COSY'.html_safe, '2D-1H-13C-COSY'],
     ['2D-&#185;&#179;C-&#185;&#179;C-COSY'.html_safe, '2D-13C-13C-COSY'],
     ['2D-&#185;H-&#185;&#179;C-HSQC'.html_safe, '2D-1H-13C-HSQC'],
     ['2D-&#185;H-&#185;&#179;C-HMQC'.html_safe, '2D-1H-13C-HMQC'],
     ['2D-&#185;H-&#185;&#179;C-HMBC'.html_safe, '2D-1H-13C-HMBC'],
     ['2D-&#185;H-&#185;&#8309;N-HMBC'.html_safe, '2D-1H-15N-HMBC'],
     ['2D-&#185;H-&#185;H-TOCSY'.html_safe, '2D-1H-1H-TOCSY'],
     ['2D-&#185;H-&#185;H-ROESY'.html_safe, '2D-1H-1H-ROESY'],
     ['2D-&#185;&#179;C-&#185;&#179;C-INADEQUATE'.html_safe, '2D-13C-13C-INADEQUATE']]
  end

  def physical_state_of_compound_collection
    %w(Solid  Liquid  Gas)
  end

  def is_number? string
    true if Float(string) rescue false
  end

  def provenance_collection
    ["Biotransformation", "Chemical synthesis", "Commercial", "eDNA screening", "Heterologous biosynthesis",
     "Isolated from a combinational source", "Isolated from a community", "Isolated from a field sample",
     "Shunt products", "Other"]
  end

  def calculator_details_modal
    render '/custom_atom_numbers/calculator_details_modal'
  end

  def link_to_submission_calculator_details(submission_id)
    link_to "Renumber Atoms".html_safe,
      main_app.new_custom_atom_number_path(submission_id),
      data: { toggle: 'modal', target: '#project-modal' },
      class: 'btn-calculator-details'#, target: "_blank"
  end


  def savenmrfile_details_modal
    render '/nmr_submissions/savenmrfile_details_modal'
  end

  def link_to_savenmrfile(submission_id)
    puts"reached link_to_savenmrfile"
    puts"submission_id = #{submission_id}"

    link_to "<b>Upload Spectrum</b>".html_safe,
      main_app.newsavenmrfile_nmr_submission_path(submission_id),
      data: { toggle: 'modal', target: '#savenmrfile-details' },
      class: 'btn-savenmrfile-details'
  end

  # Changes the spectrum type into a superscripted form
  # @param [String] spectrum A string of spectrum value
  # @return [String] the superscripted version of the spectrum type
  def superscript_spectrum(spectrum)
    # Hash with spectrum types as keys and values as the superscripted version
    superscript_table = {'1D-1H': '1D-<sup>1</sup>H'.html_safe, '1D-13C': '1D-<sup>13</sup>C'.html_safe,
                         '1D-1H-DEPT90': '1D-<sup>1</sup>H-DEPT90'.html_safe,
                         '1D-13C-DEPT90': '1D-<sup>13</sup>C-DEPT90'.html_safe,
                         '1D-31P': '1D-<sup>31</sup>P'.html_safe,
                         '2D-1H-1H-COSY': '2D-<sup>1</sup>H-<sup>1</sup>H-COSY'.html_safe,
                         '2D-1H-13C-COSY': '2D-<sup>1</sup>H-<sup>13</sup>C-COSY'.html_safe,
                         '2D-13C-13C-COSY': '2D-<sup>13</sup>C-<sup>13</sup>C-COSY'.html_safe,
                         '2D-1H-13C-HSQC': '2D-<sup>1</sup>H-<sup>13</sup>C-HSQC'.html_safe,
                         '2D-1H-13C-HMQC': '2D-<sup>1</sup>H-<sup>13</sup>C-HMQC'.html_safe,
                         '2D-1H-13C-HMBC': '2D-<sup>1</sup>H-<sup>13</sup>C-HMBC'.html_safe,
                         '2D-1H-15N-HMBC': '2D-<sup>1</sup>H-<sup>15</sup>N-HMBC'.html_safe,
                         '2D-1H-1H-TOCSY': '2D-<sup>1</sup>H-<sup>1</sup>H-TOCSY'.html_safe,
                         '2D-1H-1H-ROESY': '2D-<sup>1</sup>H-<sup>1</sup>H-ROESY'.html_safe,
                         '2D-13C-13C-INADEQUATE': '2D-<sup>13</sup>C-<sup>13</sup>C-INADEQUATE'.html_safe}
                          .stringify_keys
    # Return the superscripted value
    superscript_table[spectrum]
  end

  # Changes the solvent into a subscripted form
  # @param [String] solvent A string of the solvent
  # @return [String] the subscripted version of the solvent
  def subscript_solvent(solvent)
    # Since H2O and CDCl3 are the only ones that need a subscript, check for those and return subscripted version.
    # Otherwise, return original value
    if solvent == 'H2O'
      'H<sub>2</sub>O'.html_safe
    elsif solvent == 'CDCl3'
      'CDCl<sub>3</sub>'.html_safe
    else
      solvent
    end
  end

  # Changes the chemical shift reference into a subscripted form
  # @param [String] solvent - A string of the chemical shift reference
  # @return [String] the subscripted version of the chemical shift reference
  def subscript_reference(reference)
    # Since H2O and CDCl3 are the only ones that need a subscript, check for those and return subscripted version.
    # Otherwise, return original value
    if reference == 'H2O (Residual-Peak)'
      'H<sub>2</sub>O (Residual-Peak)'.html_safe
    elsif reference == 'CHCl3 (Residual-Peak)'
      'CHCl<sub>3</sub> (Residual-Peak)'.html_safe
    else
      reference
    end
  end

  # Get the earliest creation date and latest update date from the union of
  # MolDB spectra and valid chemical shift submissions
  def get_creation_update_dates(submissions, earliest_creation_date, last_update_date)
    # Dates from valid chemical shift submissions
    submission_earliest_creation_date = submissions.order(:created_at).first.created_at.strftime("%Y-%m-%d")
    submission_last_update_date = submissions.order(:updated_at).last.updated_at.strftime("%Y-%m-%d")

    # Compare with MolDB dates
    if earliest_creation_date # MolDB spectra exist
      earliest_creation_date = [earliest_creation_date, submission_earliest_creation_date].min
      last_update_date = [last_update_date, submission_last_update_date].max
      return earliest_creation_date, last_update_date
    else # no MolDB spectra exist
      return submission_earliest_creation_date, submission_last_update_date
    end
  end
end
