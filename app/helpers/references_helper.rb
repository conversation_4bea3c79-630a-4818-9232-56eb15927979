module ReferencesHelper
  # Return the given text with links to different references.
  def text_with_linked_references(text, num_references)
    # Please don't convert to REGEX. It is a big one and not future-self friendly.
    # Sometimes some verbosity goes a long way...

    return text if text.blank?

    begin # Rescue errors to ensure this never fails and kills the page
      linked_text = text.to_s
      linkers = { pubmed: 'PMID', omim: 'OMIM' }

      linkers.each do |link_type, matcher|
        matches = text.scan(/#{matcher}:?\s*([\d\,\s]+)/i)
        replaced = Set.new

        if matches.present?
          matches.each do |match|
            match.first.split(/,\s*/).each do |id|
              next if id.blank? || replaced.include?(id)
              replaced << id
              linked_text.gsub!(id, bio_link_out(link_type, id))
            end
          end
        end
      end

      replace_count_text(linked_text, num_references)
    rescue Exception => e
      logger.error "Couldn't link refernces in description (#{text}): #{e.message}"
      text
    end
  end

  def replace_count_text(text, num_references)
    # Determine the reference description based on the number of references
    reference_description = if num_references < 3
                              "very few articles"
                            elsif num_references < 6
                              "a small amount of articles"
                            else
                              "a significant number of articles"
                            end

    # Replace the phrase in the text
    text.gsub(/Based on a literature review a significant number of articles/, "Based on a literature review #{reference_description}")
  end
end