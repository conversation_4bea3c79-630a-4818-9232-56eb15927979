module ChemicalShiftSubmissionsHelper
  def literature_reference_types_collection
    %w(PMID DOI Book Unpublished/Under\ Review)
  end

  def mixture_solvent_types_collection
    %w(Aqueous Chloroform Other)
  end

  def solvent_collection
    # This uses unicode to display subscripted numbers
    # &#8322; is a subscripted 2
    # &#8323; is a subscripted 3
    [['H&#8322;O'.html_safe, 'H2O'], ['CDCl&#8323;'.html_safe, 'CDCl3'], %w[DMSO DMSO], %w[Methanol Methanol],
     %w[Acetone Acetone], %w[Pyridine Pyridine]]
  end

  def spectrometer_frequency_collection
    ["300 MHz", "400 MHz", "500 MHz", "600 MHz", "700 MHz", "800 MHz", "850 MHz", "900 Mz", "1 GHz"]
  end

  def chemical_shift_reference_collection
    [%w[Tetramethylsilane\ (TMS) TMS], %w[Sodium\ Trimethylsilylpropanesulfonate\ (DSS) DSS],
     ['Water (H&#8322;O) (Residual-Peak)'.html_safe, 'H2O (Residual-Peak)'],
     ['Chloroform (CHCl&#8323;) (Residual-Peak)'.html_safe, 'CHCl3 (Residual-Peak)'],
     ['Ethanol (C&#8322;H&#8326;O) (Residual-Peak)'.html_safe, 'Ethanol (Residual-Peak)'],
     ['Methanol (CH&#8323;OH) (Residual-Peak)'.html_safe, 'Methanol (Residual-Peak)']]
  end

  def spectrum_type_collection
    # This uses unicode to display superscripted numbers
    # &#185; is a superscripted 1
    # &#179; is a superscripted 3
    # &#8309; is a superscripted 5
    [['1D-&#185;H'.html_safe, '1D-1H'], ['1D-&#185;&#179;C'.html_safe, '1D-13C'],
     ['2D-&#185;H-&#185;&#179;C'.html_safe, '2D-1H-13C']]
  end



  def physical_state_of_compound_collection
    %w(Solid  Liquid  Gas)
  end

  def is_number? string
    true if Float(string) rescue false
  end

  def provenance_collection
    ["Biotransformation", "Chemical synthesis", "Commercial", "eDNA screening", "Heterologous biosynthesis",
     "Isolated from a combinational source", "Isolated from a community", "Isolated from a field sample",
     "Shunt products", "Other"]
  end


  def calculator_details_modal
    render '/custom_atom_numbers/calculator_details_modal'
  end

  def link_to_calculator_details(chemical_shift_submission)
    link_to "Renumber Atoms".html_safe,
      main_app.new_modal_custom_atom_number_path(chemical_shift_submission),
      data: { toggle: 'modal', target: '#project-modal' },
      class: 'btn-calculator-details'#, target: "_blank"
  end


  # Get the earliest creation date and latest update date from the union of
  # MolDB spectra and valid chemical shift submissions
  def get_creation_update_dates(chemical_shift_submissions, earliest_creation_date, last_update_date)
    # Dates from valid chemical shift submissions
    submission_earliest_creation_date = chemical_shift_submissions.order(:created_at).first.created_at.strftime("%Y-%m-%d")
    submission_last_update_date = chemical_shift_submissions.order(:updated_at).last.updated_at.strftime("%Y-%m-%d")

    # Compare with MolDB dates
    if earliest_creation_date # MolDB spectra exist
      earliest_creation_date = [earliest_creation_date, submission_earliest_creation_date].min
      last_update_date = [last_update_date, submission_last_update_date].max
      return earliest_creation_date, last_update_date
    else # no MolDB spectra exist
      return submission_earliest_creation_date, submission_last_update_date
    end
  end
end
