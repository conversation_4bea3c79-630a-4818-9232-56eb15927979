# frozen_string_literal: true

module NaturalProductsHelper
  # Checks if a given spectrum is predicted based on its spectrum_type.
  #
  # @param spectrum [Object] A spectrum object with a spectrum_type attribute.
  # @return [<PERSON><PERSON><PERSON>] Returns true if the spectrum_type contains 'predicted', false otherwise.
  def is_predicted(spectrum)
    spectrum.spectrum_type.include?('predicted')
  end

  # Sorts and separates spectra into two hashes (predicted and experimental) by SpecDB type.
  #
  # @param spectra [Array] An array of spectrum objects.
  # @return [Array<Hash>] Returns two hashes: the first containing predicted spectra, and the second containing experimental spectra.
  def sorted_spectra_with_type(spectra)
    predicted_spectra = Hash.new { |hash, key| hash[key] = [] }
    experimental_spectra = Hash.new { |hash, key| hash[key] = [] }

    spectra.compact.each do |spectrum|
      target_hash = is_predicted(spectrum) ? predicted_spectra : experimental_spectra

      if spectrum.instance_of?(Specdb::CMs)
        target_hash[:gcms] << spectrum
      elsif spectrum.instance_of?(Specdb::Ms<PERSON>)
        if spectrum.spectrum_type =~ /^LC-MS/
          target_hash[:lcms] << spectrum
        else
          target_hash[:msms] << spectrum
        end
      elsif spectrum.instance_of?(Specdb::NmrOneD)
        target_hash[:nmr1d] << spectrum
      elsif spectrum.instance_of?(Specdb::NmrTwoD)
        target_hash[:nmr2d] << spectrum
      end
    end

    [predicted_spectra, experimental_spectra]
  end

  # Checks if any NMR1D or NMR2D spectra are present in the given hash.
  #
  # @param spectra_hash [Hash] A hash containing spectra objects as values.
  # @return [Boolean] Returns true if there are any NMR1D or NMR2D spectra present, false otherwise.
  def any_nmr_spectra?(spectra_hash)
    spectra_hash[:nmr1d].present? || spectra_hash[:nmr2d].present?
  end

  # Sort spectra into a hash, by SpecDB type
  def sorted_spectra(spectra)
    sorted_spectra = {}
    sorted_spectra[:gcms] = []
    sorted_spectra[:msms] = []
    sorted_spectra[:lcms] = []
    sorted_spectra[:nmr1d] = []
    sorted_spectra[:nmr2d] = []

    spectra.each do |spectrum|
      if spectrum.instance_of?(Specdb::CMs)
        sorted_spectra[:gcms] << spectrum
      elsif spectrum.instance_of?(Specdb::MsMs)
        if spectrum.spectrum_type =~ /^LC-MS/
          sorted_spectra[:lcms] << spectrum
        else
          sorted_spectra[:msms] << spectrum
        end
      elsif spectrum.instance_of?(Specdb::NmrOneD)
        sorted_spectra[:nmr1d] << spectrum
      elsif spectrum.instance_of?(Specdb::NmrTwoD)
        sorted_spectra[:nmr2d] << spectrum
      end
    end

    sorted_spectra
  end

  def get_spectra_description(spectrum, is_simulated)
    if spectrum.class.name == "ChemicalShiftSubmission"
      types = spectrum.chemical_shift_submission_meta_data.spectrum_type&.split('-') || ['NA', 'NA']
      unless spectrum.chemical_shift_submission_meta_data.spectrometer_frequency.nil?
         solvent = subscript_solvent_helper(spectrum.chemical_shift_submission_meta_data.solvent)
      end
      unless spectrum.chemical_shift_submission_meta_data.spectrometer_frequency.nil?
        frequency = spectrum.chemical_shift_submission_meta_data.spectrometer_frequency
      end
    elsif spectrum.class.name == 'ExternalNmrSubmission'
      dimension = spectrum.f2_nucleus.present? ? '2D' : '1D'
      types = spectrum.f2_nucleus.present? ? [dimension, spectrum.f1_nucleus.to_s.gsub('_', ''), spectrum.f2_nucleus.to_s.gsub('_', '')] : [dimension, spectrum.f1_nucleus.to_s.gsub('_', '')]
      solvent = spectrum.solvent
      frequency = spectrum.spectrometer_frequency.gsub(/"|\[|\]/, '').to_f.round.to_s + ' MHz'
    else
      types = spectrum.submission_meta_data.spectrum_type&.split('-') || ['NA', 'NA']
      unless spectrum.submission_meta_data.spectrometer_frequency.nil?
        solvent = subscript_solvent_helper(spectrum.submission_meta_data.solvent)
      end
      unless spectrum.submission_meta_data.spectrometer_frequency.nil?
        frequency = spectrum.submission_meta_data.spectrometer_frequency
      end
    end
    dimension = types[0]
    new_description = ""
    if dimension == '1D'
      new_description += "#{types[1].gsub(/(\d+)/, '<sup>\0</sup>')} NMR Spectrum "
    elsif dimension == '2D'
      new_description +=
        "[#{types[1].gsub(/(\d+)/, '<sup>\0</sup>')}, #{types[2].gsub(/(\d+)/, '<sup>\0</sup>')}] NMR Spectrum "
    end
    new_description += "("
    new_description += "#{dimension}, " unless dimension.nil?
    new_description += "#{frequency}, " unless frequency.nil?
    new_description += "#{solvent}, " unless solvent.nil?
    new_description += is_simulated ? "simulated" : "experimental"
    new_description += ")"
    new_description.html_safe
  end

  def get_download_files
    submission_obj = if @submission.nil?
                       @chemical_shift_submission
                     else
                       @submission
                     end

    url_path = File.join('downloads', submission_obj.user_session_id.to_s)
    base_path = File.join('public', url_path)
    downloads = {}
    if File.exist? Rails.root.join(base_path, "#{submission_obj.id}_#{@natural_product.np_mrd_id}_#{submission_obj.user_id}.jdx")
      downloads['jcamp'] = File.join(
        url_path,
        "#{submission_obj.id}_#{@natural_product.np_mrd_id}_#{submission_obj.user_id}.jdx"
      )
    end


    if File.exist? Rails.root.join(base_path, "#{submission_obj.id}_#{@natural_product.np_mrd_id}_#{submission_obj.user_id}_assignment_score.svg")
      downloads['validation'] = File.join(
        url_path,
        "#{submission_obj.id}_#{@natural_product.np_mrd_id}_#{submission_obj.user_id}_assignment_score.svg"
      )
    end

    spectrum_type = if @submission.nil?
                      submission_obj.chemical_shift_submission_meta_data.spectrum_type
                    else
                      submission_obj.submission_meta_data.spectrum_type
                    end

    spectrum_type_suffix = spectrum_type&.split('-')&.[](1)&.downcase || 'na'

    peak_list = "#{submission_obj.id}_#{@natural_product.np_mrd_id}_" \
                "#{submission_obj.user_id}_" \
                "#{spectrum_type_suffix}_" \
                'peaklist.txt'

    if File.exist? Rails.root.join(base_path, peak_list)
      downloads['peak_list'] = File.join(
        url_path,
        peak_list
      )
    end

    peak_assignments = "#{submission_obj.id}_#{@natural_product.np_mrd_id}_" \
                           "#{submission_obj.user_id}_" \
                           'user_assignmenttable.txt'
    if File.exist? Rails.root.join(base_path, peak_assignments)
      downloads['peak_assignments'] = File.join(
        url_path,
        peak_assignments
      )
    end

    downloads

  end

  def rdkit_generate_3D_structure(smiles)
    error = false
    mol = nil
    message = nil
    begin
      stdin, stdout, stderr = Open3.popen3("python #{Rails.root}/public/python/rdkit_smiles_to_mol.py '#{smiles}'")
      mol = stdout.get(nil).to_s
      puts mol
      error = false
    rescue Exception => e
      message = e.message
      error = true
    end
    [mol, error, message]
  end

  # Adds a sentence based on the kingdom and first reference of the NP
  # @param natural_product the natural product to generate description of
  # @return 0, 1 or 2 sentences
  def update_cs_description(natural_product)
    update_references(natural_product)
    sentence = ''
    sentence += get_origin_sentence(natural_product)
    sentence + get_first_discovered_sentence(natural_product)
  end

  # Returns a sentence containing a link to the oldest documentation of the NP
  # @param natural_product the natural product to search documentation for
  # @return 0 or 1 sentences, depending if it has documentation with valid PubMedIds
  def get_first_discovered_sentence(natural_product)
    sentence = ''
    years = Array[]
    textbooks = natural_product.textbooks
    filtered_articles = natural_product.filtered_articles
    name = natural_product.name

    unless filtered_articles.empty?
      years = filtered_articles.map(&:year)
    end

    if years.empty?
      if textbooks.present?
        textbooks.each do |textbook|
          years.push(textbook.year) if textbook.year.present?
        end
      end
      if years.empty?
        if textbooks[0].present? && !textbooks[0].authors.empty?
          sentence += " #{name} has been documented by #{truncate_authors textbooks[0].authors}."
        end
      else
        textbook = natural_product.textbooks.where(year: years.min).first
        unless textbook.authors.empty?
          sentence += " #{name} was first documented in #{years.min} (#{truncate_authors textbook.authors})."
        end
      end
    else
      oldest_article = filtered_articles.min_by(&:year)
      if oldest_article.present?
        pmid = oldest_article.pubmed_id
        unless pmid.blank?
          sentence += " #{name} was first documented in #{oldest_article.year}"
          sentence += " (PMID: #{link_to(pmid, "https://pubmed.ncbi.nlm.nih.gov/#{pmid}")})."
        end
      end
    end
    sentence
  end

  def truncate_authors(orig_authors)
    unless orig_authors.include? 'et al.'
      author_list = orig_authors.split ','
      first_author = author_list[0]
      return "#{first_author.split(' ').last.tr('.', '')}, et al." if author_list.length >= 3
    end
    orig_authors
  end

  # Generates a sentence based on the origin if it has one
  # @param natural_product the natural product to generate description of
  # @return 0 or 1 sentence, depending if it has an origin
  def get_kingdom_sentence(natural_product)
    sentence = ''
    return '' if natural_product.origin.nil?

    taxons = natural_product.origin.split(';')
    if taxons.length == 1
      return '' unless valid_taxon?(taxons[0])

      sentence += " #{natural_product.name} is found in the kingdom #{natural_product.origin}."
    else
      sentence += " #{natural_product.name} is found in the kingdoms "
      taxons.each_with_index do |taxon, index|
        return '' unless valid_taxon?(taxon)

        sentence += if index == taxons.length - 2
                      " #{taxon} "
                    elsif index == taxons.length - 1
                      "and #{taxon}."
                    else
                      "#{taxon}, "
                    end
      end
      sentence.nil? ? '' : sentence
    end
  end

  # Generates a sentence based on the species origin if it has one
  # @param natural_product the natural product to generate description of
  # @return 0 or 1 sentence, depending if it has a species mapping
  def get_origin_sentence(natural_product)
    species_mappings = natural_product.ordered_species_mappings
    sentence = ''
    return '' if species_mappings.blank?

    species_mappings = species_mappings.reject { |species| species.original_origin.blank? }
    return '' if species_mappings.blank?

    if species_mappings.length == 1
      sentence += if species_mappings[0].original_origin[-1] == '.'
                    " #{natural_product.name} is found in  #{species_mappings[0].original_origin}"
                  else
                    " #{natural_product.name} is found in  #{species_mappings[0].original_origin}."
                  end
    else
      sentence += " #{natural_product.name} is found in "
      species_mappings.each_with_index do |species, index|

        sentence += if index == species_mappings.length - 2
                      "#{species.original_origin} "
                    elsif index == species_mappings.length - 1
                      "and #{species.original_origin}."
                    else
                      "#{species.original_origin}, "
                    end
      end
      sentence.nil? ? '' : sentence
    end
  end

  def valid_taxon?(taxon)
    %w[Bacteria Archaea Protozoa Chromista Plantae Fungi Animalia Eubacteria Archaebacteria].include?(taxon)
  end

  def update_and_get_references(natural_product)
    update_references(natural_product)
    all_references = filtered_reference_list(natural_product).to_s

    # Commented out since the code is very clumsy and filtered_reference_list can be modified from cite-this gem instead.
    # I'll leave this here just in case.
    
    external_submissions = ExternalSubmission.where(natural_product_id: natural_product.id)
    external_submissions.each do |external_submission|
      if all_references.blank?
        all_references += "<ol class=\"cite-this-references\">"
      end

      # Remove the </ol> tags from the references, and then just add the doi, pii, and pmid.
      all_references = all_references.gsub(/<\/ol[^>]*>$/, '')

      if external_submission.citation_doi.present?
        string = link_to("DOI: #{external_submission.citation_doi.to_s}", "https://doi.org/#{external_submission.citation_doi.to_s}")
        unless all_references.include?(string)
          all_references += "<li id=\"reference-\">#{string}</li>"
        end
      end

      if external_submission.citation_pii.present?
        string = link_to("PII: #{external_submission.citation_pii.to_s}", "https://www.sciencedirect.com/science/article/abs/pii/#{external_submission.citation_pii.to_s}")
        unless all_references.include?(string)
          all_references += "<li id=\"reference-\">#{string}</li>"
        end
      end

      if external_submission.citation_pmid.present?
        string = link_to("PMID: #{external_submission.citation_pmid.to_s}", "https://pubmed.ncbi.nlm.nih.gov/#{external_submission.citation_pmid.to_s}")
        unless all_references.include?(string)
          all_references += "<li id=\"reference-\">#{string}</li>"
        end
      end

      unless all_references.blank?
        all_references += "</ol>"
      end
    end

    all_references
  end

  def update_references(natural_product)
    if natural_product.referenced? && natural_product.articles.length <= 5
      unique_ids = get_ids_from_moldbi_curation(natural_product.reference) # reference returns a moldbi curation object

      unique_ids.each do |pubmed_id| # reference.references because moldbi returns base obj
        next if natural_product.articles.length >= 5 || pubmed_id.length > 8 || !is_number?(pubmed_id)

        # first_or_create should ideally work, but isn't on the server
        article = CiteThis::Article.where(pubmed_id: pubmed_id).first
        article = CiteThis::Article.where(pubmed_id: pubmed_id).first_or_create if article.blank?

        # both checks are doing the same thing, however it is failing on the server
        if CiteThis::ArticleReferencing.where(article_id: article.id, referencer_id: natural_product.id, referencer_type: 'NaturalProduct').blank? && !natural_product.articles.include?(article)
          natural_product.articles << article
        end
      end
    end
  rescue => e
    Rails.logger.error "Error updating references for natural product #{natural_product.np_mrd_id}: #{e.message}"
  end

  def is_number? string
    true if Float(string) rescue false
  end

  def add_reference_pmids(natural_product)
    sentence = ''
    count = 0
    discovered_sentence = get_first_discovered_sentence(natural_product)
    natural_product.filtered_articles.each do |article|
      next if count >= 6

      pmid = article.pubmed_id
      unless discovered_sentence.include?(pmid.to_s)
        sentence += " (PMID: #{link_to(pmid, "https://pubmed.ncbi.nlm.nih.gov/#{pmid}")})"
        count += 1
      end
    end
    sentence
  end

  def get_ids_from_moldbi_curation(moldbi_curation)
    references = moldbi_curation.references
    citations = moldbi_curation.citations
    reference_ids = []

    # get all of the citations
    citations.each do |citation_group|
      citation_group.each do |citation|
        if citation.key?(:id)
          id = citation[:id]
          # title blank check for cite-this non-empty title validation
          reference_ids.push(id) unless id.blank? || citation[:db] != 'pubmed' || citation[:title].blank?
        end
      end
    end

    references.each do |reference|
      id = reference[:pubmed_id]
      reference_ids.push(id) unless id.blank?
    end

    reference_ids.uniq
  end

  # Checks if a natural product meets criteria for being a NPAtlas compound with valid molfile
  # @param [NaturalProduct] natural_product the NP
  # @return [Boolean] if NP meets criteria or not
  def valid_np_atlas(natural_product)
    return false if natural_product.structure_resource.nil? || natural_product.structure_resource.structure.nil?

    assignments_link = find_assignments @natural_product
    (!natural_product.comment.nil? &&
      natural_product.comment.include?('NPAtlas') &&
      natural_product.structure_resource.structure.include?("\n") ||
      natural_product.id == 1 ||
      Constants::NMR_PREDICTED_COMPOUNDS.include?(@natural_product.np_mrd_id)) &&
      !assignments_link.nil?
  end

  # Finds the first assignment table in the spectra for an NP
  # @param [Array] natural_product the natural product we are trying to find a table for
  # @return [String, nil] the url of the first assignment table and the frequency of the spectrum
  def find_assignments(natural_product)
    natural_product.spectra.each do |spectrum|
      next if spectrum.nil?

      spectrum.documents.each do |document|
        if document.class.name == 'Specdb::NmrOneD::Document' && document.description.downcase == 'peak assignments' &&
          natural_product.id != 1
          return document.url, spectrum.frequency
        elsif natural_product.id == 1 && document.class.name == 'Specdb::NmrOneD::Document' &&
          document.description.downcase == 'for jsmol'
          return document.url, spectrum.frequency
        end
      end
    end
    nil
  end

  # Finds the first dft assignment table in the spectra for an NP
  # @param [Array] natural_product the natural product we are trying to find a table for
  # @return [String, nil] the url of the first dft assignment table and the frequency of the spectrum
  def find_dft_assignments(natural_product)
    natural_product.nmr_one_d_spectra.select{|spec| spec.notes.include? "DFT"}.each do |spectrum|
      next if spectrum.nil?

      spectrum.documents.each do |document|
        if document.class.name == 'Specdb::NmrOneD::Document' && document.description.downcase == 'peak assignments'
          return document.url, spectrum.frequency
        end
      end
    end
    nil
  end

  def hyperlink_sources(natural_product, source_text)
    return nil if source_text.blank?

    case source_text
    when 'The Good Scents Company Information System'
      goodscents_id = natural_product.wrangler_identifiers['goodscents_id']
      if goodscents_id.blank? # shouldn't happen
        return link_to(source_text, 'http://www.thegoodscentscompany.com/')
      else
        return link_to(source_text, "http://www.thegoodscentscompany.com/data/#{goodscents_id}.html")
      end
    when 'KNApSAcK Database'
      knapsack_id = natural_product.wrangler_identifiers['knapsack_id']
      if knapsack_id.blank? # shouldn't happen
        return link_to(source_text, 'http://www.knapsackfamily.com/KNApSAcK_Family/')
      else
        return link_to(source_text,"http://www.knapsackfamily.com/knapsack_core/information.php?word=#{knapsack_id}")
      end
    else
      url_regexp = %r{
        (?:(?:https?|ftp|file):\/\/|www\.|ftp\.)
        (?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|
             [-A-Z0-9+&@#\/%=~_|$?!:,.])*
        (?:\([-A-Z0-9+&@#\/%=~_|$?!:,.]*\)|
              [A-Z0-9+&@#\/%=~_|$])
      }ix
      return source_text.gsub(url_regexp, '<a href="\0">\0</a>') if source_text.include?('http')
    end
    source_text
  end

  # Gets a link to the reference for user submitted data
  # @param [Submission] submission the user's submission
  # @return [String] a link or a plain string depending on the type of literature reference
  def user_submission_reference(submission)
    if submission.instance_of? ChemicalShiftSubmission
      lit_ref = submission.chemical_shift_submission_meta_data.literature_reference
      lit_type = submission.chemical_shift_submission_meta_data.literature_reference_type.to_s.downcase
    else
      lit_ref = submission.submission_meta_data.literature_reference
      lit_type = submission.submission_meta_data.literature_reference_type.to_s.downcase
    end
    case lit_type
    when 'pmid'
      bio_link_out :pubmed, lit_ref, "PubMed: #{lit_ref}"
    when 'doi'
      bio_link_out :doi, lit_ref, "DOI: #{lit_ref}"
    else
      lit_ref
    end
  end

  def is_peak_list(submission)
    if submission.instance_of?(ChemicalShiftSubmission) && submission.peak_list_uuid.present?
      return true
    else
      return false
    end
  end

  def get_spectra_description_index(spectrum)
    case spectrum.class.name
    when 'OneDSpectrum'
      nucleus_new = spectrum.nucleus.gsub(/(\d+)/, '<sup>\0</sup>')
      solvent_new = spectrum.solvent ? (subscript_solvent_specdb spectrum.solvent) : ''
      solvent_new = solvent_new.capitalize if solvent_new.match(/^[a-z]+$/)
      name = if !spectrum.frequency.nil?
               if spectrum.frequency =~ /MHz/
                 nucleus_new.html_safe + " NMR Spectrum (1D, #{spectrum.frequency.round}"
               else
                 nucleus_new.html_safe + " NMR Spectrum (1D, #{spectrum.frequency.round} MHz"
               end
             else
               "#{nucleus_new.html_safe} NMR Spectrum (1D"
             end

      name += ", #{solvent_new}".html_safe unless solvent_new.empty?

      if spectrum.predicted && spectrum.predicted != 0
        "#{name}, predicted)"
      elsif spectrum.simulated && spectrum.simulated != 0
        "#{name}, simulated)"
      else
        "#{name}, experimental)"
      end
    when 'TwoDSpectrum'
      nucleus_x = spectrum.nucleus_x
      nucleus_y = spectrum.nucleus_y
      solvent = spectrum.solvent
      frequency = spectrum.frequency
      nucleus_x_new = nucleus_x ? nucleus_x.gsub(/(\d+)/, '<sup>\0</sup>') : ''
      nucleus_y_new = nucleus_y ? nucleus_y.gsub(/(\d+)/, '<sup>\0</sup>') : ''
      solvent_new = solvent ? (subscript_solvent_specdb solvent) : ''
      spectrum_source = (spectrum.predicted == 1 ? 'predicted' : 'experimental')
      experiment_type = spectrum.experiment_type ? "-#{spectrum.experiment_type}" : ""
      if frequency && solvent
        "[#{nucleus_x_new}, #{nucleus_y_new}]#{experiment_type} NMR Spectrum (2D, #{frequency.round} MHz, " \
        "#{solvent_new}, #{spectrum_source})".html_safe
      elsif frequency
        "[#{nucleus_x_new}, #{nucleus_y_new}]#{experiment_type} NMR Spectrum (2D, #{frequency.round} MHz, #{spectrum_source})".html_safe
      elsif solvent
        "[#{nucleus_x_new}, #{nucleus_y_new}]#{experiment_type} NMR Spectrum (2D, #{solvent}, #{spectrum_source})".html_safe
      else
        "[#{nucleus_x_new}, #{nucleus_y_new}]#{experiment_type} 2D NMR Spectrum (#{spectrum_source})".html_safe
      end
    else
      spectrum
    end
  end

  def get_spectrum_description_by_id(id)
    begin
      spectrum = OneDSpectrum.find(id)
    rescue ActiveRecord::RecordNotFound
      return nil
    end
    get_spectra_description_index(spectrum).html_safe
  end

  # Duplicate of specdb's subscript_solvent method
  def subscript_solvent_specdb(solvent)
    # Map all the solvents that need subscripting to their subscripted versions
    # Add to this map when a new solvent is added that needs subscripting
    subscript_hash = {'C5D5N' => 'C<sub>5</sub>D<sub>5</sub>N'.html_safe,
                      'CD3OD' => 'CD<sub>3</sub>OD'.html_safe,
                      'CD3COOD' => 'CD<sub>3</sub>COOD'.html_safe,
                      'CDCl3' => 'CDCl<sub>3</sub>'.html_safe,
                      'H2O' => 'H<sub>2</sub>O'.html_safe,
                      'CD2Cl2' => 'CD<sub>2</sub>Cl<sub>2</sub>'.html_safe,
                      'CCl3' => 'CCl<sub>3</sub>'.html_safe,
                      'D2O' => 'D<sub>2</sub>O'.html_safe,
                      'CHCL3' => 'CHCL<sub>3</sub>'.html_safe,
                      'C5H5N' => 'C<sub>5</sub>H<sub>5</sub>N'.html_safe,
                      'CD3OH' => 'CD<sub>3</sub>OH'.html_safe,
                      'CHCl3' => 'CHCl<sub>3</sub>'.html_safe,
                      'C6D6' => 'C<sub>6</sub>D<sub>6</sub>'.html_safe,
                      'CH3OD' => 'CH<sub>3</sub>OD'.html_safe,
                      'CD3Cl' => 'CD<sub>3</sub>Cl'.html_safe,
                      'CF3CO2D' => 'CF<sub>3</sub>CO<sub>2</sub>D'.html_safe,
                      'CF3COOD' => 'CF<sub>3</sub>COOD'.html_safe,
                      'CS2' => 'CS<sub>2</sub>'.html_safe,
                      'CCl4' => 'CCl<sub>4</sub>'.html_safe,
                      'CD3CN' => 'CD<sub>3</sub>CN'.html_safe,
                      'C5D6N' => 'C<sub>5</sub>D<sub>6</sub>N'.html_safe,
                      'CF3COOH' => 'CF<sub>3</sub>COOH'.html_safe,
                      'C6H6' => 'C<sub>6</sub>H<sub>6</sub>'.html_safe,
                      'ND3' => 'ND<sub>3</sub>'.html_safe,
                      'Water' => 'H<sub>2</sub>O'.html_safe}

    # Match groups from the hash and replace matching groups with the subscripted version
    re = Regexp.new(subscript_hash.keys.map { |x| Regexp.escape(x) }.join('|'))
    solvent.gsub(re, subscript_hash)
  end

  # Filters backfilled (from moldb) spectra to be displayed on the browse page
  # @param [OneDSpectrum] one_d_relation the one d spectra of corresponding to a natural product
  # @param [TwoDSpectrum] two_d_relation the two d spectra of corresponding to a natural product
  # @return [Hash] a hash containing both relations with applied filters
  def filter_spectra_index(one_d_relation, two_d_relation)
    filter = init_filters
    spectra = {}

    unless filter[:assignment_quality_filter].empty?
      high, low = SpectraFilter.get_high_low_for_assignment(filter[:assignment_quality_filter].map(&:to_sym))
      one_d_relation = one_d_relation.where('assignment_score BETWEEN ? AND ?', low, high)
      two_d_relation = two_d_relation.where('assignment_score BETWEEN ? AND ?', low, high)
    end

    unless filter[:solvent_filter].empty?
      temp_one_d_relation = []
      temp_two_d_relation = []
      one_d_relation.each do |spectrum|
        filter[:solvent_filter].each do |solvent|
          temp_one_d_relation << spectrum if !spectrum.solvent.nil? && spectrum.solvent.downcase.include?(solvent.downcase)
        end
      end
      two_d_relation.each do |spectrum|
        filter[:solvent_filter].each do |solvent|
          temp_two_d_relation << spectrum if !spectrum.solvent.nil? && spectrum.solvent.downcase.include?(solvent.downcase)
        end
      end
      one_d_relation = temp_one_d_relation
      two_d_relation = temp_two_d_relation
    end
    unless filter[:spectra_assessment_filter].empty?
      one_d_relation = one_d_relation.select do |spectrum|
        next unless spectrum.spectra_assessment
        filter[:spectra_assessment_filter].include?(spectrum.spectra_assessment.downcase)
      end
      two_d_relation = two_d_relation.select do |spectrum|
        next unless spectrum.spectra_assessment
        filter[:spectra_assessment_filter].include?(spectrum.spectra_assessment.downcase)
      end
    end

    unless filter[:nuclei_filter].empty?
      one_d_spectra = []
      two_d_spectra = []
      filter[:nuclei_filter].each do |nucleus|
        case nucleus
        when 'one_d_proton'
          one_d_relation.each { |spectrum| one_d_spectra << spectrum if spectrum.nucleus == '1H' }
        when 'one_d_carbon'
          one_d_relation.each { |spectrum| one_d_spectra << spectrum if spectrum.nucleus == '13C' }
        when 'two_d_proton'
          two_d_relation.each do |spectrum|
            two_d_spectra << spectrum if spectrum.nucleus_x == '1H' && spectrum.nucleus_y == '1H'
          end
        when 'two_d_carbon'
          two_d_relation.each do |spectrum|
            two_d_spectra << spectrum if spectrum.nucleus_x == '13C' && spectrum.nucleus_y == '13C'
          end
        when 'hetero_carbon_proton'
          two_d_relation.each do |spectrum|
            two_d_spectra << spectrum if spectrum.nucleus_x == '1H' && spectrum.nucleus_y == '13C'
          end
        end
      end
      one_d_relation = one_d_spectra
      two_d_relation = two_d_spectra
    end

    unless filter[:spectral_types_filter].empty?
      one_d_spectra_type = []
      two_d_spectra_type = []

      filter[:spectral_types_filter].each do |type|
        case type
        when 'predicted'
          one_d_relation.each { |spectrum| one_d_spectra_type << spectrum if spectrum.predicted == true }
          two_d_relation.each { |spectrum| two_d_spectra_type << spectrum if spectrum.predicted == true }
        when 'simulated'
          one_d_relation.each { |spectrum| one_d_spectra_type << spectrum if spectrum.simulated == true }
          # 2D Spectra does not have a simulated field yet because there is no simulated data
        when 'experimental'
          one_d_relation.each do |spectrum|
            unless (spectrum.predicted && spectrum.predicted != 0) || (spectrum.simulated && spectrum.simulated != 0)
              one_d_spectra_type << spectrum
            end
          end
          two_d_relation.each { |spectrum| two_d_spectra_type << spectrum if spectrum.predicted != true }
        end
      end
      one_d_relation = one_d_spectra_type
      two_d_relation = two_d_spectra_type
    end

    spectra[:nmr1d] = one_d_relation
    spectra[:nmr2d] = two_d_relation
    spectra
  end

  # Grabs and filters all user submitted data to be displayed on the browse page
  # @param [NaturalProduct] natural_product the NP to get user submitted spectra on
  # @return [Hash] a hash with applicable 1D and 2D spectra
  def get_user_submitted_spectra(natural_product)
    user_submitted_spectra = {}
    one_d_spectra = []
    two_d_spectra = []
    filter = init_filters

    submissions = natural_product.valid_submissions
    cs_submissions = natural_product.valid_cs_submissions

    cs_submissions.each do |cs|
      unless filter[:solvent_filter].empty?
        next unless filter[:solvent_filter].include? cs.chemical_shift_submission_meta_data.solvent.downcase
      end
      next unless filter[:spectra_assessment_filter].empty?
      unless filter[:spectral_types_filter].empty?
        next unless filter[:spectral_types_filter].include? 'simulated'
      end

      unless filter[:assignment_quality_filter].empty?
        high, low = SpectraFilter.get_high_low_for_assignment(filter[:assignment_quality_filter].map(&:to_sym))
        assignment_score = get_assignment_score(cs)
        next if assignment_score < low || assignment_score > high || assignment_score.nil?
      end

      unless filter[:nuclei_filter].empty?
        skip_submission = false
        filter[:nuclei_filter].each do |nuclei|
          case nuclei
          when 'one_d_proton'
            skip_submission = true unless cs.chemical_shift_submission_meta_data.spectrum_type.downcase.include?('1d') &&
              cs.chemical_shift_submission_meta_data.spectrum_type.downcase.include?('1h')
          when 'one_d_carbon'
            skip_submission = true unless cs.chemical_shift_submission_meta_data.spectrum_type.downcase.include?('1d') &&
              cs.chemical_shift_submission_meta_data.spectrum_type.downcase.include?('13c')
          when 'two_d_proton'
            skip_submission = true unless cs.chemical_shift_submission_meta_data.spectrum_type.downcase.include?('2d') &&
              cs.chemical_shift_submission_meta_data.spectrum_type.downcase.include?('1h-1h')
          when 'two_d_carbon'
            skip_submission = true unless cs.chemical_shift_submission_meta_data.spectrum_type.downcase.include?('2d') &&
              cs.chemical_shift_submission_meta_data.spectrum_type.downcase.include?('13c-13c')
          when 'hetero_carbon_proton'
            skip_submission = true unless cs.chemical_shift_submission_meta_data.spectrum_type.downcase.include?('2d') &&
              cs.chemical_shift_submission_meta_data.spectrum_type.downcase.include?('1h-13c')
          end
        end
        next if skip_submission
      end
      cs_link = link_to "#{get_spectra_description(cs, true)}".html_safe,
                        spectrum_view_natural_product_path(cs), 'data-no-turbolink' => true
      if cs_link.include? '1D'
        one_d_spectra << cs_link
      else
        two_d_spectra << cs_link
      end
    end
    submissions.each do |s|
      unless filter[:solvent_filter].empty?
        next unless filter[:solvent_filter].include? s.submission_meta_data.solvent.downcase
      end
      unless filter[:spectral_types_filter].empty?
        next unless filter[:spectral_types_filter].include?('simulated') ||
          filter[:spectral_types_filter].include?('experimental')
      end
      unless filter[:assignment_quality_filter].empty?
        high, low = SpectraFilter.get_high_low_for_assignment(filter[:assignment_quality_filter].map(&:to_sym))
        assignment_score = get_assignment_score(s)
        next if assignment_score < low || assignment_score > high || assignment_score.nil?
      end
      unless filter[:nuclei_filter].empty?
        skip_submission = false
        filter[:nuclei_filter].each do |nuclei|
          case nuclei
          when 'one_d_proton'
            skip_submission = true unless s.submission_meta_data.spectrum_type.downcase.include?('1d') &&
              s.submission_meta_data.spectrum_type.downcase.include?('1h')
          when 'one_d_carbon'
            skip_submission = true unless s.submission_meta_data.spectrum_type.downcase.include?('1d') &&
              s.submission_meta_data.spectrum_type.downcase.include?('13c')
          when 'two_d_proton'
            skip_submission = true unless s.submission_meta_data.spectrum_type.downcase.include?('2d') &&
              s.submission_meta_data.spectrum_type.downcase.include?('1h-1h')
          when 'two_d_carbon'
            skip_submission = true unless s.submission_meta_data.spectrum_type.downcase.include?('2d') &&
              s.submission_meta_data.spectrum_type.downcase.include?('13c-13c')
          when 'hetero_carbon_proton'
            skip_submission = true unless s.submission_meta_data.spectrum_type.downcase.include?('2d') &&
              s.submission_meta_data.spectrum_type.downcase.include?('1h-13c')
          end
        end
        next if skip_submission
      end
      if filter[:spectral_types_filter].empty? ||
        (!filter[:spectral_types_filter].empty? && filter[:spectral_types_filter].include?('simulated'))
        s_link = link_to "#{get_spectra_description(s, true)}".html_safe,
                         submission_spectrum_view_natural_product_path(:spectrum_type => 'simulation', :id => s.id),
                         'data-no-turbolink' => true
        if filter[:spectra_assessment_filter].empty?
          if s_link.include? '1D'
            one_d_spectra << s_link
          else
            two_d_spectra << s_link
          end
        end
      end
      s.nmr_submissions.each do |file|
        next unless filter[:spectral_types_filter].empty? ||
          (!filter[:spectral_types_filter].empty? && filter[:spectral_types_filter].include?('experimental'))
        unless filter[:spectra_assessment_filter].empty?
          next unless !file.spectra_assessment.nil? && filter[:spectra_assessment_filter]
                                                         .map{ |assessment| assessment.humanize(capitalize: false)}
                                                         .include?(file.spectra_assessment.downcase)
        end
        s_experimental_link = link_to "#{get_spectra_description(s, false)}".html_safe,
                                      submission_spectrum_view_natural_product_path(:spectrum_type => 'real',
                                                                                    :id => s.id,
                                                                                    :spectrum_id => file.id),
                                      'data-no-turbolink' => true
        if s_experimental_link.include? '1D'
          one_d_spectra << s_experimental_link
        else
          two_d_spectra << s_experimental_link
        end
      end
    end

    user_submitted_spectra[:nmr1d] = one_d_spectra
    user_submitted_spectra[:nmr2d] = two_d_spectra
    user_submitted_spectra
  end

  # Sets up array for use to filter the spectra displayed on the browse page
  # @return [Hash] a hash with all the arrays containing any filters that were selected
  def init_filters
    solvents = %w[Water D2O CDCl3 CD3OD C5D5N DMSO acetone]
    spectra_assessments = %w[excellent satisfactory poor not_usable very_low acceptable very_good good]
    nuclei = %w[one_d_proton one_d_carbon two_d_proton two_d_carbon hetero_carbon_proton]
    spectral_types = %w[predicted experimental simulated]
    assignment_qualities = %w[level_one level_two level_three level_four]
    solvent_filter = []
    spectra_assessment_filter = []
    spectral_types_filter = []
    nuclei_filter = []
    assignment_quality_filter = []

    solvents.each do |solvent|
      solvent_filter << solvent if params.has_key? solvent
    end

    solvent_filter << "H2O" if solvent_filter.include? "Water"

    spectra_assessments.each do |assessment|
      spectra_assessment_filter << assessment if params.has_key? assessment
    end

    nuclei.each do |nucleus|
      nuclei_filter << nucleus if params.has_key? nucleus
    end

    spectral_types.each do |type|
      spectral_types_filter << type if params.has_key? type
    end

    assignment_qualities.each do |quality|
      assignment_quality_filter << quality if params.has_key? quality
    end

    {nuclei_filter: nuclei_filter, solvent_filter: solvent_filter, spectra_assessment_filter:  spectra_assessment_filter,
     assignment_quality_filter: assignment_quality_filter, spectral_types_filter: spectral_types_filter}
  end

  # Calculates the assignment score of a user submission by averaging the score of each atom
  # @param [Submission, ChemicalShiftSubmission] submission the user submitted entry
  # @return [Float] the calculated assignment score
  def get_assignment_score(submission)
    if submission.class.name == "Submission"
      shifts = ChemicalShift.where(submission_id: submission.id)
    else
      shifts = ChemicalShift.where(chemical_shift_submission_id: submission.id)
    end
    return nil if shifts.empty?
    assignment_score = 0
    shifts.each do |shift|
      next if shift.assignment_score.nil?
      assignment_score += Float(shift.assignment_score)
    end
    Float(assignment_score / shifts.length)
  end


  # Determines if the NP card needs to display and attribution
  # @param [NaturalProduct] natural_product the np that is going to be shown
  # @return [Boolean] true if NP card needs an attribution
  def needs_attribution(natural_product)
    natural_product.comment.present? &&
      (natural_product.comment.include?('JEOL') || natural_product.comment.include?('NPAtlas') ||
        natural_product.comment.include?('BMRB'))
  end

  def generate_spectra_link(result, ext_nmr, chem_shift)
    if ext_nmr.present?
      nah link_to "#{get_spectra_description(ext_nmr, false)}".html_safe,
                      "/natural_products/#{ext_nmr.id}/external_spectrum_view", 'data-no-turbolink' => true
    elsif chem_shift.present?
      nah link_to "#{get_spectra_description(chem_shift, false)}".html_safe,
                      "/natural_products/#{chem_shift.id}/spectrum_view", 'data-no-turbolink' => true
    else
      nah link_to "#{get_spectrum_description_by_id(result.id)}".html_safe,
                      "/spectra/nmr_one_d/#{result.id}", 'data-no-turbolink' => true
    end
  end

  def generate_compare_spectra_link(result, ext_nmr, chem_shift)
    if ext_nmr.present? || chem_shift.present?
      link_to "Compare Spectrum", "#", class: "compare btn-card btn-compare",
        'data-spectrum_id' => result.id, 'spec_name' => "nmr_one_d"
    else
      link_to "Compare Spectrum", "#", class: "compare btn-card btn-compare",
        'data-spectrum_id' => result.id, 'spec_name' => "nmr_one_d"
    end
  end

  def embargo_alert(natural_product)
    if natural_product.parent_accession_number.present?
      content_tag(:div, class: 'alert alert-danger fixed-top', style: 'font-size: 1.5rem; padding: 1.5rem; font-weight: bold;') do
        raw("This compound has been merged with an existing record. Please view the record: #{link_to(natural_product.parent_accession_number, natural_product_path(natural_product.parent_accession_number))}")
      end
    elsif !natural_product.exported?
      content_tag(:div, class: 'alert alert-danger fixed-top', style: 'font-size: 1.5rem; padding: 1.5rem; font-weight: bold;') do
        raw('This compound is embargoed and not publicly released yet. Information here is subject to change.')
      end
    end
  end

  def format_spectrum_type(cs)
    spectrum_type = cs.chemical_shift_submission_meta_data.spectrum_type
    return nah if spectrum_type.nil?

    "#{spectrum_type.include?('1D') ? '1D' : '2D'} NMR"
  end

  def nmrml_file_exists?(submission, natural_product, chemical_shift_submission)
    if submission.nil?
      # Check for chemical shift submission
      file_name = "#{chemical_shift_submission.id}_#{natural_product.np_mrd_id}_#{chemical_shift_submission.user_id}.nmrML"
      abs_path = Rails.root.join('public', 'downloads', chemical_shift_submission.user_session_id.to_s, file_name.to_s).to_s
    else
      # Check for regular submission
      file_name = "#{submission.id}_#{natural_product.np_mrd_id}_#{submission.user_id}.nmrML"
      abs_path = Rails.root.join('public', 'downloads', submission.user_session_id.to_s, file_name.to_s).to_s
    end
    # Return true if the file exists
    File.exist?(abs_path)
  end
end
