module ExternalDepositionsHelper
  # Log and Print error details for external submission errors during depositions
  def handle_submission_error(error_message, inchikey, error, es=nil, headers=nil)
    Rails.logger.error "#{error_message} #{inchikey} #{es&.submission_uuid} for External Deposition Job"
    full_backtrace = error.message + "\n" + error.backtrace.join("\n")
    Rails.logger.error full_backtrace

    es.error_details = full_backtrace if es.present?
  end
end
