class EmbargoReleaseJob < ApplicationJob
  queue_as :default

  # Check if external submission embargo falls on today and update all natural_products, peak_lists
  # and external nmr submissions associated with it
  def perform
    current_date = Date.today

    ExternalSubmission.find_each do |submission|
      # Make sure the compound is not just a name or inchikey match
      if submission.embargo_date == current_date && !ExternalSubmission::FOR_REVIEW.include?(submission.npmrd_match_status)
        begin
          product = submission.natural_product
          if !product.export && submission.compound_embargo_release_ready
            product.update!(export: true)
          end

          submission.chemical_shift_submissions.find_each do |shift|
            if !shift.export && shift.peak_list_embargo_release_ready
              shift.update!(export: true)
            end
          end

          submission.external_nmr_submissions.find_each do |nmr_submission|
            if !nmr_submission.export && nmr_submission.spectrum_embargo_release_ready
              nmr_submission.update!(export: true)
            end
          end

          Rails.logger.info "Embargo Release Successful for Submission : #{submission.submission_uuid}"
        rescue => e
          Rails.logger.error "Embargo Release Job Failed for Submission: #{submission.submission_uuid} - Error: #{e.message}"
          Rails.logger.error "Backtrace: #{e.backtrace.join("\n")}"
        end
      end
    end
  end
end
