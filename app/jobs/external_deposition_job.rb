require 'zip'
require 'open3'

class ExternalDepositionJob < ApplicationJob
  queue_as :default

  include Shared::NaturalProductLoader
  include ExternalDepositionsHelper

  def perform(submission_data)
    begin
      start_deposition(submission_data)
    rescue => e
      es = ExternalSubmission.find_or_create_by!(submission_json: submission_data) 
      handle_submission_error("Submission Error", "", e, es)
      es.save!
    end
  end

  # Processes new submissions received from the Linington Application and deposits them into the database.
  # This function handles the complete workflow of processing each submission, including downloading data,
  # extracting contents, processing submissions, and finally updating the database.
  #
  # @param [Array<Hash>] submission_data 
  #   An array of hashes where each hash represents a submission's compound data.
  #   The structure of each hash is expected to match the format required by the `process_submission` method.
  #
  # The function works through the following steps:
  #   1. Iterates over each submission in the submission_data.
  #   2. Calls `process_submission` for each submission, which handles the individual processing of each submission/compound.
  #   3. Collects reports from each processed submission into `all_reports`.
  #   4. Creates submission collections based on the processed data.
  #   5. Attempts to post the status of report ingestion using `post_report_ingestion_status`.
  #
  # The `submission_uuid` variable is updated with each iteration and used for error reporting.
  # In its current implementation, `submission_uuid` holds the UUID of the last processed submission.
  #
  # Note: 
  # - This function assumes that `Rails.application.secrets.deposition_api_token` is correctly set and available.
  # - The function is dependent on the correct structure of `submission_data` and expects specific keys and data formats.
  #
  def start_deposition(submission_data)
    all_reports = []
    submission_collections = Hash.new { |hash, key| hash[key] = [] }
    submission_uuid = ""

    submission_data&.each do |compound_json|
      submission_uuid = process_submission(submission_collections, all_reports, compound_json)
    end

    create_submission_collections(submission_collections)
    post_report_ingestion_status(Rails.application.secrets.deposition_api_token, all_reports, submission_uuid)
  end

  # Processes an individual submission from the Linington Application, handling various stages including
  # downloading the submission file, extracting it, depositing the data into the database, and handling errors.
  #
  # @param [Hash] submission_collections 
  #   A hash that groups external submissions by depositor email. This is used to create collections of submissions.
  #
  # @param [String] filename 
  #   The name of the file to be downloaded and processed. Typically, this is a zip file containing the submission data.
  #
  # @param [Array] all_reports 
  #   An array that accumulates reports generated from each processed submission. These reports typically contain
  #   information about the success or failure of processing each submission.
  #
  # @param [Hash] compound_json 
  #   A hash containing data for the current submission. It's expected to include keys and values necessary for processing,
  #   such as 'submission_uuid' and 'inchikey'.
  #
  # @return [String]
  #   Returns the UUID of the submission. This can be used for tracking or logging purposes.
  #
  # The function operates as follows:
  #   1. Initializes a new `ExternalSubmission` object or finds existing one from submission_uuid
  #   2. Extracts the `submission_uuid` from the `compound_json`.
  #   3. Creates a temporary directory for processing the submission files.
  #   4. Downloads the submission file and extracts it to the temporary directory.
  #   5. Processes the submission data (through the `deposit` method) and adds any generated reports to `all_reports`.
  #   6. Adds the external submission to `submission_collections` if a depositor email is present.
  #   7. Handles any exceptions that may occur during processing and logs them in `all_reports`.
  #   8. Ensures cleanup by saving the submission and copying any failed submission data to a specified directory.
  #
  def process_submission(submission_collections, all_reports, compound_json)
    compound_errors = []
    begin
      external_submission, compound_uuid, submission_uuid = find_create_external_submission(compound_json)
     
      # Generate a unique directory name for this job instance 
      # Proceed within this directory without changing the global process directory
      unique_dir = "submission_#{submission_uuid}_#{compound_uuid}"
      work_dir = Rails.root.join('public', 'system', 'external_depositions', unique_dir)
      FileUtils.mkdir_p(work_dir)
  
      error = download_file(compound_json, external_submission, work_dir)
      submission_path = find_submission_path(work_dir, compound_json['inchikey'])
  
      report_data = return_or_deposit(submission_path, compound_json, external_submission, compound_errors)
      report_data['compound_errors'] << { type: 'download_error', message: error } if error
      all_reports << report_data
  
      if external_submission.depositor_email.present?
        submission_collections[external_submission.depositor_email] << external_submission
      end
    rescue => e
      handle_submission_error("Compound Deposition Error", compound_json['inchikey'], e, external_submission)
      compound_errors << { type: 'submission_error', message: e.message }
      all_reports << { "compound_errors": compound_errors, submission_uuid: submission_uuid }
    ensure
      external_submission.submission_json = compound_json
      save_external_submission(external_submission, compound_errors)
      if external_submission.error_details.present?
        failed_submissions_directory = Rails.root.join('public', 'system', 'failed_submissions', compound_json['inchikey'].to_s)
        FileUtils.mkdir_p failed_submissions_directory
        FileUtils.cp_r("#{work_dir}/.", failed_submissions_directory)
      end
      FileUtils.remove_dir(work_dir) # Remove current working directory after job is done
    end
    return submission_uuid
  end
  
  def find_create_external_submission(compound_json)
    submission_uuid = compound_json['submission']['submission_uuid']
    compound_uuid = compound_json['submission']['compound_uuid']
    if submission_uuid.present?
      es = ExternalSubmission.find_or_create_by(submission_uuid: submission_uuid, compound_uuid: compound_uuid)
      es.error_details = nil
    else
      es = ExternalSubmission.new()
    end
    return es, compound_uuid, submission_uuid
  end

  def return_or_deposit(submission_path, compound_json, external_submission, compound_errors)
    if ExternalSubmission::FOR_REVIEW.include?(external_submission.npmrd_match_status)
      unless external_submission.quality_report.present?
        raise StandardError, "External Depostion Needs Review due to match by just Inchikey or Name; No Quality Report present"
      end
      report_data_json = File.open(external_submission.quality_report.path).read
      return JSON.parse(report_data_json)
    else
      return deposit(submission_path, compound_json, external_submission, compound_errors)
    end
  end

  # Groups and organizes individual submissions into collections based on the depositor's email. 
  # For each group of submissions associated with a unique email, a new `ExternalSubmissionCollection` 
  # is created. Each submission within a group is then linked to this collection. Additionally, 
  # this function is responsible for updating each collection with the user information and 
  # performing post-collection creation actions like generating a DOI and sending completion emails.
  #
  # @param [Hash] submission_collections 
  #   A hash that groups external submissions by depositor email. Each key is an email address, 
  #   and its associated value is an array of `ExternalSubmission` objects that belong to the depositor.
  #
  # The function operates as follows:
  #   1. Iterates through each key-value pair in the `submission_collections` hash.
  #   2. Creates a new `ExternalSubmissionCollection` for each unique email.
  #   3. Updates each submission in the array to link it to the newly created collection.
  #   4. Updates the collection with user information from the submission.
  #   5. TODO: Implement functionality to generate a DOI for each collection.
  #   6. TODO: Implement functionality to send a completion email for each collection.
  #
  # Note:
  # - The function assumes that `ExternalSubmissionCollection.create` and `submission.update` 
  #   methods are defined and handle their respective operations correctly.
  # - Future implementations should address the TODO items for DOI generation and sending completion emails.
  #
  def create_submission_collections(submission_collections)
    submission_collections.each do |email, submissions|
      # TODO: Generate DOI for the collection
      collection = ExternalSubmissionCollection.create({user_email: email})
      submissions.each do |submission|
        submission.update({external_submission_collection: collection})
        collection.update({user: submission.user})
      end
      # TODO: send_completed_email collection
    end
  end

  # Determines the correct path for a submission within a specified working directory. 
  # This function is particularly useful when dealing with extracted zip files, as it 
  # navigates the directory structure to find where the submission's files are located.
  #
  # @param [String] work_dir 
  #   The directory in which the submission files are expected to be found. Typically, 
  #   this is the directory where the zip file containing the submission data was extracted.
  #
  # @param [String] submission_uuid 
  #   The unique identifier for the submission. This is used primarily for error logging purposes.
  #
  # @return [String]
  #   Returns the path to the directory where the submission's files are located. 
  #   In most cases, this will be either the `work_dir` itself or a subdirectory within it.
  #
  # The function operates as follows:
  #   1. Initializes the `submission_path` to `work_dir`, assuming initially that the submission files 
  #      are directly within `work_dir`.
  #   2. Counts the number of items in the `work_dir` to determine the directory structure.
  #   3. If there is exactly one item (excluding system files and the zip file), 
  #      it assumes this item is a directory containing the submission and updates `submission_path` accordingly.
  #   4. Handles any exceptions that may occur during this process and logs them using `handle_submission_error`.
  #
  # Note:
  # - The function assumes a specific structure of the extracted contents - either the files are directly 
  #   in `work_dir` or within a single subdirectory.
  #
  def find_submission_path(work_dir, inchikey)
    # Constructs the path to the directory where the data might be located
    temp_path = File.join(work_dir, "filtered_data_#{inchikey}")
  
    # Checks if temp_path exists and is a directory
    if File.directory?(temp_path)
      # If it exists and is a directory, returns temp_path
      return temp_path
    else
      # If it doesn't exist or isn't a directory, returns work_dir
      return work_dir
    end
  end  

  # TODO: Legacy function to find the correct work folder
  def find_submission_path_legacy(work_dir)
    submission_path = work_dir # Case when all the folders of the zipped file gets extracted into work dir
    begin
      item_count = 0
      Dir.foreach(work_dir) do |item|
        # Skip the current and parent directory entries and also depositions.zip created before
        next if item == '.' || item == '..' || item == '__MACOSX' || item == 'depositions.zip'
        item_count += 1
      end
      
      if item_count == 1 # Case where there is a top_level directory under zip
        Dir.foreach(work_dir) do |folder|
          next if folder[0] == '.' or folder == 'depositions.zip' or folder == '__MACOSX'
          submission_path = File.join(work_dir, folder) # Find the right folder
        end
      end
    rescue => e
      handle_submission_error("Submission Path Error", submission_uuid, e)
    end
    return submission_path
  end

  # Downloads and unzips NMR data files based on a provided download URL found in the `compound_json`.
  # The function handles the downloading of files such as 'fid' and 'procpar' from the specified URL, 
  # writes them to a file named `filename`, and then unzips the contents for further processing.
  #
  # @param [Hash] compound_json 
  #   A hash containing the data for the current submission. This includes the URL for the NMR data file,
  #   which is used to download the file.
  #
  # @param [String] filename 
  #   The name of the file to be created for the downloaded content. Typically, this is a zip file.
  #
  # @param [ExternalSubmission] external_submission 
  #   An object representing the external submission being processed. 
  #
  # @return [String, Boolean]
  #   Returns `false` if the file is downloaded and unzipped successfully. 
  #   If an error occurs, the error message is returned.
  #
  # The function operates as follows:
  #   1. Retrieves the download URL from `compound_json`.
  #   2. Opens the specified `filename` for writing and downloads the file from the URL.
  #   3. Unzips the downloaded file to the current directory.
  #   4. Handles any exceptions that may occur during the download or unzip process and logs them using 
  #      `handle_submission_error`. In case of an error, the error message is returned.
  #
  # Note:
  # - The function uses system-level unzip command, which requires the appropriate permissions and environment.
  #
  def download_file(compound_json, external_submission, work_dir)
    file_path = File.join(work_dir, 'depositions.zip')
    begin
      download_url = compound_json['nmr_data']['experimental_data']['nmr_data_download_link']
      # Ensure filename includes the full path to work_dir
      File.open(file_path, 'wb') do |file|
        file.write(open(download_url).read)
      end
      # Specify the output directory for the unzip operation
      system("unzip -o #{file_path.to_s.shellescape} -d #{work_dir.to_s.shellescape}")
    rescue => e
      handle_submission_error("NMR File Download Error", compound_json['inchikey'], e, external_submission)
      return e.message
    end
    return false
  end

  # Posts the ingestion status of a report to an external deposition API. The function sends a JSON payload 
  # containing the data of the processed submissions to a specified endpoint, using the provided bearer token 
  # for authorization. It logs the process and the response from the API for monitoring and debugging purposes.
  #
  # @param [String] bearer_token
  #   The authorization token used to authenticate the request to the external API.
  #
  # @param [Hash, Array] data
  #   The data to be sent in the request body. This typically includes information about the processed submissions,
  #   such as status, errors, and other relevant metadata.
  #
  # @param [String] submission_uuid
  #   The unique identifier for the submission being reported. This is used primarily for logging purposes.
  #
  # The function operates as follows:
  #   1. Logs the submission UUID and the data to be sent.
  #   2. Constructs the URI for the API endpoint using environment variables.
  #   3. Creates the HTTP request object, enabling SSL/TLS for HTTPS.
  #   4. Sets the request headers, including the Content-Type and Authorization with the bearer token.
  #   5. Converts the data to JSON format and adds it to the request body.
  #   6. Makes the POST request to the API and captures the response.
  #   7. Logs the result of the API call, including the response body.
  #
  # Note:
  # - The function uses `Net::HTTP` for making HTTP requests, and it's assumed that the necessary environment variables
  #   for the API endpoint are correctly set in `ENV`.
  #
  def post_report_ingestion_status(bearer_token, data, submission_uuid)
    begin
      Rails.logger.info("Quality Report JSON for Submission UUID: #{submission_uuid} #{data}")
      uri = URI("#{ENV['deposition_api']}#{ENV['report_ingestion_endpoint']}")

      http = Net::HTTP.new(uri.host, uri.port)
      http.use_ssl = true if uri.scheme == 'https'  # Enable SSL/TLS when using HTTPS
      request = Net::HTTP::Post.new(uri, 'Content-Type' => 'application/json', 'Authorization' => "Bearer #{bearer_token}")
    
      request.body = data.to_json
      # Make the POST request
      response = http.request(request)
      Rails.logger.info("Result of Quality Report API call for Submission UUID: #{submission_uuid} #{response.body}")
    rescue => e
      handle_submission_error("Quality Report API Error", submission_uuid, e)
    end
  end

  def generate_1d_nmrml(nmr_submission, magmet_json, submission_uuid, submission_path)
    parent_dir = File.expand_path('..')
    File.rename parent_dir, parent_dir.gsub(' ', '') # Temp solution to deal with spaces in folder names
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    script      = Rails.root.join('backend', 'nmr_ml/nmrml_creator.py')
    submission_path ||= Pathname.new(nmr_submission.fid_file.path).dirname
    o = submission_path

    proton_dimension = false
    if nmr_submission.nmr_spectrum_type == '1D-1H' || nmr_submission.nmr_spectrum_type == '1D-1H-DEPT90'
      proton_dimension = true
    end

    external_submission = nmr_submission.external_submission
    natural_product = external_submission.natural_product

    nmrml_cmd = "#{python_path} #{script}"
    nmrml_cmd << " -mol #{o}/rdkit_mol_for_nmrpred.mol"
    nmrml_cmd << " -output_path #{o}/#{nmr_submission.id}.nmrML"
    nmrml_cmd << " -name '#{natural_product.name}'"
    nmrml_cmd << " -genus #{external_submission.origin_genus}"
    nmrml_cmd << " -species '#{external_submission.origin_species}'"
    nmrml_cmd << " -freq #{nmr_submission.formatted_frequency}"
    # nmrml_cmd << " -ref #{}" #TODO: Add references
    nmrml_cmd << ' -standard TMS' #TODO: Change this when Linington's tool can handle the cs reference
    nmrml_cmd << " -temp #{nmr_submission.temperature}"
    nmrml_cmd << " -spec_type #{nmr_submission.spectrum_type}"
    nmrml_cmd << " -pl #{o}/external_submission_1h_peaklist.txt"
    nmrml_cmd << " -param_path #{o}/external_submission_1h_params.txt"
    nmrml_cmd << " -fid_path #{o}/external_submission_1h_fid.txt -spec_path #{o}/external_submission_1h_spectrum.txt"
    nmrml_cmd << " -13C_fid_path #{o}/external_submission_13c_fid.txt -13C_spec_path #{o}/external_submission_c13_spectrum.txt"
    nmrml_cmd << " -13C_param_path #{o}/external_submission_13c_params.txt"
    nmrml_cmd << " -13C_pl #{o}/external_submission_13c_peaklist.txt"

    if magmet_json.present?
      nmrml_cmd << if proton_dimension == true
                    " -json_1h_path '#{magmet_json}'"
                  else
                    " -json_13c_path '#{magmet_json}'"
                  end
    end

    nmrml_cmd << " > #{o}/nmrml_creator.log"

    `#{nmrml_cmd}`
    
    Rails.logger.info("NmrML Command of #{submission_uuid}: #{nmrml_cmd}")
    File.rename parent_dir.gsub(' ', ''), parent_dir
  end

  def run_nmr_pred(nmr_submission, submission_uuid, submission_path, mol_file)
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    nmrpred_script = Rails.root.join('backend', 'nmr-pred', 'nmrpred.py')
    submission_path ||= Pathname.new(nmr_submission.fid_file.path).dirname

    nmrpred_cmd = "#{python_path} #{nmrpred_script}"
    nmrpred_cmd << " --mol '#{mol_file}'"
    nmrpred_cmd << " --outputpath '#{submission_path}'"
    nmrpred_cmd << ' --outputprefix external_submission'
    nmrpred_cmd << ' --writeassignmenttable '
    nmrpred_cmd << ' --write1h  '
    nmrpred_cmd << ' --write1hcoup '
    nmrpred_cmd << ' --write13c '
    nmrpred_cmd << ' --writechsqctable'
    nmrpred_cmd << ' --plot1h'
    nmrpred_cmd << ' --plot13c ' if nmr_submission.spectrum_type.include? '13C'

    `#{nmrpred_cmd}`
    Rails.logger.info("NmrPred Command of #{submission_uuid}: #{nmrpred_cmd}")
  end

  def zip_magmet_path(nmr_submission, submission_path, submission_uuid)
    fid_folder_name = nmr_submission.filtered_fid_file_name.split(".")[0]
    filtered_fid_path = File.join(submission_path, fid_folder_name)
    zip_path = "#{filtered_fid_path}_magmet.zip"
    # Create zip for magmet zipped files
    begin
      Zip::File.open(zip_path, create: true) do |zipfile|
        Dir[File.join(filtered_fid_path, '**', '**')].each do |file|
          zipfile.add(file.sub(submission_path + '/', ''), file)
        end
      end
    rescue => e
      Rails.logger.error "Zipping Failed for Magmet for #{submission_uuid} - #{e.message}"
    end

    puts "Magmet zip path = #{zip_path}"
    return zip_path
  end

  # Runs magmet on the nmr_submission to generate the quality report and magmet json
  def run_magmet(nmr_submission, submission_uuid, submission_path)
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    script_dir  = Rails.root.join('backend', 'magmet')
    script      = Rails.root.join('backend', 'magmet/processing_1d.py')
    zip_path    = zip_magmet_path(nmr_submission, submission_path, submission_uuid)

    process_cmd = "#{python_path} #{script} -m #{script_dir}/"
    process_cmd << " -i '#{zip_path}'"
    process_cmd << " -o '#{submission_path}'"
    process_cmd << " -f #{nmr_submission.formatted_frequency}"
    process_cmd << " -ref 'TMS'" # this is temporary until Linington's tool collects and sends us the correct reference
    process_cmd << " -sptype '#{nmr_submission.spectrum_type}'"
    process_cmd << " -sol #{nmr_submission.solvent}"

    `#{process_cmd}` # run magmet
    Rails.logger.info("Magmet Command of #{submission_uuid}: #{process_cmd}")

    working_dir = submission_path
    magmet_file_prefix = "#{nmr_submission.spectrum_type}_#{nmr_submission.solvent}_TMS_#{nmr_submission.formatted_frequency}"

    magmet_json_path = File.join(working_dir, "#{magmet_file_prefix}.json")
    magmet_svg_path = File.join(working_dir, "#{magmet_file_prefix}.svg")
    [magmet_json_path, magmet_svg_path]
  end

  def process_1d_data nmr_submission, magmet=true, submission_uuid=nil, submission_path=nil
    # Only run magmet and nmrpred for fid files, not peak lists and assignment files
    # Nmrpred creates peaklists text files for nmrml files to be created from
    # Assignments are custom made from custom peaklist files from submissions and peak lists dont create nmrml files
    magmet_report, magmet_json = nil
    mol_file = "#{submission_path}/rdkit_mol_for_nmrpred.mol"

    if magmet
      magmet_json, magmet_svg = run_magmet nmr_submission, submission_uuid, submission_path
      File.open(mol_file, 'w') do |file|
        file.write nmr_submission.external_submission.natural_product.threeDmol
      end

      # zip the magmet files together to create quality report
      magmet_report = File.join(File.dirname(magmet_json), "#{File.basename(magmet_json, '.json')}_report.zip")
      `zip -j #{magmet_report} #{magmet_json} #{magmet_svg}`
      run_nmr_pred(nmr_submission, submission_uuid, submission_path, mol_file)
    elsif nmr_submission.assignment_uuid.present?
      # Create custom peaklist files for assignments from 3d structure
      # File.open(mol_file, 'w') do |file|
      #   file.write nmr_submission.external_submission.natural_product.threeDmol #TODO: Use rdkit 2019 to generate mol from smiles
      # end
      generate_molfile(nmr_submission.external_submission.smiles, mol_file) # Use this to test molfile generation from rdkit
      run_nmr_pred(nmr_submission, submission_uuid, submission_path, mol_file)
      nmr_submission.save_peaklist_file_for_nmrml(submission_path)
    end
    generate_1d_nmrml(nmr_submission, magmet_json, submission_uuid, submission_path)
    nmrml_file = File.join(submission_path, "#{nmr_submission.id}.nmrML")

    [nmrml_file, magmet_report]
  end

  def process_2d_data nmr_submission

  end

  def send_completed_email( collection )
    mailer = Mailgun::Client.new '**************************************************'
    view = ActionView::Base.new(ActionController::Base.view_paths,
                                {submission_collection: collection})
    message = {
      from: "The NP-MRD Team <<EMAIL>>",
      to: collection.user_email,
      subject: "Completed Submission to NP-MRD (#{collection.submission_id})",
      text: view.render(file: 'notifier/completed_external_deposition.text.erb')
    }
    mailer.send_message 'npmrd-deposition.org', message
  end

  def rename original_name
    new_name = original_name.to_s.gsub(' ', '_').gsub(/[^a-zA-Z0-9\-\/_(){}\[\]]/,"")
    File.rename original_name, new_name
  end

  def save_fields_build_quality_report(external_submission, submission_data, compound_errors)
    begin
      # Populate external_submission using the mapping
      ExternalSubmission::SUBMISSION_MAPPINGS.each do |attr, (parent_key, child_key)|
        value = submission_data.dig(parent_key, child_key)
        external_submission.send("#{attr}=", value) if value.present?
      end

      # For fields directly mapped between submission_data and external_submission
      ExternalSubmission::DIRECT_FIELDS.each do |field|
        value = submission_data[field]
        external_submission.send("#{field}=", value) if value.present?
      end

      # Direct fields for the report data
      report_data = ExternalSubmission::DIRECT_FIELDS.each_with_object({}) do |field, hash|
        hash[field] = submission_data[field] if submission_data[field].present?
      end

      # Handling nested report fields separately
      ExternalSubmission::NESTED_REPORT_FIELDS.each do |field, path|
        value = submission_data.dig(*path)
        report_data[field] = value
      end
    rescue => e
      handle_submission_error("Report Data generation error", submission_data['inchikey'], e, external_submission)
      compound_errors << { type: 'submission_error', message: e.message }
    end
    return report_data || {}
  end

  def init_natural_product(submission_data, external_submission, compound_errors)
    begin
      natural_product = load_create_natural_product_for_deposition(
        submission_data['npmrd_id'], submission_data['compound_name'], 
        submission_data['smiles'], submission_data['inchikey'],
        external_submission
      )
      external_submission.natural_product = natural_product
    rescue StandardError => e
      handle_submission_error("Structure Error", submission_data['inchikey'], e, external_submission)
      compound_errors << { type: 'structure_error', message: e.message }
    end
    return natural_product
  end

  def find_user(external_submission, compound_errors)
    begin
      user = User.find(external_submission.depositor_account_id)
      external_submission.user = user
    rescue StandardError => e
      handle_submission_error("User not Found Error", external_submission.inchikey, e, external_submission)
      compound_errors << { type: 'submission_error', message: e.message }
    end
    return user
  end

  def create_link_species(external_submission, natural_product, compound_errors)
    begin
      if external_submission.origin_species.present? || external_submission.origin_genus.present?
        scientific_name = if external_submission.origin_genus.present? && external_submission.origin_species.present?
                          "#{external_submission.origin_genus} #{external_submission.origin_species}"
                        elsif external_submission.origin_genus.present?
                          external_submission.origin_genus
                        else
                          external_submission.origin_species
                        end

        sp = Species.find_or_create_by!(scientific_name: scientific_name)
        sp.genus = external_submission.origin_genus if external_submission.origin_genus.present?
        sp.species = external_submission.origin_species if external_submission.origin_species.present?
        sp.save!

        SpeciesMapping.find_or_create_by!(species: sp, species_mappable: external_submission,
          is_origin_species: true)
      end
    rescue StandardError => e
      handle_submission_error("Species Mapping Error", external_submission.inchikey, e, external_submission)
      compound_errors << { type: 'submission_error', message: e.message }
    end
  end

  def create_link_references(external_submission, natural_product, compound_errors)
    begin
      # TODO: Can it have two articles or is it just one article that can have both doi and pubmed?
      if external_submission.citation_pmid.present?
        article = CiteThis::Article.where(pubmed_id: external_submission.citation_pmid).first_or_create!
        unless external_submission.articles.include?(article)
          external_submission.articles << article
          external_submission.save!
        end
      elsif external_submission.citation_doi.present?
        article = CiteThis::Article.where(doi: external_submission.citation_doi).first_or_create
        # TODO: If article cannot be created by doi search, it returns last article from db with id = nil. Fix?
        if !external_submission.articles.include?(article) && article.id.present?
          external_submission.articles << article
          external_submission.save!
        end
      end
    rescue StandardError => e
      handle_submission_error("Cite this Referencing Error", external_submission.inchikey, e, external_submission)
      compound_errors << { type: 'submission_error', message: e.message }
    end
  end

  def save_external_submission(external_submission, compound_errors)
    begin
      external_submission.save!
    rescue StandardError => e
      handle_submission_error("External Submission Error", external_submission.inchikey, e, external_submission)
      # TODO: Multiple entries can be with submission error. Maybe add array?
      compound_errors << { type: 'submission_error', message: e.message }
    end
  end

  def deposit(submission_path, submission_data, external_submission, compound_errors)
    natural_product = init_natural_product(submission_data, external_submission, compound_errors)
    report_data = save_fields_build_quality_report(external_submission, submission_data, compound_errors)
    user = find_user(external_submission, compound_errors)
    save_external_submission(external_submission, compound_errors)

    create_link_species(external_submission, natural_product, compound_errors)
    create_link_references(external_submission, natural_product, compound_errors)

    peak_lists = submission_data['nmr_data']['peak_lists']
    deposit_peak_lists(peak_lists, report_data, external_submission, natural_product, user, submission_path)

    assignments = submission_data['nmr_data']['assignment_data']
    deposit_assignments(assignments, report_data, external_submission, natural_product, user, submission_path)
    
    nmr_data = submission_data['nmr_data']['experimental_data']['nmr_metadata']
    deposit_nmr_data(nmr_data, external_submission, report_data, submission_path)

    export_natural_product(natural_product, submission_data, external_submission, compound_errors)
    generate_compound_release_status(report_data, natural_product, external_submission, compound_errors)
    save_quality_report(report_data, external_submission, compound_errors)

    return report_data
  end

  def generate_compound_release_status(report_data, natural_product, external_submission, compound_errors)
    begin
      report_data['ingestion_time'] = Time.now
      if natural_product.nil?
        report_data['np_card_status'] = 'not_created'
        report_data['npmrd_id'] = nil
        report_data['compound_npmrd_db_release_status'] = 'not_released'
      else
        report_data['np_card_status'] = external_submission.npmrd_match_status == 'new_entry' ? 'created' : 'already_existed'
        report_data['npmrd_id'] = natural_product.np_mrd_id
        report_data['compound_npmrd_db_release_status'] = natural_product.db_release_status
      end
      report_data['npmrd_match_status'] = external_submission.npmrd_match_status
      report_data['compound_errors'] = compound_errors
    rescue StandardError => e
      handle_submission_error("Release Status Error", external_submission.inchikey, e, external_submission)
      # TODO: Multiple entries can be with submission error. Maybe add array?
      compound_errors << { type: 'submission_error', message: e.message }
    end
  end

  def deposit_nmr_data(nmr_data, external_submission, report_data, submission_path)
    report_data['nmr_data']['experimental_data'] = []
    nmr_data&.each do |raw_data|
      external_nmr_submission = nil
      experiment = ExternalNmrSubmission::EXPERIMENT_FIELDS.each_with_object({}) do |field, hash|
        hash[field] = raw_data[field]
      end
      
      begin
        filtered_data_zip_path, zipped_fid_path, fid_path = create_nmr_zip_paths(raw_data, submission_path)
  
        external_nmr_submission = create_external_nmr_submission(external_submission, 
          raw_data, filtered_data_zip_path, zipped_fid_path, fid_path, submission_path)
  
        experiment.merge!(create_spectrum_report_hash(external_nmr_submission))
      rescue => e
        handle_submission_error("NMR Submission Failed for", external_submission.inchikey, e, external_submission)
        experiment.merge!(create_spectrum_report_hash(external_nmr_submission, e))
      end
      report_data['nmr_data']['experimental_data'] << experiment
    end
  end

  def create_spectrum_report_hash(external_nmr_submission, error=nil)
    if error.nil?
      {
        'spectrum_errors' => [],
        'spectrum_ingestion_status' => 'ingested',
        'spectrum_npmrd_db_release_status' => external_nmr_submission.db_release_status
      }
    else
      {
        'spectrum_errors' => [{ type: 'nmr_error', message: error.message }],
        'spectrum_ingestion_status' => 'not_ingested',
        'spectrum_npmrd_db_release_status' => 'not_released'
      }
    end
  end

  def create_nmr_zip_paths(raw_data, submission_path)
    filtered_data_path = File.join(submission_path, raw_data['extracted_experiment_folder'])
    filtered_data_zip_path = "#{filtered_data_path}.zip"
    # Use the -j option to strip the path information
    system("zip -j \"#{filtered_data_zip_path}\" \"#{filtered_data_path}\"/*")
    puts "Filtered data path = #{filtered_data_zip_path}"

    fid_path = File.join(submission_path, raw_data['extracted_data_path'])
    zipped_fid_path = "#{fid_path}.zip"
    system("zip -j \"#{zipped_fid_path}\" \"#{fid_path}\"")

    return filtered_data_zip_path, zipped_fid_path, filtered_data_path
  end  

  def save_magmet_nmrml_files(external_nmr_submission, external_submission, fid_path, submission_path)
    Rails.logger.info "ED DEBUG FID FILE? #{fid_path}"
    nmrml_name, magmet_report = if external_nmr_submission.f2_nucleus.present?
                                    process_2d_data external_nmr_submission
                                elsif File.file?(File.join(fid_path, 'fid'))
                                    process_1d_data(external_nmr_submission, true, external_submission.submission_uuid, submission_path)
                                end
    if magmet_report.present?
      begin
        external_nmr_submission.assign_attributes(magmet_report: File.open(magmet_report))
      rescue Errno::ENOENT
      end
    end

    if nmrml_name.present?
      begin
        external_nmr_submission.assign_attributes(nmrml_file: File.open(nmrml_name),
                                                  processed: true)# save nmrml file into nmr_submission object
      rescue Errno::ENOENT
      end
    end
    external_nmr_submission.save!
  end

  def create_external_nmr_submission(external_submission, raw_data, filtered_data_zip_path, zipped_fid_path, 
      fid_path, submission_path)
    ext_nmr_sub = ExternalNmrSubmission.find_by(spectrum_uuid: raw_data['spectrum_uuid'])
    return ext_nmr_sub if ext_nmr_sub.present?
  
    # Create a new ExternalNmrSubmission if not found
    ext_nmr_sub = external_submission.external_nmr_submissions.create(
      nmr_spectrum_type: raw_data['experiment_type'],
      spectrometer_frequency: raw_data['frequency'],
      temperature: raw_data['temperature'],
      temperature_units: raw_data['temperature_units'],
      solvent: raw_data['solvent'],
      vendor: raw_data['vendor'],
      f1_nucleus: raw_data['f1_nucleus'],
      f2_nucleus: raw_data['f2_nucleus'],
      filetype: raw_data['filetype'],
      frequency_units: raw_data['frequency_units'],
      spectrum_uuid: raw_data['spectrum_uuid'],
      spectrum_embargo_release_ready: raw_data['spectrum_embargo_release_ready'],
      export: raw_data['spectrum_embargo_release_ready'],
      filtered_fid: File.open(filtered_data_zip_path.to_s.gsub('\\', '')),
      fid_file: File.open(zipped_fid_path.to_s.gsub('\\', ''))
    )
    if raw_data["vendor"] == 'Bruker' || raw_data["vendor"] == 'Varian'
      save_magmet_nmrml_files(ext_nmr_sub, external_submission, fid_path, submission_path)
    end
  
    ext_nmr_sub
  end
  

  def save_quality_report(report_data, external_submission, compound_errors)
    # Save quality report of the submission to the database
    begin
      report_json = report_data.to_json
      # Create a temporary file
      Tempfile.open(['report', '.json']) do |tempfile|
        # Write JSON data to the tempfile
        tempfile.write(report_json)
        tempfile.rewind

        # Assign this tempfile to the Paperclip attachment
        external_submission.quality_report = tempfile
        # Mark as processed if no errors faced
        external_submission.processed = true if external_submission.error_details.nil?
        save_external_submission(external_submission, compound_errors)
      end
    rescue => e
      handle_submission_error("Quality Report Failed", external_submission.inchikey, e, external_submission)
      compound_errors << { type: 'submission_error', message: e.message }
      report_data['compound_errors'] = compound_errors
    end
  end

  def export_natural_product(natural_product, submission_data, external_submission, compound_errors)
    # Ensure natural product is exported
    # Also saves the structure if it was added above
    begin
      unless natural_product.nil? || natural_product.export
        if external_submission.npmrd_match_status == 'name' || external_submission.npmrd_match_status == 'inchikey'
          natural_product.export = false
        elsif submission_data['submission']['compound_embargo_release_ready'].present?
          natural_product.export = submission_data['submission']['compound_embargo_release_ready']
        end
        natural_product.save!
      end
    rescue => e
      handle_submission_error("Natural Product not exported", external_submission.inchikey, e, external_submission)
      compound_errors << { type: 'structure_error', message: e.message }
    end
  end

  def deposit_peak_lists(peak_lists, report_data, external_submission, natural_product, user, path)
    report_data['nmr_data'] = { 'peak_lists' => [] }
    lit_ref, lit_ref_type = determine_literature_reference(external_submission)

    peak_lists&.each do |data|
      peak_list_json = { 'peak_list_uuid' => data['peak_list_uuid'], 'peak_list_errors' => [], 'nuclues' => data['nucleus'] }
      begin
        if data['values']&.any? # Spectrum type is 1D-1H
          chemical_shift_submission = create_chemical_shift_submission(data,
            external_submission, natural_product, user, path, lit_ref, lit_ref_type)
        end
  
        peak_list_json.merge!({
          'peak_list_ingestion_status' => 'ingested',
          'peak_list_npmrd_db_release_status' => chemical_shift_submission.db_release_status
        })
      rescue StandardError => e
        handle_submission_error("Peak List Error", external_submission.inchikey, e, external_submission)
        peak_list_json['peak_list_errors'] << { type: 'peak_list_error', message: e.message }
        peak_list_json.merge!({
          'peak_list_ingestion_status' => 'not_ingested',
          'peak_list_npmrd_db_release_status' => 'not_released'
        })
      end
  
      report_data['nmr_data']['peak_lists'] << peak_list_json
    end
  end

  def deposit_assignments(assignments, report_data, external_submission, natural_product, user, path)
    report_data['assignment_data'] = []

    assignments&.each do |data|
      assignment_json = { 'assignment_uuid' => data['assignment_uuid'], 'assignment_errors' => [] }
      begin
        if data['spectrum']&.any? # Spectrum type is 1D-1H
          chemical_shift_submission = create_assignment_submission(data,
            external_submission, natural_product, user, path)
        end
  
        assignment_json.merge!({
          'assignment_ingestion_status' => 'ingested',
          'assignment_npmrd_db_release_status' => chemical_shift_submission.db_release_status
        })
      rescue StandardError => e
        handle_submission_error("Assignment Error", external_submission.inchikey, e, external_submission)
        assignment_json['assignment_errors'] << { type: 'assignment_error', message: e.message }
        assignment_json.merge!({
          'assignment_ingestion_status' => 'not_ingested',
          'assignment_npmrd_db_release_status' => 'not_released'
        })
      end
  
      report_data['assignment_data'] << assignment_json
    end
  end

  def determine_literature_reference(external_submission)
    if external_submission.citation_doi.present?
      [external_submission.citation_doi, 'DOI']
    elsif external_submission.citation_pmid.present?
      [external_submission.citation_pmid, 'PMID']
    elsif external_submission.citation_pii.present?
      [external_submission.citation_pii, 'PII']
    else
      ['NA', 'NA']
    end
  end

  def create_chemical_shift_submission(data, external_submission, natural_product, user, 
    path, lit_ref, lit_ref_type)
    nucleus = data['nucleus']
    spectrum_type = nucleus == 'H' ? '1D-1H' : '1D-13C'
  
    # Return if ChemicalShiftSubmission and its corresponding spectrum_type already exist
    # Find all CS with peak_list_uuid and then filter by spectrum_type
    cs = ChemicalShiftSubmission.includes(:chemical_shift_submission_meta_data)
          .where(peak_list_uuid: data['peak_list_uuid'])
          .find_by(chemical_shift_submission_meta_data: { spectrum_type: spectrum_type })
    
    return cs if cs.present?

    chemical_shift_submission = ChemicalShiftSubmission.create!(
      chemical_shift_submission_meta_data_attributes: {
        solvent: data["solvent"],
        chemical_shift_standard: data["reference"],
        spectrometer_frequency: data['frequency'],
        temperature: data['temperature'],
        temperature_units: data["temperature_units"],
        frequency_units: data["frequency_units"],
        spectrum_type: spectrum_type,
        literature_reference: lit_ref,
        literature_reference_type: lit_ref_type
      },
      external_submission: external_submission,
      natural_product: natural_product,
      valid: true,
      user: user,
      peak_list_uuid: data["peak_list_uuid"],
      peak_list_embargo_release_ready: data["peak_list_embargo_release_ready"],
      export: data["peak_list_embargo_release_ready"]
    )
  
    data['values']&.each_with_index do |value, index|
      atom_symbol = spectrum_type == '1D-1H' ? 'H' : 'C'
      chemical_shift_submission.chemical_shifts.create!(
        atom_symbol: atom_symbol,
        # atom_id: index.to_s, # Don't convert index to atom_id for peak lists. Could be any atom.
        chemical_shift_true: value,
      )
    end
  
    process_peak_list_data(path, chemical_shift_submission, spectrum_type)
    return chemical_shift_submission
  end

  def create_assignment_submission(data, external_submission, natural_product, user, path)
    nucleus = data['nucleus']
    spectrum_type = nucleus == 'H' ? '1D-1H' : '1D-13C'
  
    # Return if ChemicalShiftSubmission already exists. TODO: Maybe just re-update with all values in case submission
    # wasn't successful? 
    cs = ChemicalShiftSubmission.find_by(assignment_uuid: data['assignment_uuid'])
    return cs if cs.present?

    chemical_shift_submission = ChemicalShiftSubmission.create!(
      chemical_shift_submission_meta_data_attributes: {
        solvent: data["solvent"],
        chemical_shift_standard: data["reference"],
        spectrometer_frequency: data['frequency'],
        temperature: data['temperature'],
        temperature_units: data["temperature_units"],
        frequency_units: data["frequency_units"],
        spectrum_type: spectrum_type,
      },
      external_submission: external_submission,
      natural_product: natural_product,
      valid: true,
      user: user,
      assignment_uuid: data["assignment_uuid"],
      curator_email_address: data["curator_email_address"],
      export: data["assignment_data_embargo_release_ready"]
    )
  
    data['spectrum']&.each do |spectrum|
      spectrum['rdkit_index']&.each do |rdkit_index|
        rdkit_index_list = spectrum['rdkit_index']&.join(',')
        it_index_list = spectrum['interchangeable_index']&.join(',')
        coupling_list = spectrum['coupling']&.join(',')

        chemical_shift_submission.chemical_shifts.create!(
          atom_symbol: nucleus,
          atom_id: rdkit_index,
          chemical_shift_true: spectrum['shift'],
          integration: spectrum['integration'],
          multiplet_true: spectrum['multiplicity'],
          rdkit_index: rdkit_index_list,
          interchangeable_index: it_index_list,
          jcoupling_true: coupling_list
        )
      end
    end
  
    process_peak_list_data(path, chemical_shift_submission, spectrum_type)
    return chemical_shift_submission
  end
  
  def process_peak_list_data(path, chemical_shift_submission, spectrum_type)
    new_folder_name = spectrum_type == '1D-1H' ? '1H_peak' : '13C_peak'
    new_folder_path = File.join(path, new_folder_name)
    FileUtils.mkdir_p(new_folder_path)

    nmrml_name = process_1d_data(chemical_shift_submission, false, 
      chemical_shift_submission.external_submission.submission_uuid, new_folder_path).first # Don't run magmet : false

    if nmrml_name.present?
      begin
        chemical_shift_submission.assign_attributes(nmrml_file: File.open(nmrml_name), processed: true)
      rescue Errno::ENOENT
        Rails.logger.info "ED DEBUG NMRML FILE NOT FOUND #{nmrml_name}"
      end
      chemical_shift_submission.save!
    end
  end

  # Usage example:
  # submission_path = '/desired/path/for/output'
  # mol_file = "#{submission_path}/rdkit_mol_for_nmrpred.mol"
  # generate_molfile('CCO', mol_file)  # Example SMILES for Ethanol
  def generate_molfile(smiles, molfile_path)
    script = Rails.root.join('backend', 'nmr-pred', 'mol_from_smiles.py')
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
  
    # Build the command to execute the Python script
    command = "#{python_path} #{script} '#{smiles}' '#{molfile_path}'"
  
    # Execute the command
    Open3.popen3(command) do |stdin, stdout, stderr, wait_thr|
      output = stdout.read
      errors = stderr.read
      if errors.empty?
        puts "Output: #{output}"
      else
        puts "Errors occurred during the molfile generation: #{errors}"
      end
    end
  end  
end
