class ParseExcelJob < ActiveJob::Base
  queue_as :default

  def perform(excel_parser_script_with_path,excel_file_with_user_chemical_shifts_and_meta_data,project_name_parser,existing_location_where_project_directory_will_be_created,parsed_excel_file_path)
    @excel_parser_script_with_path = excel_parser_script_with_path
    puts("@excel_parser_script_with_path job = #{@excel_parser_script_with_path}")
    @excel_file_with_user_chemical_shifts_and_meta_data = excel_file_with_user_chemical_shifts_and_meta_data
    puts("@excel_file_with_user_chemical_shifts_and_meta_data = #{@excel_file_with_user_chemical_shifts_and_meta_data}")
    @project_name_parser = project_name_parser
    puts("@project_name_parser job = #{@project_name_parser}")
    @existing_location_where_project_directory_will_be_created = existing_location_where_project_directory_will_be_created
    puts("@existing_location_where_project_directory_will_be_created job = #{@existing_location_where_project_directory_will_be_created}")
    @parsed_excel_file_path = parsed_excel_file_path
    puts("@parsed_excel_file_path job = #{@parsed_excel_file_path}")
    
    BatchUpload.ParseExcel(@excel_parser_script_with_path,@excel_file_with_user_chemical_shifts_and_meta_data, @project_name_parser, @existing_location_where_project_directory_will_be_created)
  end
end
