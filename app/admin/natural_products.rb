ActiveAdmin.register NaturalProduct do
  menu priority: 2

  config.sort_order = 'np_mrd_id'
  config.batch_actions = true

  actions :index, :show, :new, :create, :edit, :update

  member_action :clear_cache, method: :post do
    # Find the natural product
    @natural_product = NaturalProduct.find_by_np_mrd_id(params[:id])
    # Assuming bust_cache is a method within the controller that can operate on @natural_product
    bust_cache
    # Redirect back or to any other page with a notice
    redirect_to admin_natural_product_path(@natural_product), notice: "Cache cleared!"
  end

  # Add a button/link in the view to trigger clear_cache action
  action_item :clear_cache, only: :show do
    link_to 'Clear Cache', clear_cache_admin_natural_product_path(natural_product), method: :post
  end

  controller do
    after_action :bust_cache, only: [:update, :create, :destroy]

    helper Moldbi::<PERSON><PERSON>sHelper
    defaults finder: :find_by_np_mrd_id

    private

    # Cache only dependent on show.html.slim not any of its dependent partials. Changing those will not expire cache
    # Cache key like "views/natural_products/id-updated_at/digest"
    def bust_cache
      digest = ActionView::Digestor.new(name: 'natural_products/show.html.slim', finder: lookup_context).digest
      cache_key = "views/#{@natural_product.cache_key}/#{digest}"
      Rails.logger.info "Cache Key For #{@natural_product.np_mrd_id} - #{cache_key}"

      # Rails.cache.delete(cache_key) # This does not work always
      # This removes curation resource cache so that we can see classificationm
      # identifiers, references, and other metadata, synonyms, etc.
      Moldbi::CurationResource.bust_cache(@natural_product.np_mrd_id)
      Moldbi::StructureResource.bust_cache(@natural_product.np_mrd_id)
      # This removes the structure resource cache so that we can see the structure,
      # and related images
      # Moldbi::StructureResource.bust_cache(@natural_product.np_mrd_id)
      # This removes the show page cache so that we can display any data
      @natural_product.touch
    end

    def permitted_params
      params.permit! # This permits all attributes
    end
  end

  # Used to search for natural_products from select2
  collection_action :search, method: :get do
    @natural_products = NaturalProduct.limit(20)
    params[:q].to_s.split.each do |word|
      @natural_products =
        @natural_products.where("name LIKE :q OR np_mrd_id LIKE :q", q: "%#{word}%")
    end
    respond_to do |format|
      format.json do
        render json: @natural_products.as_json(only: [:id, :name], methods: [:label])
      end
    end
  end

  # action_item(:more, only: [:show, :edit]) do
  #   link_to('Proteins', admin_protein_associations_path(q: { natural_product_np_mrd_id_contains: natural_product.np_mrd_id })) << " " <<
  #   link_to('Concentrations', admin_concentrations_path(q: { natural_product_np_mrd_id_contains: natural_product.np_mrd_id }))
  # end

  scope :exported
  scope :not_exported
  scope :duplicates_by_inchikey

  filter :np_mrd_id
  filter :name
  filter :cas
  filter :moldb_inchikey

  index do
    selectable_column
    column :np_mrd_id
    column :name
    column :cas
    column :moldb_inchikey
    column "Exported", sortable: :export do |natural_product|
      status_tag (natural_product.export ? "Exported" : "Not Exported"), class: (natural_product.export ? :ok : :error)
    end
    actions
  end

  show title: :label do |m|
    panel "Identification" do
      attributes_table_for m, :created_at, :updated_at, :np_mrd_id, :name, :description, :cas, :moldb_inchikey, :moldb_smiles do
        row(:structure) do
          moldb_vector_thumbnail(natural_product)
        end
        row(:synonyms) do 
          moldb_synonyms_table(natural_product) if natural_product.synonymified?
        end
        row(:export) do
          status_tag (m.export ? "Exported" : "Not Exported"),
            (m.export ? :ok : :error)
        end
      end
      table_for(m.accession_numbers) do
        column "Accession Numbers" do |an|
          an.number
        end
      end
      # table_for(m.synonyms) do
      #   column "Synonyms" do |synonym|
      #     synonym.synonym
      #   end
      # end
    end

    panel "Experimental Properties" do
      attributes_table_for m, :state
      render partial: "experimental_properties", locals: { natural_product: m }
    end

    panel "External Submissions" do
      table_for m.external_submissions do
        column :compound_name do |external_submission|
          link_to external_submission.compound_name, admin_external_submission_path(external_submission)
        end
        column :inchikey
        column :submission_uuid
        column :compound_uuid
        column :depositor_email
        column :npmrd_match_status
        column :submission_date
      end
    end

    panel "Same Structures" do
      table_for m.same_structures do 
        column(:structure) do |np|
          moldb_vector_thumbnail(np)
        end
        column(:np_mrd_id) do |np|
          link_to np.np_mrd_id, admin_natural_product_path(np)
        end
        column :name
        column(:synonyms) do |np|
          moldb_synonyms_table(np) if np.synonymified?
        end
        column :moldb_inchikey
      end
    end

    panel "Same Names" do
      table_for m.same_names do
        column(:structure) do |np|
          moldb_vector_thumbnail(np)
        end
        column(:np_mrd_id) do |np|
          link_to np.np_mrd_id, admin_natural_product_path(np)
        end
        column :name
        column(:synonyms) do |np|
          moldb_synonyms_table(np) if np.synonymified?
        end
        column :moldb_inchikey
      end
    end

    panel "References" do
      if m.articles.any?
        table_for(m.articles) do
          column "PubMed ID" do |article|
            bio_link_out(:pubmed, article.pubmed_id)
          end
          column "Citation" do |article|
            article.citation
          end
        end
      end
      if m.textbooks.any?
        table_for(m.textbooks) do
          column "ISBN" do |textbook|
            bio_link_out(:open_isbn, textbook.isbn)
          end
          column "Title" do |textbook|
            textbook.title
          end
        end
      end
      if m.external_links.any?
        table_for(m.external_links) do
          column "Link" do |external_link|
            link_to(external_link.name, external_link.url, target: '_blank')
          end
        end
      end
    end

    active_admin_comments
  end

  form do |f|
    f.semantic_errors *f.object.errors.keys

    f.actions

    tabs do
      tab 'Identification' do
        f.inputs "Identification", id: 'natural-product-identification' do
          f.input :np_mrd_id, input_html: { disabled: true }
          f.input :export
          f.input :name
          render partial: "structure_form", locals: { form: f }


          f.has_many :accession_numbers, allow_destroy: true do |an_f|
            an_f.input :number, input_html: { style: 'width: 400px'}
          end
          # f.has_many :synonyms, allow_destroy: true do |synonym_f|
          #   synonym_f.input :synonym, input_html: { style: 'width: 400px'}
          # end
        end
      end

      tab 'Experimental Properties' do
        f.inputs 'Experimental Properties' do
          #f.input :state, collection: NaturalProduct.states
          f.has_many :experimental_property_set, heading: false, new_record: false do |eps_f|
            eps_f.input :logp
            eps_f.input :logp_reference
            eps_f.input :water_solubility
            eps_f.input :water_solubility_reference
            eps_f.input :melting_point
            eps_f.input :melting_point_reference
            eps_f.input :boiling_point
            eps_f.input :boiling_point_reference
          end
        end
      end

      tab 'References' do
        f.inputs "References", id: 'natural-product-references' do
          f.input :synthesis_reference, input_html: { rows: 3 }
          f.input :article_text_references, as: :text, label: 'Pubmed References', input_html: { rows: 3 }
          f.has_many :textbook_referencings, heading: "Textbooks", new_record: "Add Textbook", allow_destroy: true do |textbook_f|
            textbook_f.input :textbook_id, as: :string, placeholder: 'Start typing to search',
            input_html: { class: 'remote-selector formatted-select-wide',
                          data: { source: cite_this.textbooks_path,
                                  label: textbook_f.object.try(:textbook).try(:to_label) } }
          end
          f.has_many :external_link_referencings, heading: "External Links", new_record: "Add External Link", allow_destroy: true do |link_f|
            link_f.input :external_link_id, as: :string, placeholder: 'Start typing to search',
            input_html: { class: 'remote-selector formatted-select-wide',
                          data: { source: cite_this.external_links_path,
                                  label: link_f.object.try(:external_link).try(:to_label) } }
          end
        end
      end
    end
    f.actions
  end
end
