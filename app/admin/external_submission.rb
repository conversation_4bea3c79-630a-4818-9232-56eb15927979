ActiveAdmin.register ExternalSubmission do
  menu priority: 3

  config.sort_order = 'natural_product_id'
  config.batch_actions = true

  actions :index, :show, :new, :create, :edit, :update

  controller do
    after_action :bust_cache, only: [:update, :create, :destroy]

    helper Moldbi::<PERSON><PERSON>sHelper
    defaults finder: :find_by_id

    private

    def bust_cache
      expire_fragment(@external_submission)
    end

    def permitted_params
      params.permit! # This permits all attributes
    end
  end

  # Used to search for external_submissions from select2
  collection_action :search, method: :get do
    @external_submissions = ExternalSubmission.limit(20)
    params[:q].to_s.split.each do |word|
      @external_submissions =
        @external_submissions.where("name LIKE :q OR np_mrd_id LIKE :q", q: "%#{word}%")
    end
    respond_to do |format|
      format.json do
        render json: @external_submissions.as_json(only: [:id, :name], methods: [:label])
      end
    end
  end

  # action_item(:more, only: [:show, :edit]) do
  #   link_to('Proteins', admin_protein_associations_path(q: { external_submission_np_mrd_id_contains: external_submission.np_mrd_id })) << " " <<
  #   link_to('Concentrations', admin_concentrations_path(q: { external_submission_np_mrd_id_contains: external_submission.np_mrd_id }))
  # end

  scope :np_exported
  scope :np_not_exported
  scope :inchikey_matched
  scope :name_matched

  filter :natural_product_np_mrd_id_cont, as: :string, label: 'NP MRD ID'
  filter :compound_name
  filter :inchikey
  filter :submission_uuid

  index do
    selectable_column
    column('NPMRD ID') do |external_submission|
      external_submission&.natural_product&.np_mrd_id
    end
    column :compound_name
    column :inchikey
    column :submission_uuid
    column("NP Exported") do |external_submission|
      status_tag (external_submission&.natural_product&.export ? "Exported" : "Not Exported"),
        (external_submission&.natural_product&.export ? :ok : :error)
    end
    actions
  end

  show title: :compound_name do |es|
    panel "Natural Product" do
      attributes_table_for es do
        row('NPMRD ID') do |external_submission|
          link_to external_submission&.natural_product&.np_mrd_id, admin_natural_product_path(external_submission&.natural_product)
        end
        row('NP Name') do |external_submission|
          external_submission&.natural_product&.name
        end
        row('NP Inchikey') do |external_submission|
          external_submission&.natural_product&.moldb_inchikey
        end
        row('NP Smiles') do |external_submission|
          external_submission&.natural_product&.moldb_smiles
        end
      end
    end

    panel "Identification" do
      attributes_table_for es do
        row :user_id
        row :citation_doi
        row :citation_pmid
        row :origin_genus
        row :origin_species
        row :depositor_email
        row :submission_uuid
        row :external_submission_collection_id
        row :inchikey
        row :smiles
      end
    end
  
    panel "Depositor Information" do
      attributes_table_for es do
        row :depositor_attribution_name
        row :depositor_account_id
        row :depositor_attribution_organization
        row :depositor_show_email_in_attribution
        row :depositor_show_name_in_attribution
        row :depositor_show_organization_in_attribution
      end
    end
  
    panel "Submission Details" do
      attributes_table_for es do
        row :citation_pii
        row :submission_source
        row :submission_type
        row :compound_uuid
        row :submission_date
        row :embargo_status
        row :embargo_date
        row :compound_name
        row :smiles
        row :submission_json
      end
    end
  
    panel "Experimental & Error Details" do
      attributes_table_for es do
        row :c_values
        row :h_values
        row :compound_embargo_release_ready
        row :processed
        row :error_details
        row :npmrd_match_status
      end
    end
  
    panel "File Information" do
      attributes_table_for es do
        row :quality_report_file_name
        row :quality_report_content_type
        row :quality_report_file_size
        row :quality_report_updated_at
      end
    end

    panel "Chemical Shift Submissions" do
      table_for es.chemical_shift_submissions do
        column('NPMRD ID') do |cs|
          cs&.natural_product&.np_mrd_id
        end
        column :peak_list_uuid
        column :peak_list_embargo_release_ready
        column :processed
        column :export

        column "Metadata" do |chemical_shift_submission|
          if chemical_shift_submission.chemical_shift_submission_meta_data.present?
            table_for chemical_shift_submission.chemical_shift_submission_meta_data do
              column :literature_reference
              column :solvent
              column :spectrometer_frequency
              column :temperature
              column :chemical_shift_standard
              column :spectrum_type
              column :literature_reference
              column :literature_reference_type
              column :temperature_units
              column :frequency_units
              # Add other metadata fields you want to display...
            end
          else
            "No metadata available"
          end
        end
      end
    end

    panel "External NMR Submissions" do
      table_for es.external_nmr_submissions do
        column :nmr_spectrum_type
        column :spectrometer_frequency
        column :temperature
        column :temperature_units
        column :solvent
        column :vendor
        column :f1_nucleus
        column :f2_nucleus
        column :processed
        column :filetype
        column :spectrum_uuid
        column :spectrum_embargo_release_ready
        column :export
        # Add other relevant fields as necessary
      end
    end

    panel "Species" do
      table_for es.species do 
        column :scientific_name
      end
    end

    panel "References" do 
      table_for es.articles do
        column :pubmed_id
        column :doi
        column :title
        column :authors
      end
    end
  
    panel "Miscellaneous" do
      attributes_table_for es do
        row :origin_private_collection
      end
    end
  
    panel "Timestamps" do
      attributes_table_for es do
        row :created_at
        row :updated_at
      end
    end
  
    active_admin_comments
  end  

  form do |f|
    f.semantic_errors *f.object.errors.keys
  
    f.actions
  
    tabs do
      tab 'Identification' do
        f.inputs "Identification", id: 'external-submission-identification' do
          f.input :natural_product_id, as: :string, placeholder: 'Start typing to search',
            input_html: { class: 'remote-selector formatted-select-wide',
                          data: { source: search_admin_natural_products_path,
                                  label: f.object.try(:natural_product).try(:label) } }
          f.input :submission_uuid, input_html: { disabled: true }
        end
      end
  
      tab 'Properties & JSON' do
        f.inputs 'Properties & JSON' do
          f.input :compound_name, as: :text, input_html: { rows: 3, disabled: true }
          f.input :npmrd_match_status, as: :select, collection: ExternalSubmission::MATCH_STATUS_VALUES, include_blank: true
        end
      end
  
      # Assuming other tabs (Experimental Properties, References) are kept as is from your initial setup
      # Ensure you adapt them based on actual relationships and fields in your model
    end
  
    f.actions
  end
  
end
