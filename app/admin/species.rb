# app/admin/species.rb

ActiveAdmin.register Species do
  menu priority: 5
  permit_params :scientific_name, :kingdom, :phylum, :class, :order, :family, 
                :scientific_name_taxid, :superkingdom, :species, :genus, :scientific_name_original

  # Filter options for the sidebar
  filter :scientific_name
  filter :kingdom
  filter :genus

  # Customize the index page
  index do
    selectable_column
    id_column
    column :scientific_name
    column :kingdom
    column :genus
    column :created_at
    column :updated_at
    actions
  end

  # Customize the show page
  show do
    attributes_table do
      row :scientific_name
      row :kingdom
      row :phylum
      row :class
      row :order
      row :family
      row :genus
      row :species
      row :scientific_name_taxid
      row :superkingdom
      row :scientific_name_original
      row :created_at
      row :updated_at
    end
    active_admin_comments
  end

  # Customize the form page
  form do |f|
    f.inputs do
      f.input :scientific_name
      f.input :kingdom
      f.input :genus
    end
    f.actions
  end

end
