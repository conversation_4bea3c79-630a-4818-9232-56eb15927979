ActiveAdmin.register SpeciesMapping do
  menu priority: 4
  permit_params :species_id, :species_mappable_id, :species_mappable_type, :is_origin_species

  # Filter by species scientific name
  filter :species_scientific_name_cont, as: :string, label: 'Species Scientific Name', 
    collection: proc { Species.pluck(:scientific_name).uniq }
  filter :species_mappable_type, as: :select, collection: ["NaturalProduct", "ExternalSubmission"]
  filter :is_origin_species

  # Index page
  index do
    selectable_column
    id_column
    column :species do |mapping|
      mapping.species.scientific_name
    end
    column :species_mappable_type
    column :species_mappable do |mapping|
      if mapping.species_mappable_type == "NaturalProduct"
        link_to mapping.species_mappable.np_mrd_id, admin_natural_product_path(mapping.species_mappable)
      elsif mapping.species_mappable_type == "ExternalSubmission"
        link_to mapping.species_mappable.submission_uuid, admin_external_submission_path(mapping.species_mappable)
      end
    end
    column :is_origin_species
    actions
  end

  # Show page
  show do
    attributes_table do
      row :species do |mapping|
        mapping.species.scientific_name
      end
      row :species_mappable_type
      row :species_mappable do |mapping|
        if mapping.species_mappable_type == "NaturalProduct"
          link_to mapping.species_mappable.np_mrd_id, admin_natural_product_path(mapping.species_mappable)
        elsif mapping.species_mappable_type == "ExternalSubmission"
          link_to mapping.species_mappable.submission_uuid, admin_external_submission_path(mapping.species_mappable)
        end
      end
      row :is_origin_species
      row :created_at
      row :updated_at
    end
  end
end
