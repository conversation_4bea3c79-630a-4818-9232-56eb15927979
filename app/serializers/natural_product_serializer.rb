class NaturalProductSerializer
  def initialize(natural_product)
    @natural_product = natural_product
  end

  def as_json
    {
      id: @natural_product.id,
      np_mrd_id: @natural_product.np_mrd_id,
      name: @natural_product.name,
      moldb_smiles: @natural_product.moldb_smiles,
      moldb_inchi: @natural_product.moldb_inchi,
      moldb_inchikey: @natural_product.moldb_inchikey,
      export: @natural_product.export,
      external_submissions_count: @natural_product.external_submissions.count,
      created_at: @natural_product.created_at,
      updated_at: @natural_product.updated_at
    }
  end
end
