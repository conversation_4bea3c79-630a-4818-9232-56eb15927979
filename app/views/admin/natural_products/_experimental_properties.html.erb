<table>
  <thead>
    <tr>
      <th>Property</th>
      <th>Value</th>
      <th>Reference</th>
    </tr>
  </thead>
  <tbody>
    <% [ :melting_point, :boiling_point, :logp, :water_solubility ].each do |col| %>
    <tr class="<%= cycle 'odd', 'even' %>">
      <td><%= ExperimentalPropertySet.human_attribute_name(col) %></td>
      <td><%= natural_product.send("experimental_#{col}") %></td>
      <td><%= natural_product.send("experimental_#{col}_reference") %></td>
    </tr>
    <% end -%>
  </tbody>
</table>