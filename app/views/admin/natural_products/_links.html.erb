<table>
  <thead>
    <tr>
      <th>Resource</th>
      <th>ID</th>
      <th>Link</th>
    </tr>
  </thead>
  <tbody>
    <% link_fields = column_names - [ 'id', 'natural_product_id', 'created_at', 'updated_at', 'omim' ] %>
    <% link_fields.sort.each do |col| %>
    <% resource = col.sub('_id', '') %>
    <% resource = "kegg_compound" if resource.to_s == 'kegg' %>
    <tr class="<%= cycle 'odd', 'even' %>">
      <td><%= LinkSet.human_attribute_name(col) %></td>
      <td><%= natural_product.link_set.send("#{col}") %></td>
      <td><%= bio_link_out resource, natural_product.link_set.send("#{col}") %></td>
    </tr>
    <% end -%>
  </tbody>
</table>