  = form_for(@custom_atom_number, :remote=>true, url: {controller: 'custom_atom_numbers', action: 'create'} ) do |f_m| 
      .modal-dialog.modal-l.modal-dialog-centered role="document"
        .modal-content
          .modal-header
            br
            .well-content
              = render partial: 'chemical_shift_submissions/custom_numbering_handling' 
                          
          .modal-body
            br
            / = f_m.text_field :custom_atom_id 
            .well-content
              .row
                .col-sm-8
                  h4 Submitted Structure with Atom Indexing
                .col-sm-4
                  h4 Enter Chemical Shift Data into the Table
              .row
                .col-sm-8 id="view_3d" style="z-index:0"
                  //- if @submission.renumberedMol
                    //div[class="mol_file" data-mol_file = "#{@submission.renumberedMol}"]
                    //= render partial: "shared/view_3D"
                  - if @submission.natural_product.threeDmol
                    div[class="mol_file" data-mol_file = "#{@submission.natural_product.threeDmol}"
                    data-custom_numbers = "#{@submission.custom_atom_numbers.to_json}"
                    data-applet_id = "jmolApplet1"]
                    = render partial: "shared/view_3D"
                  - else
                    = image_tag "#{@threeD_image_url}", class: 'featurette-image'
                // put headers in a separate cell so that position: sticky will work in Safari
                .col-sm-4
                  .grid-item-table
                    .table-responsive
                      .div style="max-height: 45vh; overflow: auto;"
                        table.table-bordered.table-hover.chemical-shifts#atom_table
                          thead id="submissions-custom-numbering-table-head"
                            tr
                              th.atom-col-head Atom Type
                              th.atom-col-head Atom No.
                              th.atom-col-head Custom Atom No.
                          tbody
                            - @custom_atom_numbers.each do |cs|
                              = f_m.fields_for @custom_atom_numbers, cs do |cs_f|
                                tr class="custom-number-input-row" id="atom-row-#{cs.atom_id}" data-row_num=cs.atom_id
                                  // Uneditable fields
                                  = f_m.hidden_field :atom_symbol, value:cs.atom_symbol, multiple: true
                                  = f_m.hidden_field :atom_id, value:cs.atom_id, multiple: true
                                  td.grey-col
                                    b
                                      = cs.atom_symbol
                                  td.grey-col
                                    b
                                      = cs.atom_id
                                  td.grey-col
                                    = f_m.text_field :custom_atom_id, multiple: true, value:cs.custom_atom_id,
                                            class: "custom-num-input", id: cs.atom_id,
                                            onChange: "updateLabel(this.id, this.value, true)",
                                            style: "width: 99%; margin-left: -1px", maxlength:"20"
                      br
                        .div display="flex" justify-content="space-between" id="submissions-modal-buttons"
                          button.btn.btn-success type="button" onclick="resetNumbering()" class="inline" style="margin-right:0.5em" Clear Numbering
                          button.btn.btn-success type="button" onclick="mapHydrogens()" class="inline" Map H
                          div style="display: inline-block; float: right"
                            button.btn.btn-default data-dismiss="modal" type="button" class="inline" style="margin-right:0.5em"  Cancel
                            button type='submit' value='s_s' name='state' id='btnSubmit' class='btn btn-success inline' Submit
  javascript:
      $(document).ready(function () {
          positionButtons();
          cbOn('pickCallback', 'isPicked');
          $("#btnSubmit").click(function () {
              $("body").removeClass("modal-open");
          });
          orderTable();
      });

      $(".custom-number-input-row").click(function (event) {
          selectAtom($(this).data('row_num'))
      });

      function positionButtons() {
          let one = document.getElementById("submissions-custom-numbering-table-head");
          let two = document.getElementById("submissions-modal-buttons");
          let style = window.getComputedStyle(one);
          let wdt = style.getPropertyValue('width');
          two.style.width = wdt;
          console.log(wdt)
      }

      function orderTable(){
          let ordering = atomOrdering();
          ordering.forEach(atom =>{
              let atomNo = atom.atomNo;
              atom["hydrogens"].forEach(hydrogen => {
                  $(`#atom-row-${hydrogen}`).insertAfter($(`#atom-row-${atomNo}`));
              });
          });
      }