.page-header: h1= title 'Classification Browse'

= render_filter_bars
= db_paginate(@search, entry_name: 'Classyfication')

= form_tag classyfication_path,
        method: :get, id: 'table-search-form', class: 'table-search' do
  - params_as_hidden_fields(request.query_parameters, ['q']).each do |field|
    = field.html_safe
  table.table.table-condensed.table-striped.taxonomy-list
    thead
      tr
        th
          | Compound Name
        th
          | Chemical Kingdom&nbsp
          i [title="Organic compounds"
            data-toggle="tooltip" data-placement="right" data-html="true"] = glyphicon('info-sign')
        th
          | Chemical Superclass&nbsp
          i [title="Organoheterocyclic compounds, Benzenoids, Organic acids and derivatives, \
          Lipids and lipid-like molecules, Organic oxygen compounds, Organic nitrogen compounds, \
          Phenylpropanoids and polyketides, Organosulfur compounds, Alkaloids and derivatives, \
          Organohalogen compounds, Hydrocarbons, Nucleosides, nucleotides, and analogues, Organometallic compounds, \
          Organic 1,3-dipolar compounds, Lignans, neolignans and related compounds, Organic Polymers, Organic salts, \
          Mixed metal/non-metal compounds, Organophosphorus compounds, Hydrocarbon derivatives, Acetylides, \
          Homogeneous non-metal compounds, Homogeneous metal compounds, Carbides, Allenes, Inorganic salts, \
          Miscellaneous inorganic compounds, Organopnictogen compounds, Organic cations, Carbenes, Organic anions"
            data-toggle="tooltip" data-placement="right" data-html="true"] = glyphicon('info-sign')
        th
          | Chemical Class&nbsp
          i [title="Benzene and substituted derivatives, Carboxylic acids and derivatives, Azoles, Organooxygen \
          compounds, Organonitrogen compounds, Pyridines and derivatives, Phenol ethers, Piperidines, Quinolines \
          and derivatives, Prenol lipids, Diazinanes, Indoles and derivatives, Diazines, Fatty Acyls, Pyrrolidines, \
          Diazanaphthalenes, Naphthalenes, Thioethers, Benzimidazoles, Cinnamic acids and derivatives, \
          Glycerophospholipids, Benzopyrans, Thiophenes, Glycerolipids, Stilbenes, Steroids and steroid derivatives, \
          Oxazinanes, Benzothiazoles, Isoindoles and derivatives, Pyrroles, Benzofurans, Thienopyrimidines, \
          Tetrahydroisoquinolines, Benzoxazines, Phenols, Benzodioxoles, Indanes, Tetralins, Imidazopyridines, \
          Benzodioxanes, Imidazopyrimidines, Organic sulfonic acids and derivatives, Isoquinolines and derivatives, \
          etc" data-toggle="tooltip" data-placement="right" data-html="true"] = glyphicon('info-sign')
        th
          | Chemical Subclass&nbsp
          i [title="Benzene and substituted derivatives, Carboxylic acids and derivatives, Azoles, \
          Organooxygen compounds, Organonitrogen compounds, Pyridines and derivatives, Phenol ethers, Piperidines, \
          Quinolines and derivatives, Prenol lipids, Diazinanes, Indoles and derivatives, Diazines, Fatty Acyls, \
          Pyrrolidines, Diazanaphthalenes, Naphthalenes, Thioethers, Benzimidazoles, Cinnamic acids and derivatives, \
          Glycerophospholipids, Benzopyrans, Thiophenes, Glycerolipids, Stilbenes, Steroids and steroid derivatives, \
          Oxazinanes, Benzothiazoles, Isoindoles and derivatives, Pyrroles, Benzofurans, Thienopyrimidines, \
          Tetrahydroisoquinolines, Benzoxazines, Phenols, Benzodioxoles, Indanes, Tetralins, Imidazopyridines, \
          Benzodioxanes, Imidazopyrimidines, Organic sulfonic acids and derivatives, Isoquinolines and derivatives, etc"
          data-toggle="tooltip" data-placement="right" data-html="true"] = glyphicon('info-sign')
        th
      tr.table-search-row
        th= index_search_text :name, placeholder: ' Search natural product name'
        th= index_search_text :kingdom, placeholder: ' Search kingdom'
        th= index_search_text :superklass, placeholder: ' Search superclass'
        th= index_search_text :klass, placeholder: ' Search class'
        th= index_search_text :subklass, placeholder: ' Search subclass'
        th.table-search-submit = table_search_actions
    tbody
      - @search.each do |hit|
        - cache [hit] do
          tr
            td= link_to hit.name, natural_product_path(id: hit.np_mrd_id)
            td= nah hit.kingdom
            td= nah hit.superklass
            td= nah hit.klass
            td= nah hit.subklass
            td= link_to 'Details',
                    natural_product_path(id: hit.np_mrd_id, anchor: 'chemo_taxonomy'),
                    class: 'btn btn-xs btn-info pull-right'

= db_paginate(@search, entry_name: 'Classyfication')