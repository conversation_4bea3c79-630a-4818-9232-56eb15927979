= "Under Construction"
//=render partial: 'submissions/warning'
//br
//h1 NP-NMR Deposition (Natural Product NMR Deposition)
//br
//.well-content.form-wrapper id="variable_html"
//  h2 <b>Input Form for Natural Product NMR Data<b>
//  br
//  = form_for @submission, :url => url_for(:controller => 'submissions', :action => 'create'), :remote => true do |f|
//    .well
//      =render partial: 'submissions/intro_nmr_deposition'
//    br
//    .well-content
//      =render partial: 'submissions/instructions'
//    h3 Please provide a structure and a name for your natural product and fill in the fields below:
//    br
//    .row
//      .col-sm-5
//          = moldbi_structure_drawer '', "structure_input", format: "smiles"
//          br
//          br
//          br
//          = link_to "#{glyphicon('play-circle')} MarvinJS Tutorials".html_safe, 'https://www.youtube.com///playlist?list=PLA3Ev2ngKC0TY2p59vJhlYGm-wmrLaWp6', class: 'btn btn-default pull-right', target: '_blank'
//      .col-sm-6.hide-overflow
//        = f.fields_for :submission_meta_data do |ff|
//          .row
//            .col-sm-7
//              .label-font-size
//                = label_tag :compound_name, "*Compound Name: "
//            .col-sm-1
//              = text_field_tag 'compound_name', nil, placeholder: "Common/IUPAC name", required: true
//          br
//          .row
//            .col-sm-7
//              = ff.label :genus, "*Organism Genus Name: "
//            .col-sm-1
//              = ff.text_field :genus, class: "form-control-plaintext", placeholder: "Genus name", required: true
//          br
//          .row
//            .col-sm-7
//              = ff.label :genus, "*Organism Species Name: "
//            .col-sm-1
//              = ff.text_field :species, class: "form-control-plaintext", placeholder: "Species name", required: true
//          br
//          .row
//            .col-sm-7
//              = ff.label :physical_state_of_compound, "Physical State of Compound: "
//            .col-sm-1
//              = ff.select :physical_state_of_compound, physical_state_of_compound_collection, {:include_blank => 'Select One'}
//          br
//          .row
//            .col-sm-7
//              = ff.label :melting_point, "Melting Point(°C): "
//            .col-sm-1
//              = ff.text_field :melting_point
//          br
//          .row
//            .col-sm-7
//              = ff.label :boiling_point, "Boiling Point(°C): "
//            .col-sm-1
//              = ff.text_field :boiling_point  
//          br    
//          .row
//            .col-sm-7
//              = ff.label :literature_reference_type, "*Literature Reference Type: ",  required: true
//            .col-sm-1
//              = ff.select :literature_reference_type, literature_reference_types_collection, {:include_blank => 'Select One'}, //{:required => true, :class => 'select'}
//          br
//          .row
//            .col-sm-7
//              = ff.label :literature_reference, "*Literature Reference: "
//            .col-sm-1
//              = ff.text_field :literature_reference, class: "form-control-plaintext", placeholder: "PubMed ID/DOI/Book", required: //true
//          br
//          .row
//            .col-sm-5
//              = f.button 'Next' , type: 'submit', name: 'state', value: 'post_np'
//