br
h1 NP-Chemical Shift Offline Bulk Deposition (Natural Product Chemical Shift Offline Bulk Deposition)
br
.well-content.form-wrapper id="variable_html"
  .well
    = render partial: 'batch_uploads/intro'
  h2 <b>Instructions for Offline Bulk Submission<b>
  br
  .well-content
    = render partial: 'batch_uploads/instructions'



  = form_for @batch_upload do |f|
    //= f.label :Upload
    = f.file_field :batch_file
    br
    = f.submit "Submit CSV File", style: "text-align:center"
 
    <!-- <div class="center jumbotron heading-jumbo">
      <%= f.label :Upload %>
      <%= f.file_field :sdf_file %>
    </div>
 
      <%= f.submit "Predict", style: "text-align:center"  %>
    <% end %>
  </div> -->

  <!-- <div>
    <ul class="nav nav-flex-column">
      <li class="nav-item">
        <h1 style="text-align:center"><a class="nav-link active" href= <%= main_app.new_nmr_pred_path %> ><u><strong>Import 3D SDF File<u></strong></a></h1>
      </li>
    </ul>
  </div> -->