.table-responsive
  table.table.table-striped.table-condensed.table-hover.submissions
    thead
      tr
        th Natural Product ID
        th Submission ID
        th Information
        th Download
        th View/Edit
    tbody
			- cache [chemical_shift_submission] do
			tr
				td
					- puts chemical_shift_submission.inspect
					- if chemical_shift_submission.natural_product_id
						- np = NaturalProduct.find(chemical_shift_submission.natural_product_id)
						- puts np
						=  np.id
					- else
						= "No ID"
				td
					- if np
						= np.name
						- if np.valid_thumb?
						    td.natural-product-structure
						    	 = image_tag(np.thumb.url)
					  	- else
						    td.natural-product-structure 
						    	= moldb_vector_thumbnail(np)
					- else
						= "No Name"
						td
			
				td
					-if np
						- if np.structure_resource
							= np.structure_resource.inchikey
						- else
							- if np.structure
								= np.structure
							- else
								= "No Structure Provided"
					- else
						= "No Structure Provided"
				td
					
					- md = ChemicalShiftSubmissionMetaData.find_by(chemical_shift_submission_id: chemical_shift_submission.id)
					- if md
						-if md.literature_reference
							= md.literature_reference
						- else
							= "Literature Ref N/A"
						br
						- if md.solvent
							= md.solvent
						- else
							= "Solvent N/A"
						br
						-if md.spectrometer_frequency
							= md.spectrometer_frequency
						- else
							= "Spectrometer Frequency N/A"
						br
						- if md.temperature
							= md.temperature
						- else
						 = "Temperature N/A"
						br
						- if md.plant_animal_microbal_species
							= md.plant_animal_microbal_species
						- else
							= Scientific Name "N/A"
			
				td
				 	- if chemical_shift_submission.chemical_shifts
				 		= "Download The Chemical Shift File"
			
				 	- else
				 		= "No imput provided"
				td
					= link_to "View/Edit", chemical_shift_submission
