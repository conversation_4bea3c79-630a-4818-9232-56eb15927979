h1
  b
    = "Thank you for submitting to NP-MRD, #{current_user.email.gsub(/@[^\s]+/,"").upcase}. Please verify your submission."
br
br
h2
  b
    = "Verification Form for Natural Product Chemical Shift Data"
br
= form_for @chemical_shift_submission do |f|

  table.padded
    tbody
      tr
        td
          b = "Creation Date:"
        td = @chemical_shift_submission.created_at.strftime("%Y-%m-%d")
      tr
        td
          b = "NP-MRD ID:"
        td = @natural_product.np_mrd_id
      tr
        td
          b = "Compound Name:"
        td = @natural_product.name
      tr
        td
          b = "Spectrum Type:"
        td = superscript_spectrum(@chemical_shift_submission.chemical_shift_submission_meta_data.spectrum_type)
  br
  = f.fields_for :chemical_shift_submission_meta_data do |ff|
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :provenance, "*Provenance: ",  required: true
        .col-sm-6
          = ff.select :provenance, provenance_collection, {:include_blank => 'Select One'}, {:required => true, :class => 'select', :id => "chemical_shift_provenance_verification", :style => "width: 100%; margin-left: 4px"}
      .col-sm-6
        .col-sm-6
          = ff.label :temperature, "Temperature (°C):"
        .col-sm-6
          = ff.number_field :temperature, style: "width: 100%", class: 'no-spin', step: :any
    #chemical_shift_genus_verification
      br
      .row
        .col-sm-6
          .col-sm-6
            = ff.label :genus, "*Organism Genus Name:"
          .col-sm-6
            = ff.text_field :genus, style: "width: 100%"
        .col-sm-6
          .col-sm-6
            = ff.label :species, "*Organism Species Name:"
          .col-sm-6
            = ff.text_field :species, style: "width: 100%"
    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :physical_state_of_compound, "Physical State of Compound:"
        .col-sm-6
          - if @meta_data.physical_state_of_compound.empty?
            = ff.select :physical_state_of_compound, physical_state_of_compound_collection, {:include_blank => 'Select One'}, style: "width: 100%; margin-left: 4px"
          - else
            = ff.select :physical_state_of_compound, physical_state_of_compound_collection, {},  {style: "width: 100%; margin-left: 4px"}

          - if @meta_data.physical_state_of_compound.nil?
            .validation_message
              = "* No selection provided"

      .col-sm-6
        .col-sm-6
          = ff.label :melting_point, "Melting Point (°C):"
        .col-sm-6
          = ff.number_field :melting_point, style: "width: 100%", class: 'no-spin', step: :any

          //- if @meta_data.melting_point == "NA"
          //  .validation_message
          //    = "* No melting point provided"
    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :boiling_point, "Boiling Point (°C):"
        .col-sm-6
          = ff.number_field :boiling_point, style: "width: 100%", class: 'no-spin', step: :any

          //- if @meta_data.boiling_point == "NA"
          //  .validation_message
          //    = "* No boiling point provided"
    br
    .row

      .col-sm-6
        .col-sm-6
          = ff.label :solvent, "NMR Solvent:"
        .col-sm-6
          = ff.select :solvent, solvent_collection, {}, {:style => "width: 100%; margin-left: 4px"}

          - if @meta_data.chemical_shift_standard == "TMS" && @meta_data.solvent == "H2O (Solvent-Peak)"
            .validation_message
              = "* Not compatible with chemical shift reference"

    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :literature_reference_type, "Literature Reference Type:"
        .col-sm-6
          = ff.select :literature_reference_type, literature_reference_types_collection,
                  {:include_blank => 'Select One'},
                  {:required => true, :class => 'select lit-type', :style => "width: 100%; margin-left: 4px"}
    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :spectrometer_frequency, "Spectrometer Frequency:"
        .col-sm-6
          = ff.select :spectrometer_frequency, spectrometer_frequency_collection, {},
                  {:style => "width: 100%; margin-left: 4px"}
    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :literature_reference, "Literature Reference:"
        .col-sm-6
          - if @meta_data.literature_reference_type == "Unpublished / Under Review"
            = ff.text_field :literature_reference, required: true, style: "width: 100%",
                    value: 'Unpublished / Under Review', disabled: true, class: 'lit-text'
          - else
            = ff.text_field :literature_reference, required: true, style: "width: 100%", class: 'lit-text'

            - if (@meta_data.literature_reference_type == "PMID") && (is_number?(@meta_data.literature_reference) == false)
              .validation_message
                = "* PubMed IDs must only contain numbers"
            - elsif (@meta_data.literature_reference_type == "DOI") && (@meta_data.literature_reference.match(/10.\d{4}.+/) == nil)
              .validation_message
                = "* DOIs must begin with a prefix with the form 10.NNNN, where NNNN is a four digit number"

    br
    .row
      .col-sm-6
        .col-sm-6
          = ff.label :chemical_shift_standard, "Chemical Shift Referencing Compound:"
        .col-sm-6
          = ff.select :chemical_shift_standard, chemical_shift_reference_collection, {},
                  {:style => "width: 100%; margin-left: 4px"}

          - if @meta_data.chemical_shift_standard == "TMS" && @meta_data.solvent == "H2O (Solvent-Peak)"
            .validation_message
              = "* Not compatible with solvent"
          - elsif (@meta_data.chemical_shift_standard == "DSS" || @meta_data.chemical_shift_standard == "TSP") && (@meta_data.solvent == "DMSO" || @meta_data.solvent == "CHCL3 (Solvent-Peak)" || @meta_data.solvent == "Acetone (Solvent-Peak)" || @meta_data.solvent == "Methane (Solvent-Peak)")
              .validation_message
                = "* Not compatible with solvent"
  br
  br
  .row
    .col-sm-4
      h4 Submitted Structure with Atom Indexing
    .col-sm-8
      h4 Verify the Chemical Shift Data in the Table

  br
  .row
    .col-sm-4 id="view_3d" style="z-index:0"
        - @css = ChemicalShiftSubmission.find(@chemical_shift_submission.id)
        - if @css.natural_product.threeDmol
          div[class="mol_file" data-mol_file = "#{@css.natural_product.threeDmol}"
            data-custom_numbers = "#{@chemical_shift_submission.custom_atom_numbers.to_json}"]
          = render partial: "shared/view_3D"
        - else
          = image_tag "#{@threeD_image_url}", class: 'featurette-image'
    // put headers in a separate cell so that position: sticky will work in Safari
    .col-sm-8
      .grid-item-table
        = render partial: 'chemical_shift_submissions/shift_table_verification', locals: {:atom_symbol => @atom_symbol, :chemical_shifts => @chemical_shifts, :f => f}
      .grid-item
        = "Assignment Quality Score "
        br
        = link_to "Quality Score rules", score_rules_chemical_shift_submission_path,
                'data-popup' => true, style: 'font-size: 20px'
        i [title="The assignment quality score is a relative measure of the assignment completeness and \
                  assignment correctness of an entry. All NP-MRD assignments are assessed based on the number of \
                  chemical shift assignments (relative to the expected number), the number of J-couplings and multiplet \
                  designations provided (relative to the expected number), the use of a chemical shift reference standard \
                  and the number of significant chemical shift assignment deviations from expected/predicted shifts \
                  (i.e., the number of potentially incorrect chemical shift assignments). The score that a compound \
                  entry receives is then scaled relative to all other entries in the NP-MRD.  An entry that gets 0% \
                  (worst) is among the least complete or has the highest number of potentially incorrect assignments \
                  relative to other entries in the NP-MRD. An entry that gets 100% (best) is among the most complete \
                  and has the fewest or essentially no incorrect assignments relative to other entries in the NP-MRD. \
                  The assignment quality score is relative, so as the quality of data in the NP-MRD improves (or diminishes) \
                  over time, the score that an entry receives will change. The assignment quality score is recalculated \
                  every week for every entry in the NP-MRD. Users should try to strive for an assignment quality score >75%."
          data-toggle="tooltip" data-placement="right" style="padding: 0 1em 0 0.5em"] = glyphicon('info-sign')
        = image_tag "#{@score_image_path}", class: 'featurette-image'
      .grid-item
        br
        = "I verify that the above information is correct."
        br
        // User submission
        .chemical-shifts-alert-radio
          = label_tag 'user_input', 'Yes'
          = radio_button_tag 'user_input', "Continue", true
          = label_tag 'user_input', 'No'
          = radio_button_tag 'user_input', "Cancel"

        br
        .row
          .col-sm-8
            = f.button 'Verified' , type: 'submit', name: 'state', value: 'post_verification',
                    :onclick => "submit_button_click()", class: 'btn btn-primary btn-lg'

javascript:
  if (($('#chemical_shift_provenance_verification').val() != "Other") || ($('#chemical_shift_provenance_verification').val() != "Biotransformation") || ($('#chemical_shift_provenance_verification').val() != "Chemical synthesis")){
    $('#chemical_shift_genus_verification').hide()
  } else {
    $('#chemical_shift_genus_verification').show()
  }
  $('#chemical_shift_provenance_verification').change(function () {
    if ($('#chemical_shift_provenance_verification').val() == "Other") {
      $('#chemical_shift_genus_verification').hide()
    } else if ($('#chemical_shift_provenance_verification').val() == "Biotransformation") {
      $('#chemical_shift_genus_verification').hide()
    } else if ($('#chemical_shift_provenance_verification').val() == "Chemical synthesis") {
      $('#chemical_shift_genus_verification').hide()
    } else {
      $('#chemical_shift_genus_verification').show()
    }
  })

  $(function () {
      if ($('.lit-text').length === 0) {
          return;
      }
      $('.lit-text').get(0).oninput = function (event) {
          return event.target.setCustomValidity('');
      };
      return $('.lit-type').change(function (e) {
          var invalid_message, lit_type, pattern;
          console.log("Lit type changed");
          lit_type = $('.lit-type').val();
          invalid_message = "";
          if (lit_type === 'DOI') {
              $('.lit-text').get(0).pattern = '10.\\d{4}.+';
              pattern = $('.lit-text').get(0).pattern;
              invalid_message = "Please provide a valid DOI";
          } else if (lit_type === 'PMID') {
              $('.lit-text').get(0).pattern = "\\d{7,8}";
              invalid_message = "Please provide a valid PMID";
              pattern = $('.lit-text').get(0).pattern;
          } else if (lit_type === 'Unpublished / Under Review') {
              $('.lit-text').get(0).value = 'Unpublished / Under Review';
              $('.lit-text').prop('disabled', true);
              return;
          } else {
              $('.lit-text').pattern = '.*';
          }
          if ($('.lit-text').get(0).value === 'Unpublished / Under Review') {
              $('.lit-text').get(0).value = "";
          }
          $('.lit-text').prop('disabled', false);
          $('.lit-text').get(0).title = invalid_message;
          return $('.lit-text').get(0).oninvalid = function (event) {
              return event.target.setCustomValidity(invalid_message);
          };
      });
  });


