.table-responsive
  table.table-bordered.table-hover.chemical-shifts
    thead
      tr
      th.atom-col-head Atom Type
      th.atom-col-head Atom No.
      - unless @custom_atom_numbers.select { |can| !can.custom_atom_id.nil? }.empty?
        th.atom-col-head Custom Atom No.
      th.atom-col-head Measured Shift<br>(ppm)
      th.atom-col-head Measured Multiplet Type
      th.atom-col-head Measured J-Coupling<br>( Hz )
      th.atom-col-head Assignment Level
      th.atom-col-head Assignment Quality Score
    tbody
      - @chemical_shifts.each do |cs|
        tr
          td.grey-col
            b
              = cs.atom_symbol
          td.grey-col
            b
              = cs.atom_id
          - unless @custom_atom_numbers.select { |can| !can.custom_atom_id.nil? }.empty?
            td.grey-col
              - if @custom_atom_numbers.select { |can| can.atom_id == cs.atom_id }.empty?
                = cs.atom_id
              - else
                = @custom_atom_numbers.select { |can| can.atom_id == cs.atom_id }[0].custom_atom_id
          td.grey-col
            = cs.chemical_shift_true
          td.grey-col
            = cs.multiplet_true
          td.grey-col
            = cs.jcoupling_true
          td.grey-col
            = cs.assignment_level
          td.grey-col
            = cs.assignment_score
