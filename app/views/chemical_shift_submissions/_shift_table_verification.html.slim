.table-responsive
  table.table-bordered.table-hover.chemical-shifts
    thead
      tr
        th.atom-col-head Atom Type
        th.atom-col-head Atom No.
        - unless @custom_atom_numbers.select { |can| !can.custom_atom_id.nil? }.empty?
          th.atom-col-head Custom Atom No.
        th.atom-col-head Predicted Shift<br>( ppm )
        th.atom-col-head Measured Shift<br>( ppm )
        th.atom-col-head Predicted Multiplet Type
        th.atom-col-head Measured Multiplet Type
        th.atom-col-head Predicted J-Coupling<br>( Hz )
        th.atom-col-head Measured J-Coupling<br>( Hz )
        th.atom-col-head Assignment Level
        th.atom-col-head Assignment Quality Score
    tbody
      - chemical_shifts.each do |cs|
        = f.fields_for :chemical_shifts, cs do |cs_f|
          // Uneditable fields
          = cs_f.hidden_field :atom_symbol
          = cs_f.hidden_field :atom_id
          = cs_f.hidden_field :chemical_shift_pred
          = cs_f.hidden_field :multiplet_pred
          = cs_f.hidden_field :jcoupling_pred

          tr
            td.grey-col
              b
                = cs.atom_symbol
            td.grey-col
              b
                = cs.atom_id
            - unless @custom_atom_numbers.select { |can| !can.custom_atom_id.nil? }.empty?
              td.grey-col
                - if @custom_atom_numbers.select { |can| can.atom_id == cs.atom_id }.empty?
                  = cs.atom_id
                - else
                  = @custom_atom_numbers.select { |can| can.atom_id == cs.atom_id }[0].custom_atom_id
            td.grey-col
              = cs.chemical_shift_pred
            td.white-col
              - if (cs.chemical_shift_true.to_f - cs.chemical_shift_pred.to_f).abs > 1.00
                - @shift_error =  "Some chemical shifts appear to be incorrectly assigned/not available, please check"
                .validation_message
                  = cs_f.text_field :chemical_shift_true
              - else
                = cs_f.text_field :chemical_shift_true
            td.grey-col
              = cs.multiplet_pred
            td.white-col
              - if cs.multiplet_pred == cs.multiplet_true
                = cs_f.text_field :multiplet_true
              - else
                - @multiplet_error =  "Some multiplets appear to be incorrectly assigned, please check"
                .validation_message
                  = cs_f.text_field :multiplet_true
            td.grey-col
              = cs.jcoupling_pred
            td.white-col
              - if cs.multiplet_true == "s" and !(cs.jcoupling_true == "0" or cs.jcoupling_true == "NA")
                - @coupling_error =  "Some coupling constants appear to be incorrectly assigned, please check"
                .validation_message
                  = cs_f.text_field :jcoupling_true, required: true
              - else
                - jcoupling_pred_array = cs.jcoupling_pred.split(',')
                - jcoupling_true_array = cs.jcoupling_true.split(',')
                - jcoupling_match_count = 0
                - jcoupling_true_array.each do |j_true|
                  - jcoupling_pred_array.each do |j_pred|
                    - if (j_pred.to_f - j_true.to_f).abs < 4.0
                      - jcoupling_match_count = jcoupling_match_count + 1
                      - break
                - if jcoupling_true_array.count != jcoupling_pred_array.count or jcoupling_pred_array.count != jcoupling_match_count
                  - @coupling_error =  "Some coupling constants appear to be incorrectly assigned, please check"
                  .validation_message
                    = cs_f.text_field :jcoupling_true
                - else
                  = cs_f.text_field :jcoupling_true
            td.grey-col
              = cs.assignment_level
            td.grey-col
              = cs.assignment_score
  .validation_message
    - if @shift_error
      = "* #{@shift_error}"
    br
    - if @multiplet_error
      = "* #{@multiplet_error}"
    br
    - if @coupling_error
      = "* #{@coupling_error}"

