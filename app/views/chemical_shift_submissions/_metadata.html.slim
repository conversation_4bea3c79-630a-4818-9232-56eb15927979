h2 <b>Input Form for Natural Product Chemical Shift Data</b>
= calculator_details_modal
/ br
/ = link_to_calculator_details(@chemical_shift_submission.id)
= form_for @chemical_shift_submission, :remote => true do |f|
  br
  table.padded
    tbody
      tr
        td
          b = "NP-MRD ID:"
        td = @chemical_shift_submission.natural_product.np_mrd_id
      tr
        td
          b = "Compound Name:"
        td = @chemical_shift_submission.natural_product.name
      tr
        td
          b = "Provenance:"
        td = @chemical_shift_submission.chemical_shift_submission_meta_data.provenance

      - if !(@chemical_shift_submission.chemical_shift_submission_meta_data.provenance == "Other" || @chemical_shift_submission.chemical_shift_submission_meta_data.provenance == "Biotransformation" || @chemical_shift_submission.chemical_shift_submission_meta_data.provenance == "Chemical synthesis")

        tr
          td
            b = "Organism Genus Name:"
          td = @chemical_shift_submission.chemical_shift_submission_meta_data.genus
        tr
          td
            b = "Organism Species Name:"
          td = @chemical_shift_submission.chemical_shift_submission_meta_data.species
            br


  = f.fields_for :chemical_shift_submission_meta_data do |ff|
    br
    .row
      .col-sm-6
        h2 Submitted Structure
      .col-sm-6
        h2 Fields Marked with '*' are Mandatory
    .row
      .col-sm-6
        br
        .col-sm-6 id="view_3d" style="z-index:0"
          - if @chemical_shift_submission.natural_product.threeDmol.present?
            div[class="mol_file" data-mol_file = "#{@chemical_shift_submission.natural_product.threeDmol}"
              data-custom_numbers = "#{@chemical_shift_submission.custom_atom_numbers.to_json}"]
            = render partial: "shared/view_3D" 
          - else
            = image_tag "#{@threeD_image_url}", class: 'featurette-image'
      .col-sm-6.hide-overflow
        br
        br
        br
        .row
          .col-sm-6
            = ff.label :solvent, "*NMR Solvent:"
          .col-sm-6
            = ff.select :solvent, solvent_collection, {:include_blank => 'Select One'},
                    {:required => true, :class => 'select', :style => "width: 100%; margin-left: 4px"}

        // Spectrum type cannot be edited once set
        - if !@chemical_shift_submission_meta_data.spectrum_type.present?
          br
          .row
            .col-sm-6
              = ff.label :spectrum_type, "*Spectrum Type:"
            .col-sm-6
              = ff.select :spectrum_type, spectrum_type_collection, {:include_blank => 'Select One'},
                      {:required => true, :class => 'select', :style => "width: 100%; margin-left: 4px"}
        - else
          br
          .row
            .col-sm-6
              = ff.label :spectrum_type, "*Spectrum Type:"
            .col-sm-6
              = ff.text_field :spectrum_type, :readonly => true, style: "width: 100%"
        br
        .row
          .col-sm-6
            = ff.label :spectrometer_frequency, "*Spectrometer Frequency:"
          .col-sm-6
            = ff.select :spectrometer_frequency, spectrometer_frequency_collection, {:include_blank => 'Select One'},
                    {:required => true, :class => 'select', :style => "width: 100%; margin-left: 4px"}
        br
        .row
          .col-sm-6
            = ff.label :temperature, "Temperature (°C):"
          .col-sm-6
            = ff.number_field :temperature, class: "form-control-plaintext no-spin",
                    placeholder: "If not known, use NA", style: "width: 100%", step: :any
        br
        .row
          .col-sm-6
            = ff.label :chemical_shift_standard, "*Chemical Shift Referencing Compound:"
          .col-sm-6
            = ff.select :chemical_shift_standard, chemical_shift_reference_collection, {:include_blank => 'Select One'},
                    {:required => true, :class => 'select', :style => "width: 100%; margin-left: 4px"}
        //br
        //.row
          //.col-sm-3
            //= calculator_details_modal
            //br
            //= link_to_calculator_details(@chemical_shift_submission.id)

        br

      .div id="renumber_buttons_div"
        = calculator_details_modal
        br
        = link_to_calculator_details(@chemical_shift_submission.id)
        = ff.button 'Next', type: 'submit', name: 'state', value: 'post_meta', :onclick => "submit_button_click()",
                class: 'btn btn-primary btn-lg', style: "margin-left: auto; margin-right: 10px"

    

