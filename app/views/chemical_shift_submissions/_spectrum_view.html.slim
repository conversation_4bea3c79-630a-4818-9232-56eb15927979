.well
  .intro
    h4 Spectrum View (Predicted Using Uploaded Data)
    = render "shared/spectra_viewer", spectra_path: @nmrml_path

.well
  .intro
    h4 Submitted Chemical Shifts
    = render "final_chemical_shifts"
    br
    = link_to "Quality Score rules", score_rules_chemical_shift_submission_path,
            'data-popup' => true, style: 'font-size: 20px'
    i [title="The assignment quality score is a relative measure of the assignment completeness and \
                  assignment correctness of an entry. All NP-MRD assignments are assessed based on the number of \
                  chemical shift assignments (relative to the expected number), the number of J-couplings and multiplet \
                  designations provided (relative to the expected number), the use of a chemical shift reference standard \
                  and the number of significant chemical shift assignment deviations from expected/predicted shifts \
                  (i.e., the number of potentially incorrect chemical shift assignments). The score that a compound \
                  entry receives is then scaled relative to all other entries in the NP-MRD.  An entry that gets 0% \
                  (worst) is among the least complete or has the highest number of potentially incorrect assignments \
                  relative to other entries in the NP-MRD. An entry that gets 100% (best) is among the most complete \
                  and has the fewest or essentially no incorrect assignments relative to other entries in the NP-MRD. \
                  The assignment quality score is relative, so as the quality of data in the NP-MRD improves (or diminishes) \
                  over time, the score that an entry receives will change. The assignment quality score is recalculated \
                  every week for every entry in the NP-MRD. Users should try to strive for an assignment quality score >75%."
      data-toggle="tooltip" data-placement="right" style="padding: 0 1em 0 0.5em"] = glyphicon('info-sign')
    = image_tag "#{@score_image_path}", class: 'featurette-image'

    table.table-standard
      thead
      .row
        .col-sm-6
          = link_to "Detailed Assignment Report", show_assignment_report_chemical_shift_submission_path


//.downloads
//  table.table-standard
//    thead
//      tr
//        th.data-set-col Quality Index
//        th.download-col Value(%)
//    tbody
//      tr
//        td No. of Atoms With Chemical Shift Data
//        td = @percentage_of_assigned_atoms
//      tr
//        td No. of Deposited Atoms With True Chemical Shift Data Similar to Predicted Values(Difference With Predcted Value < 0.20ppm)
//        td = @percentage_of_good_true_values
//
//      tr
//        td No. of Deposited Jcoupling Constant Data
//        td = @percentage_of_assigned_jcoupling
//
//      tr
//        td No. of Deposited Multiplet Data
//        td = @percentage_of_assigned_multiplet

.well
  .intro
    h4 Documentation
  .downloads
    - downloads = get_download_files
    table.table-standard
      thead
        tr
          th.data-set-col Documentation Description
          th.download-col Download Link
      tbody
        tr
          td Chemical Shift Assignments File
          td = nah link_to "Download CSV File", download_submitted_chemical_shift_path
        tr
          td Validation Report (SVG)
          - if downloads.has_key? 'validation'
            td = link_to "Download Validation Report",
                    root_url + downloads['validation'].to_s, download: downloads['validation'].to_s.split('/').last
          - else
            td = nah
        tr
          td nmrML-NMR Data Exchange Format File
          td = nah link_to "Download nmrML File", nmrML_path
        //tr
          //td Structure File
          //td = nah link_to "Download Mol File", download_renumbered_mol
        tr
          td JCAMP-DX Format File
          td = nah link_to "Download JCAMP-DX File", jcamp_path
