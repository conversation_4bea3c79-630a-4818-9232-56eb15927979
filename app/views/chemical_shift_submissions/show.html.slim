br
- if params[:notice].present?
  .alert-citation.alert
    p #{glyphicon(:'glyphicon-ok')} #{params[:notice]}

.page-header: h1 = title("NP-Chemical Shift Deposition (Natural Product Chemical Shift Deposition)")
//.well
//  .intro
//    h4 Spectrum View (Real)
//= "under construction"

//.well
//  .intro
//    h4 Spectrum View (Predicted Using Uploaded Data)

/ = render partial: 'chemical_shift_submissions/spectrum_view', locals: {:download_submitted_chemical_shift_path => download_submitted_data_chemical_shift_submission_path, :nmrML_path => download_nmrml_chemical_shift_submission_path, :percentage_of_assigned_atoms => @percentage_of_assigned_atoms, :percentage_of_good_true_values => @percentage_of_good_true_values, :percentage_of_assigned_jcoupling => @percentage_of_assigned_jcoupling, :percentage_of_assigned_multiplet => @percentage_of_assigned_multiplet}


= render partial: 'chemical_shift_submissions/spectrum_view', locals: {:download_submitted_chemical_shift_path => download_submitted_data_chemical_shift_submission_path, :nmrML_path => download_nmrml_chemical_shift_submission_path, :download_renumbered_mol => download_renumbered_mol_chemical_shift_submission_path,:file_path => @assignment_report_file_path, :jcamp_path => download_jcamp_chemical_shift_submission_path}



= link_to "Finish", submissions_path, :style=> 'float:right;', class: 'btn btn-primary btn-lg'
