// p = @external_submission.id
.page-header: h1
  = title("Showing Details for External Submission doi: ...")

table.content-table.table.table-condensed.table-bordered
    tbody
        = table_header_divider "Record Information"
        tr
            th Version
            td = Rails.application.config.version
        tr
            th= ExternalSubmission.human_attribute_name(:created_at)
            td = @external_submission.created_at
        tr
            th= ExternalSubmission.human_attribute_name(:updated_at)
            td = @external_submission.updated_at
        tr
            th Assigned Natural Product
            td = link_to "#{@natural_product.np_mrd_id}", "localhost:3001/natural_products/#{@natural_product.np_mrd_id}"
        tr 
            th Compound Name
            td = "#{@natural_product.name}"
        tr 
            th Compound Structure 
            td
                - if @natural_product.valid_thumb?
                    .structure = image_tag(@natural_product.thumb.url)
                - else
                    .structure = moldb_vector_thumbnail(@natural_product)
                .structure-links
                    = moldb_structure_links(@natural_product, skip_view=true, modal_format=true)
                    / Adding another button to the existing group and style it to look the same
                    .btn-group.btn-group-sm.structure-link-addons
                    = link_to "View in JSmol", jsmol_modal_path(@natural_product.np_mrd_id), class: 'btn btn-info',
                            data: { toggle: 'modal', target: '#structure-jsmol' }, id: '3d-btn'
                    - if valid_np_atlas(@natural_product)
                        = link_to "View NMR Assignments", jsmol_nmr_modal_path(@natural_product.np_mrd_id), class: 'btn btn-info',
                                data: { toggle: 'modal', target: '#nmr-jsmol' }, id: 'nmr-btn'
                    - if @natural_product.nmr_one_d_spectra&.find {|e| (e.notes.present?) && (e.notes.include? "DFT")}.present?
                        = link_to "View DFT Assignments", jsmol_dft_modal_path(@natural_product.np_mrd_id), class: 'btn btn-info',
                                data: { toggle: 'modal', target: '#dft-jsmol' }, id: 'dft-btn'
                    - if @conformer_spectra.present?
                        = link_to "3D Conformers", jsmol_conformer_modal_path(@natural_product.np_mrd_id), class: 'btn btn-info',
                                data: { toggle: 'modal', target: '#conformer-jsmol' }, id: 'dft-btn'
                    = render partial: 'jsmol_np_cards/jsmol_modal_target'
        tr 
            th Species of Origin 
            td
                - if @external_submission.origin_genus && @external_submission.origin_species
                    | #{@external_submission.origin_genus} #{@external_submission.origin_species}
                - elsif @external_submission.origin_genus
                    | #{@external_submission.origin_genus} spp.
                - else
                    | Not available
            
        = render '/specdbi/lists/external_nmr_submission', external_submissions: @external_submission

        = table_header_divider "References", id: 'references'
        tr
            th General References
            td.data-table-container = "#{@external_submission.citation_doi} (PubMed ID: #{@external_submission.citation_pmid})"