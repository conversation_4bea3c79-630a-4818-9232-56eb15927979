h1 NP-Chemical Shift Offline Bulk Deposition (Natural Product Chemical Shift Offline Bulk Deposition)
br
.well-content.form-wrapper id="variable_html"
  .well
    = render partial: 'batch_submissions/intro'
  h2 <b>Instructions for Offline Bulk Submission<b>
  br
  .well-content
    = render partial: 'batch_uploads/instructions'


  .row
    .col-sm-7  
      = form_for @batch_submission do |f|
        //= f.label :Upload
        = f.fields_for :batch_uploads do |ff|
          .file-selector
            = ff.file_field :batch_file, as: :file, required: true, accept: '.csv'
        br
        = f.submit "Submit CSV File", style: "text-align:center", :onclick => "submit_button_click()"
    //.col-sm-5
    //  br
    //  br
    //  = "Example CSV File"
 
    