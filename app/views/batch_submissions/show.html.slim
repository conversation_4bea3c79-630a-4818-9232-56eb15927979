.page-header: h1 = title("NP-Chemical Shift Offline Bulk Deposition (Natural Product Chemical Shift Offline Bulk Deposition)")

.well
  .intro
    = "Please download the generated Excel file to complete the submission."

.downloads
  table.table-standard
    thead
      tr
        th.data-set-col Documentation Description
        th.download-col Download Link
    tbody
      tr
        td Excel File for Each of The Submitted Compounds
        td = nah link_to "Download Excel File", downloadexcel_batch_upload_path
      //tr
        //td nmrML-NMR Data Exchange Format File
        //td = nah link_to "Download nmrML File", nmrML_path

.well-content.form-wrapper id="variable_html"
  .intro

    <p> When you are ready, please upload the <strong>'.zip'</strong> excel file to complete your submission. Please do not change the file name.</p><br>

  = form_for @batch_submission do |f|
    
    = f.fields_for :batch_uploads do |ff|
      .file-selector
        = ff.file_field :excel_file, as: :file, required: true,
                accept: '.zip'
    br
    = f.submit "Submit Excel File", style: "text-align:center"


br
br
= link_to "Back", submissions_path, :style=> 'float:right;', :class => 'btn btn-primary'