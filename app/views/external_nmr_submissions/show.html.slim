.page-header: h1 = title("#{@nmr_submission.description} (#{@natural_product.np_mrd_id})")
.col-md-10
  .panel.panel-info#details
    .panel-heading Spectrum Details
    .panel-body
      table.spec-table
        tr
          th NP-MRD ID:
          td = nah @natural_product.np_mrd_id
        tr
          th Compound name:
          td = nah @natural_product.name
        tr
          th Spectrum type:
          td = nah @nmr_submission.spectrum_type

  .panel.panel-info#spectrum
    .panel-heading Spectrum View (Experimental)
    .panel-body
      - if @nmr_submission.nmr_spectrum_type.include? '2D'
        = render "spectra_viewer_real_2d"
      - else
        = render "spectra_viewer_real"
  /
  .panel.panel-info#conditions
    .panel-heading Experimental Conditions
    .panel-body
      table.spec-table
        tr
          th Frequency:
          td = nah @nmr_submission.spectrometer_frequency
        tr
          th Lock Solvent:
          td = nah @nmr_submission.solvent
        / tr
        /   th Chemical Shift Reference
        /   td = nah @nmr_submission.chemical_shift_reference
        tr
          th Temperature
          td = nah @nmr_submission.temperature

  .panel.panel-info#documentation
    .panel-heading Documentation
    .panel-body
      table.table.table-condensed.table-striped.unpadded-table
        thead
          tr
            th Document Description
            th Download
        tbody
          tr
            td nmrML File
            td = nah link_to "Download file", @nmr_submission.nmrml_file.url
          tr
            td Raw Free Induction Decay (FID) File for Spectral Processing
            td = nah link_to "Download file", @nmr_submission.fid_file.url

  .panel.panel-info#references
    .panel-heading References
    / .panel-body
    /   - if @natural_product_mixture.literature_reference_type.downcase.include?('unpublished')
    /     .no-results = nah
    /   - else
    /     ol
    /       li = @natural_product_mixture.literature_reference

nav.sidenav.specdb-sidenav.col-md-2
  div data-spy="affix" data-offset-top="90"
    ul.nav.nav-pills.nav-stacked
      li.active = link_to 'Details', '#details'
      li = link_to 'Spectrum', '#spectrum'
      li = link_to 'Experimental Conditions', '#conditions'
      li = link_to 'Documentation', '#documentation'
      li = link_to 'References', '#references'