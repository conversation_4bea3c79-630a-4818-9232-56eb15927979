<p style="font-family:arial, sans-serif;font-size: 12;font-weight: 1">
  The <sup>13</sup>C NMR predictor accepts the molecular structure and returns the predicted <sup>13</sup>C chemical shift values in <strong>ppm</strong> using <strong><a href="https://nmrshiftdb.nmr.uni-koeln.de" target="_blank">nmrshiftbd2</a></strong>. Add the structure of your natural product by pasting a <b>SMILES</b> or <b>InChI</b> string into the <b>MarvinJS</b> structure drawing applet below. Alternatively, you can use the applet to draw your structure or to upload an <b>'.mol'</b> file. To learn how to draw a structure in MarvinJS, click on
  <%= link_to "#{glyphicon('play-circle')} MarvinJS Tutorials".html_safe, 'https://www.youtube.com/playlist?list=PLA3Ev2ngKC0TY2p59vJhlYGm-wmrLaWp6', class: 'btn btn-default', target: '_blank' %>
</p>
<!-- <p style="font-family:arial, sans-serif;font-size: 12;font-weight: 1; color:green; ">
  <strong>[Integration of 1H chemical shift prediction in chloroform and DMSO, and 13C chemical shift is under development]</strong>
</p> -->
