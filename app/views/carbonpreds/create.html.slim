.page-header: h1 = title("Prediction Result")
.well
  br
  .row
    .col-sm-6.hide-overflow
      h4 Submitted Structure
      br
      .col-sm-6 
      //- if @chemical_shift_submission.natural_product.threeDmol
      #view_3d [data-mol_file="#{@mol}"]
      = render partial: "shared/view_3D"
      //- else
      //  = image_tag "#{@threeD_image_url}", class: 'featurette-image'
    .col-sm-6.hide-overflow
      h4 Predicted &#185;&#179;C Chemical Shifts
      br
      br
      <table>
        <tr>
          <th>Carbon &nbsp &nbsp</th>
          <th>Chemical Shift(ppm)</th>
          <br />
        </tr>
        <tr>
          - @shift_position.each do |s|
            <tr>
              - if @reference == "DSS"
                <td> #{s[1]} &nbsp &nbsp &nbsp &nbsp </td>
                td = ((s[0].to_f)-2.66).round(2) 
              - elsif @reference == "TSP"
                <td> #{s[1]} &nbsp &nbsp &nbsp &nbsp </td>
                td = ((s[0].to_f)-2.78).round(2)
              - else
                <td> #{s[1]} &nbsp &nbsp &nbsp &nbsp </td>
                td = s[0]               
            </tr>
        </tr>
      </table>
  
  .row
    .col-sm-6.hide-overflow
      br
      = link_to "<button>Back to Home Page</button>".html_safe, main_app.root_path, :style=> 'color:black;float:left;'
  
    .col-sm-6.hide-overflow 
      br
      = link_to "<button>Predict another compound</button>".html_safe, new_carbonpred_path, :style=> 'color:black;float:right;'
