<div class="page-header">
  <h1><sup>13</sup>C NMR Predictor</h1>
</div>

<h2>What is the NMR predictor?</h2>
<div class="well">
  <div class="intro">
    = render :partial => "carbonpreds/intro"
    <p></p>
  </div>
</div>


  
.well-content.form-wrapper id="variable_html"
  h2 <b>Input form for &#185;&#179;C Chemical Shift Prediction<b>
  br
  = form_for @carbonpred, :url => url_for(:controller => 'carbonpreds', :action => 'create')  do |f|
    .row
      .col-sm-5
          = moldbi_structure_drawer '', "structure_input", format: "smiles"
      .col-sm-6.hide-overflow
        //= f.fields_for
        br
        br
        br
        br
        br
        br
        br
        br
        br
        /.row
          .col-sm-4
            = f.label :nucleus, "*Prediction type: "
          .col-sm-7
            = f.select :nucleus, nucleus_collection_carbon, { :include_blank => 'Select One', :selected => nucleus_collection_carbon[0]},
                    { :required => true, :class => 'select'}
        br
        .row
          .col-sm-4
            = f.label :chemical_shift_reference, "*Chemical Shift Reference: "
          .col-sm-7
            = f.select :chemical_shift_reference, reference_collection_carbon, {:include_blank => 'Select One', :selected => reference_collection_carbon[0]},
                    {:required => true, :class => 'select'}
        br
        .row
          .col-sm-2
            = link_to "#{glyphicon(:save)} Load Example 1".html_safe, "javascript:void(0)",
                    class: "btn btn-default carbon-preds-example-loader", 'data-smiles': "CC(N)C(O)=O",
                    'data-no-turbolink': true
          .col-sm-2
            = link_to "#{glyphicon(:save)} Load Example 2".html_safe, "javascript:void(0)",
                    class: "btn btn-default carbon-preds-example-loader",
                    'data-smiles': "CCOC(C)=O",
                    'data-no-turbolink': true
          .col-sm-7
            = f.button 'Predict', type: 'submit', :onclick => "submit_button_click()", class: 'btn btn-primary',
                    style: "margin-left: 3em"