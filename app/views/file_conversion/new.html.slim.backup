/  Filename: new.html.slim
/  Description: First page of the file conversion process, select a type of conversion and upload a file
/
/  Public Classes:
/    - N/A
/
/  Author: <PERSON>   Creation Date: 2022/06/14
/  Changes:

/ = form_tag({:action => :create}, {:multipart => true})
/     h1 [class="page-header"] 
/         | <b>Format Convert:</b>
/         = select_tag :conversion_type, options_for_select(%w[1D\ Spectrum\ (bruker/varian)\ to\ NMRML  2D\ Spectrum\ (processed,\ bruker/varian)\ to\ NMRML  NMRML\ to\ JCAMP  NMRML\ to\ NMREDATA  NMREDATA\ to\ NMRML]), :class => 'box-header'
/     br
/     .well-content.form-wrapper id="variable_upload_html" style="padding-left: 50px; height: fit-content;"
/         = render partial: 'convert_selector'
