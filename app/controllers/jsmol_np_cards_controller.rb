class JsmolNpCardsController < ApplicationController
  include Shared::NaturalProductLoader
  include NaturalProductsHelper


  layout false
  def new_modal
    @natural_product = load_natural_product
    redirect_to @natural_product and return if @natural_product.np_mrd_id != params[:id]

    @natural_product_structure = @natural_product.structure

    # Used to tell _view_3D.html.erb to load smiles or molfile
    @use_smiles = false
    # Choose how to load the mol
    # Prefer threeDmol, then smiles then structure unless it's an NPAtlas compound
    if valid_np_atlas_with_structure(@natural_product)
      @natural_product_structure = @natural_product.structure_resource.original_structure
    elsif !@natural_product.threeDmol.blank?
      @natural_product_structure = @natural_product.threeDmol
    elsif !@natural_product.smiles.blank?
      @natural_product_structure = standardize_smiles @natural_product.smiles
      @use_smiles = true
    else
      @natural_product_structure = @natural_product.structure
    end
  rescue ActiveRecord::RecordNotFound
    raise
  end

  def new_nmr_modal
    @natural_product = load_natural_product
    redirect_to @natural_product and return if @natural_product.np_mrd_id != params[:id]

    @assignments_link, @frequency = find_assignments @natural_product
    if @natural_product.structure_resource.present? && @natural_product.structure_resource.mol_3d.present?
      @natural_product_structure = @natural_product.structure_resource.mol_3d
    end

    raise StandardError if @assignments_link.nil?
    rescue ActiveRecord::RecordNotFound
      raise
  end

  def new_dft_modal
    @natural_product = load_natural_product
    redirect_to @natural_product and return if @natural_product.np_mrd_id != params[:id]

    @dft_assignments_link, @frequency = find_dft_assignments @natural_product
    if @natural_product.structure_resource.present? && @natural_product.structure_resource.mol_3d.present?
      @natural_product_structure = @natural_product.structure_resource.mol_3d
    end

    raise StandardError if @dft_assignments_link.nil? || @natural_product_structure.nil?
    rescue ActiveRecord::RecordNotFound
      raise
  end

  def new_conformer_modal
    @natural_product = load_natural_product
    conformer_spectra = @natural_product.nmr_one_d_spectra.select { |s| s.documents.any? { |r| r.description == 'xyz File'} }.first
    if conformer_spectra
      @conformer_file = conformer_spectra.documents.select {|d| d.description == 'xyz File' && d.name.include?('.xyz')}.first.url
    end
  end


  private

  # @abstract Standardizes a smiles string according to RDKit
  # @note This function is dependent on a python script, see function for details
  # @param [String] smiles the smiles string to standardize
  # @return [String] the standardized smiles string
  def standardize_smiles(smiles)
    smiles_py_script = 'rdkit_smiles_to_smiles.py'
    py_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    convert_smiles_path = Rails.root.join('public', 'python', smiles_py_script)
    # Execute the py script
    `#{py_path} #{convert_smiles_path} "#{smiles}"`

  end

  # Checks if a natural product meets criteria for being a NPAtlas compound with valid molfile
  # @param [NaturalProduct] natural_product the NP
  # @return [Boolean] if NP meets criteria or not
  def valid_np_atlas_with_structure(natural_product)
    return false if natural_product.structure_resource.nil? || natural_product.structure_resource.structure.nil?
    (!natural_product.comment.nil? &&
      natural_product.comment.include?('NPAtlas') &&
      natural_product.structure_resource.structure.include?("\n"))
  end

end
