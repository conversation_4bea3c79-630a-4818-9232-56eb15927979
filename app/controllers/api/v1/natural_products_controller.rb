module Api
  module V1
    class NaturalProductsController < ApplicationController
      before_action :authenticate_request!
      before_action :set_natural_product, only: [:update_export]
      before_action :set_natural_product_by_np_mrd, only: [:show_by_np_mrd]

      def show_by_np_mrd
        render json: NaturalProductSerializer.new(@natural_product).as_json
      end

      # PUT /api/natural_products/:id/update_export
      def update_export
        if !@natural_product.export && @natural_product.update(export: true)
          render json: { message: 'Export flag updated successfully' }, status: :ok
        elsif @natural_product.export
          render json: { message: 'Natural Product already exported before' }, status: :ok
        else
          render json: { errors: @natural_product.errors.full_messages }, status: :unprocessable_entity
        end
      end

      private

      def set_natural_product
        @natural_product = NaturalProduct.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { error: 'Natural product not found' }, status: :not_found
      end

      def set_natural_product_by_np_mrd
        @natural_product = NaturalProduct.find_by(np_mrd_id: params[:np_mrd_id])
        render json: { error: 'Natural product not found' }, status: :not_found unless @natural_product
      end
    end
  end
end