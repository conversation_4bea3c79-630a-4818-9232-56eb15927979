module Api  
  module V1
    class ChemicalShiftSubmissionsController < ApplicationController
      before_action :authenticate_request!
      before_action :set_chemical_shift_submission, only: [:update_moldb_id, :show]

      # GET /api/v1/chemical_shift_submissions/get_moldb_missing_spectra
      def get_moldb_missing_spectra
        data = []
        ChemicalShiftSubmission.includes(:natural_product, :chemical_shifts, :chemical_shift_submission_meta_data)
                               .where(moldb_id: nil, valid: true, export: true)
                               .where.not(natural_product_id: nil)
                               .find_each(batch_size: 100) do |submission|
          npmrd_id = submission.natural_product.np_mrd_id
      
          # Use the preloaded chemical_shifts to extract peaks
          peaks = submission.chemical_shifts.map(&:chemical_shift_true)
                                            .reject { |value| value.nil? || value == 'NA' || value.strip.empty? }
                                            .map(&:to_f)
                                            .uniq
                                            .sort
      
          if peaks.any?
            submission_data = submission.as_json(
              include: {
                chemical_shift_submission_meta_data: {}
              }
            )
            submission_data.merge!(npmrd_id: npmrd_id, peaks: peaks)
            data << submission_data
          end
        end
      
        render json: data
      end

      def show
        render json: @chemical_shift_submission
      end

      # PUT /api/v1/chemical_shift_submissions/:id/update_moldb_id?moldb_id=:moldb_id
      def update_moldb_id
        moldb_id = params[:moldb_id]

        if !@chemical_shift_submission.moldb_id.present?
          if @chemical_shift_submission.update(moldb_id: moldb_id)
            render json: { message: 'Moldb ID successfully updated' }, status: :ok
          else
            render json: { errors: @chemical_shift_submission.errors.full_messages }, status: :unprocessable_entity
          end
        else
          render json: { message: 'Moldb ID already exists' }, status: :ok
        end
      rescue => e
        # Log the error and return a generic error response
        Rails.logger.error("Moldb ID update failed: #{e.message}")
        render json: { error: 'Unexpected error occurred during processing.' }, status: :internal_server_error
      end   

      # PUT /api/v1/chemical_shift_submissions_controller/bulk_update_moldb_ids
      def bulk_update_moldb_ids
        # Parse the JSON payload
        updates = params.require(:updates)
      
        # Track records and errors
        updated_records = []
        errors = []
      
        updates.each do |update|
          submission = ChemicalShiftSubmission.find_by(id: update[:id])
          if submission && submission.update(moldb_id: update[:moldb_id])
            updated_records << submission
          else
            errors << { id: update[:id], error: submission&.errors&.full_messages || ["Not found"] }
          end
        end
      
        if errors.empty?
          render json: { message: 'All Moldb IDs successfully updated' }, status: :ok
        else
          render json: { errors: errors }, status: :unprocessable_entity
        end

      rescue => e
        # Log the error and return a generic error response
        Rails.logger.error("Bulk Moldb ID update failed: #{e.message}")
        render json: { error: 'Unexpected error occurred during processing.' }, status: :internal_server_error
      end      

      private

      def set_chemical_shift_submission
        @chemical_shift_submission = ChemicalShiftSubmission.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { error: 'Chemical Shift Submission not found' }, status: :not_found
      end
    end
  end
end