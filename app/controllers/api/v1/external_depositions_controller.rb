module Api  
  module V1
    class ExternalDepositionsController < ApplicationController
      include ExternalDepositionsHelper
      before_action :authenticate_request!

      def create
        begin
          submission_data = params[:submission_data]

          # Process the submission data...
          # TODO: change function to perform_later for sending job to sidekiq
          ExternalDepositionJob.perform_later(submission_data)

          render json: { status: 'success', message: 'Submission sent to processing!' }, status: :ok
        rescue => e
          render json: { status: 'error', message: e.message }, status: :unprocessable_entity
        end
      end

      def show
        external_deposition = ExternalSubmission.find(params[:id])
        render json: external_deposition
      rescue ActiveRecord::RecordNotFound
        render json: { error: 'External deposition not found' }, status: :not_found
      end
    end
  end
end
