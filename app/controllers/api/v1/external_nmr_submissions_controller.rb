module Api  
  module V1
    class ExternalNmrSubmissionsController < ApplicationController
      before_action :authenticate_request!
      before_action :set_external_nmr_submission, only: [:update_moldb_id, :show]

      # GET /api/v1/external_nmr_submissions/get_moldb_missing_spectra
      def get_moldb_missing_spectra
        external_nmr_submissions = ExternalNmrSubmission.includes(external_submission: :natural_product)
                                   .where(moldb_id: nil)
                                   .where.not(nmrml_file_file_name: nil)
        # Filter submissions to exclude those with empty extract_multiplet_centers
        data = external_nmr_submissions.each_with_object([]) do |submission, arr|
          npmrd_id = submission.external_submission.natural_product.np_mrd_id
          # Proceed only if peaks data is not empty
          if submission.peaks.present?
            submission_data = submission.as_json
            submission_data.merge!({ npmrd_id: npmrd_id })
            arr << submission_data
          end
        end.compact

        render json: data
      end
      

      def show
        render json: @external_nmr
      end

      # PUT /api/v1/external_nmr_submissions/:id/update_moldb_id?moldb_id=:moldb_id
      def update_moldb_id
        moldb_id = params[:moldb_id]

        if !@external_nmr.moldb_id.present?
          if @external_nmr.update(moldb_id: moldb_id)
            render json: { message: 'Moldb ID successfully updated' }, status: :ok
          else
            render json: { errors: @external_nmr.errors.full_messages }, status: :unprocessable_entity
          end
        else
          render json: { message: 'Moldb ID already exists' }, status: :ok
        end
      rescue => e
        # Log the error and return a generic error response
        Rails.logger.error("Moldb ID update failed: #{e.message}")
        render json: { error: 'Unexpected error occurred during processing.' }, status: :internal_server_error
      end   

      # PUT /api/v1/external_nmr_submissions_controller/bulk_update_moldb_ids
      def bulk_update_moldb_ids
        # Parse the JSON payload
        updates = params.require(:updates)
      
        # Track records and errors
        updated_records = []
        errors = []
      
        updates.each do |update|
          submission = ExternalNmrSubmission.find_by(id: update[:id])
          if submission && submission.update(moldb_id: update[:moldb_id])
            updated_records << submission
          else
            errors << { id: update[:id], error: submission&.errors&.full_messages || ["Not found"] }
          end
        end
      
        if errors.empty?
          render json: { message: 'All Moldb IDs successfully updated' }, status: :ok
        else
          render json: { errors: errors }, status: :unprocessable_entity
        end

      rescue => e
        # Log the error and return a generic error response
        Rails.logger.error("Bulk Moldb ID update failed: #{e.message}")
        render json: { error: 'Unexpected error occurred during processing.' }, status: :internal_server_error
      end      

      private

      def set_external_nmr_submission
        @external_nmr = ExternalNmrSubmission.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { error: 'External Nmr Submission not found' }, status: :not_found
      end
    end
  end
end
