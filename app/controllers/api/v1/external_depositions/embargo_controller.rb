module Api    
  module V1
    module ExternalDepositions
      class EmbargoController < ApplicationController
        before_action :authenticate_request!

        # Public method to handle the update of embargo status for compounds, peak list and experimental NMR
        def update
          begin
            # Parse the JSON data from the request body.
            submissions = JSON.parse(request.body.read)
            data_sent = submissions

            # if submissions.is_a?(Array)
            #   Rails.logger.info "Received array: #{submissions}"
            #   submissions = submissions.first
            # elsif submissions.is_a?(Hash)
            #   Rails.logger.info "Received hash: #{submissions}"
            # end

            # if submissions.key?("submission_data")
            #   data_sent = submissions["submission_data"]
            # elsif submissions.key?("_json")
            #   data_sent = submissions["_json"]
            # end

            # if data_sent.nil?
            #   # If no data is found, render an error message.
            #     render json: { error: "No submission data found: received keys [#{submissions.keys.join(', ')}] but expected [submission_data] or [_json]." }, status: :unprocessable_entity
            #   return
            # end

            # Process each submission in the array.
            updated_submissions = data_sent.map do |submission_data|

              # Find the external submission by its UUID.
              external_submissions = ExternalSubmission.where(submission_uuid: submission_data["submission_uuid"])
              external_submissions.each do |external_submission|
                begin

                  # Update external submission with newest embargo data.
                  external_submission.assign_attributes(
                    embargo_status: submission_data["embargo_status"],
                    embargo_date: submission_data["embargo_date"]
                  )
                  external_submission.save!

                  # Update the submission data with the latest embargo status.
                  submission_data.merge!(create_ingestion_hash(submission_data["embargo_release_ready"], submission_data["embargo_status"]))

                  # Process each compound data to update the corresponding models.
                  updated_compounds = submission_data["compounds"].map do |compound_data|
                    update_compound(compound_data, external_submission)
                  end

                  # Update the compounds array in the submission data.
                  submission_data["compounds"] = updated_compounds
                rescue => e
                  # Handle any exceptions and log the error.
                  submission_data.merge!(create_ingestion_hash([], [], e))
                end
              end
        
              submission_data
            end

            # Render the updated submissions as JSON.
            render json: updated_submissions
          rescue => e
            # Handle any exceptions and render an error message.
            Rails.logger.error "External Depositions Embargo Controller : Failed #{e.message} #{e.backtrace.join("\n")}"
            render json: { error: e.message }, status: :unprocessable_entity
          end
        end  

        private

        # Creates a hash detailing ingestion status, which will then be merged into submission data.
        def create_ingestion_hash(release_ready, embargo_status, error=nil)
          ingestion_hash = {
            "embargo_npmrd_db_release_status" => "",
            "embargo_npmrd_db_ingestion_successful" => false,
            "embargo_errors" => []
          }

          if error
            # If ingestion fails, then we log the error to embargo_errors array.
            ingestion_hash["embargo_errors"] << { error: "embargo_error", message: error.message }
          else
            # Otherwise, we set the ingestion status to true and update the release status depending on JSON data given.
            ingestion_hash["embargo_npmrd_db_ingestion_successful"] = true

            # Embargo status can be "do_not_release", "release_immediately", "embargo_until_date", or "embargo_until_publication".
            # These are only a response to the deposition website, and are not saved within the database.
            if embargo_status == "do_not_release"
              ingestion_hash["embargo_npmrd_db_release_status"] = "withdrawn"
            elsif embargo_status == "release_immediately"
              ingestion_hash["embargo_npmrd_db_release_status"] = "released"
            else
              ingestion_hash["embargo_npmrd_db_release_status"] = "embargoed"
            end
          end

          ingestion_hash
        end

        # Updates the deposition information for a given compound.
        def update_compound(compound_data, external_submission)
          compound = NaturalProduct.find_by(np_mrd_id: compound_data['npmrd_id'])
          # Update compound data if it exists and the embargo is ready.
          if compound && external_submission
            external_submission.assign_attributes(
              compound_embargo_release_ready: compound_data["compound_embargo_release_ready"]
            )
            external_submission.save! if external_submission.changed?

            # If the natural product is new it's fine. But if natural product exist and exported
            # we should not unexport it under any circumstances
            compound.assign_attributes(export: compound_data["compound_embargo_release_ready"]) unless compound.export
            compound.save! if compound.changed?

            # Update peak list and NMR metadata for the compound.
            compound_data["peak_lists"].each { |peak_list_data| update_peak_list(peak_list_data) }
            compound_data["nmr_metadata"].each { |nmr_metadata_data| update_nmr_metadata(nmr_metadata_data) }

            # Mark the NPMRD release status as 'released'.
            compound_data["compound_npmrd_db_release_status"] = compound.db_release_status
          end
          return compound_data
        end

        # Updates the peak list data.
        def update_peak_list(peak_list_data)
          # Find the peak list by its UUID and update if it exists.
          peak_list = ChemicalShiftSubmission.find_by(peak_list_uuid: peak_list_data["peak_list_uuid"])
          if peak_list
            peak_list.update_columns(
              peak_list_embargo_release_ready: peak_list_data["peak_list_embargo_release_ready"],
              export: peak_list_data["peak_list_embargo_release_ready"]
            )
            peak_list.save! if peak_list.changed?
          
            peak_list_data["peak_list_npmrd_db_release_status"] = peak_list.db_release_status
          end
          peak_list_data
        end

        # Updates the NMR metadata.
        def update_nmr_metadata(nmr_metadata_data)
          # Find the NMR metadata by its UUID and update if it exists.
          nmr_metadata = ExternalNmrSubmission.find_by(spectrum_uuid: nmr_metadata_data["spectrum_uuid"])
          if nmr_metadata
            nmr_metadata.update_columns(
              spectrum_embargo_release_ready: nmr_metadata_data["spectrum_embargo_release_ready"],
              export: nmr_metadata_data["spectrum_embargo_release_ready"]
            )
            nmr_metadata.save! if nmr_metadata.changed?
          
            nmr_metadata_data["spectrum_npmrd_db_release_status"] = nmr_metadata.db_release_status
          end
          nmr_metadata_data
        end
      end
    end
  end
end
