module Api
  module V1
    module ExternalDepositions
      class FilesController < ApplicationController
        # Skip CSRF token verification as this is an API controller
        skip_before_action :verify_authenticity_token

        def download
          file_path = Rails.root.join('public', 'downloads', 'depositions.zip') # Update this path to your file's location
          if File.exist?(file_path)
            send_file file_path, type: 'application/zip', disposition: 'attachment'
          else
            render json: { error: 'File not found' }, status: :not_found
          end
        end
      end
    end
  end
end
