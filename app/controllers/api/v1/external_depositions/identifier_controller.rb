module Api    
  module V1
    module ExternalDepositions
      class IdentifierController < ApplicationController
        include ExternalDepositionsHelper
        before_action :authenticate_request!

        # Public method to handle the update of identifier for submissions
        def update
          begin
            # Parse the JSON data from the request body.
            submissions = JSON.parse(request.body.read)
            updated_submissions = submissions["submission_data"].map do |submission_data|
              # Find the external submission by its UUID.
              external_submissions = ExternalSubmission.where(submission_uuid: submission_data["submission_uuid"])
              
              unless external_submissions.empty?
                external_submissions.each do |external_submission|
                  # Update external submission data if it exists.
                  begin
                    # Update and save external submission with new identifiers from JSON data.
                    external_submission.assign_attributes(
                      citation_doi: submission_data["submission_doi"],
                      citation_pii: submission_data["submission_pii"],
                      citation_pmid: submission_data["submission_pmid"]
                    )
                    external_submission.save! 

                    # Update the response JSON accordingly based on the new identifiers provided.
                    changed_attrs = {}
                    changed_attrs["citation_doi"] = submission_data["submission_doi"] if submission_data["submission_doi"] != nil
                    changed_attrs["citation_pii"] = submission_data["submission_pii"] if submission_data["submission_pii"] != nil
                    changed_attrs["citation_pmid"] = submission_data["submission_pmid"] if submission_data["submission_pmid"] != nil
                    submission_data.merge!(create_ingestion_hash(changed_attrs))
                  rescue => e
                    handle_submission_error("Identifier Controller", external_submission.submission_uuid,
                      e, external_submission)
                    submission_data.merge!(create_ingestion_hash([], e))
                  end
                end
              end
              
              submission_data
            end
        
            # Render the updated submissions as JSON.
            render json: updated_submissions
          rescue => e
            # Handle any exceptions and render an error message.
            Rails.logger.error "External Depositions Identifier Controller : Failed #{e.message} #{e.backtrace.join("\n")}"
            render json: { error: e.message }, status: :unprocessable_entity
          end
        end  

        private

        def create_ingestion_hash(changed_attrs, error=nil)
          ingestion_hash = {
            "doi_ingested" => changed_attrs.include?('citation_doi'),
            "pii_ingested" => changed_attrs.include?('citation_pii'),
            "pmid_ingested" => changed_attrs.include?('citation_pmid'),
            "identifier_ingestion_errors" => []
          }

          ingestion_hash["identifier_ingestion_errors"] << { error: "identifier_error", message: error.message } if error
          ingestion_hash
        end
      end
    end
  end
end
