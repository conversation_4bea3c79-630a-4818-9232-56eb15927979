module Api  
  module V1
    class UsersController < ApplicationController
      before_action :authenticate_request!

      # Renders a JSON response with a JWT token for the current user.
      def show
        render json: { token: encode_jwt(@current_user) }
      end

      private

      # Encodes a JWT token for a user. Sets the payload with the user's details and 
      # encodes a JWT token with the payload and secret key.
      def encode_jwt(user)
        payload = { 
          user_id: user.id,
          name: user.name,
          email: user.email,
          organization: user.organization 
        }
        JWT.encode(payload, Rails.application.secrets.jwt_secret_key, 'HS256')
      end
    end
  end
end
