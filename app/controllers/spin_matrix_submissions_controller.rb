class SpinMatrixSubmissionsController < ApplicationController
  require 'zip'

  def new
    @spin_matrix_submission = SpinMatrixSubmission.new()
  end

  def create
    smiles = params[:structure_input]
    solvent = params[:spin_matrix_submission][:solvent]
    fid_file = params[:spin_matrix_submission][:fid_file]
    @shifts = nil
    @couplings = nil
    @structure = nil
    @spin_matrix_submission = nil
    Dir.mktmpdir do |out_dir|
      generate_spin_matrix_files(smiles,
                                 solvent,
                                 fid_file,
                                 out_dir)
      @shifts, @couplings = extract_tables(File.join(out_dir, 'params.txt'))
      @structure_mol = File.read(File.join(out_dir, 'structure.mol'))
      @spin_matrix_submission = SpinMatrixSubmission.create({
                                                              spec_file: File.open(File.join(out_dir, 'spectrum.txt')),
                                                              mol_file: File.open(File.join(out_dir, 'structure.mol')),
                                                              param_file: File.open(File.join(out_dir, 'params.txt'))
                                                            })
    end
    @page = 'spin_matrix_tables'
  end

  def update
    @spin_matrix_submission = SpinMatrixSubmission.find(params[:id])
    write_tables(@spin_matrix_submission.param_file.path,
                 params[:shifts],
                 params[:couplings])
    mol_file_path = @spin_matrix_submission.mol_file.path
    param_file_path = @spin_matrix_submission.param_file.path
    spec_file_path = @spin_matrix_submission.spec_file.path
    Dir.mktmpdir do |out_dir|

      test_files(mol_file_path,
                 param_file_path,
                 spec_file_path)
      run_minimizer(param_file_path, spec_file_path, out_dir)
      make_json(mol_file_path,
                File.join(out_dir, 'fit1/run_5/params_final.txt'),
                spec_file_path,
                out_dir)
      @spin_matrix_submission.output_file = File.open(File.join(out_dir, 'output.json'))
      @spin_matrix_submission.save!
      @page = 'show'
    end
  end

  def extract_zip(file, destination)
    FileUtils.mkdir_p(destination)

    Zip::File.open(file) do |zip_file|
      zip_file.each do |f|
        fpath = File.join(destination, f.name)
        zip_file.extract(f, fpath) unless File.exist?(fpath)
      end
    end
  end

  def make_json(structure_file, params_file, spec_file, out_dir)
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    spin_matrix_output_script = Rails.root.join('backend', 'spin_matrix', 'make_output_json.py')
    spm_command = "#{python_path} #{spin_matrix_output_script}"

    spm_command << " #{structure_file} #{params_file} #{spec_file} #{File.join(out_dir, 'output.json')}"
    `#{spm_command}`
  end

  def run_minimizer(params_file, spec_file, work_dir)
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    spin_matrix_minimizer_script = Rails.root.join('backend', 'spin_matrix', 'minimizer.py')
    spm_command = "#{python_path} #{spin_matrix_minimizer_script}"
    spm_command << ' --np 2 --ns 4 --maxiter 4'
    spm_command << " --spec #{spec_file}"
    spm_command << " --param #{params_file}"
    spm_command << " --shifttol 0.10"  # might need to change later
    spm_command << " --outdir #{File.join(work_dir, 'fit1')}"
    `#{spm_command}`
  end

  def test_files(structure_file, params_file, spectrum_file)
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    spin_matrix_checker_script = Rails.root.join('backend', 'spin_matrix', 'check_input.py')
    spm_command = "#{python_path} #{spin_matrix_checker_script}"
    spm_command << " #{structure_file} #{params_file} #{spectrum_file}"
    `#{spm_command}`
  end

  def generate_spin_matrix_files(smiles, solvent, fid_file, out_dir)
    extract_zip(fid_file.path, out_dir)
    fid_file = File.join(out_dir, fid_file.original_filename.gsub('.zip', ''))
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    spin_matrix_creator_script = Rails.root.join('backend', 'spin_matrix', 'make_files.py')

    spm_command = "#{python_path} #{spin_matrix_creator_script}"
    spm_command << " --smiles \"#{smiles}\""
    spm_command << " --spec #{fid_file}"
    spm_command << " --solv #{solvent}"
    spm_command << " --outdir #{out_dir}"

    `#{spm_command}`
  end

  def write_tables(parameter_file, shifts, couplings)
    parse_shifts = false
    parse_couplings = false
    cur_line = 0
    Tempfile.create do |temp_file|
      File.foreach(parameter_file) do |line|
        if line.include? '#SHIFT'
          parse_shifts = true
        elsif line.include? '#COUPLINGS'
          cur_line = 0
          parse_couplings = true
        elsif line.include? '#EQUIV'
          parse_couplings = false
          parse_shifts = false
        elsif parse_couplings
          coupling = couplings[cur_line]
          unless coupling[:measured_coupling].empty?
            new_line = line.split
            new_line[2] = coupling[:measured_coupling]
            line = new_line.join(' ') << "\n"
            cur_line += 1
          end
        elsif parse_shifts
          shift = shifts[cur_line]
          new_line = line.split
          new_line[1] = shift[:label]
          new_line[2] = shift[:measured_shift] unless shift[:measured_shift].empty?
          line = new_line.join(' ') << "\n"
          cur_line += 1
        end
        temp_file.write(line)
      end
      temp_file.rewind
      File.open(parameter_file, 'w') do |f|
        f.write temp_file.read
      end
    end
  end

  def extract_tables(parameter_file)
    shifts = []
    couplings = []
    parse_shifts = false
    parse_couplings = false
    File.foreach(parameter_file) do |line|
      if line.include? '#SHIFT'
        parse_shifts = true
      elsif line.include? '#COUPLINGS'
        parse_couplings = true
      elsif line.include? '#EQUIV'
        parse_couplings = false
        parse_shifts = false
      elsif parse_couplings
        couplings << parse_couplings(line)
      elsif parse_shifts
        shifts << parse_shifts(line)
      end
    end
    [shifts, couplings]
  end

  def parse_couplings(line)
    line = line.split
    {
      id1: line[0],
      id2: line[1],
      coupling_val: line[2]
    }
  end

  def parse_shifts(line)
    line = line.split
    {
      id: line[0],
      atom_type: line[1][0],
      label: line[1],
      shift_val: line[2]
    }
  end
  def json_spin_matrix
    load_submission_object_by_user
    send_file session[:json_spin_matrix], type: 'application/json', disposition: 'attachment'
  end

end
