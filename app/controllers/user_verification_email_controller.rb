
class UserVerificationEmailController < ApplicationController
  before_action :load_user_using_perishable_token, only: [:edit, :update] 
  def new
    render
  end

  def create
    @user = User.find_by_email(params[:email])
    if @user
      puts "user= #{@user.email}"
      @user.deliver_verification_instructions!
      redirect_to(root_path, notice: 'Instructions have been emailed to you. Please check your email')
    else
      flash[:warning] = 'No user was found with that email address'.html_safe
      render :new
    end
  end
  
  private
  def  load_user_using_perishable_token
    puts "params[:id]= #{params[:id]}"
    @user = User.find_using_perishable_token(params[:id])
    unless @user
      flash[:notice] = 'It appears your account has already been verified or the verification link has expired. Please try signing in or contact support if you are having trouble accessing your account.'
      redirect_to(root_url)
    end
  end
end