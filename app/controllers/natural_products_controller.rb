class NaturalProductsController < ApplicationController
  include Shared::NaturalProductLoader
  include Shared::SubmissionLoader
  include Unearth::Filters::Filterable
  # include submission_loader


  SORTABLE_COLUMNS = %i(np_mrd_id name moldb_formula moldb_average_mass moldb_mono_mass).freeze

  # All the possible params passed to use filtering
  FILTER_PARAMS = %i[excellent satisfactory poor fungi plantae animalia bacteria archaea protozoa
                     chromista one_d_proton one_d_carbon two_d_proton hetero_carbon_proton
                     Water D2O CDCl3 CD3OD C5D5N DMSO acetone experimental simulated predicted
                     level_one level_two level_three level_four not_usable very_low acceptable
                     good very_good].freeze

  parse_sort_params(SORTABLE_COLUMNS, default_column: 'np_mrd_id', only: [:index])

  filter_by_groups :kingdom, :solvent, :spectral_type, :nuclei, :spectra_quality, :assignment_quality

  def index
    # Check if params has any of the filters keys. This is done because when we filter, we pass Spectrum
    # to apply_relation_filters and when we pluck, not all NP's will appear since not all NP's have spectra
    # If we don't have any filters, we have to load NP's normally
    if FILTER_PARAMS.any? { |k| params.key?(k) }
      filtered_spectra = apply_relation_filters SpectraFilter.exported
      @natural_products = load_natural_product_index_objects
                          .where(np_mrd_id: filtered_spectra.distinct.pluck(:np_mrd_id))
    else
      @natural_products = load_natural_product_index_objects.distinct
    end
    respond_to do |format|
      format.html
    end
  end

  def show
    @natural_product = load_natural_product
    if @natural_product.nmr_one_d_spectra.present?
      @conformer_spectra = @natural_product.nmr_one_d_spectra.select { |s| s.documents.any? { |r| r.description == 'xyz File' } }.first
    end

    redirect_to @natural_product and return if @natural_product.np_mrd_id != params[:id]

    respond_to do |format|
      format.html
      format.xml
    end

  rescue ActiveRecord::RecordNotFound
    raise
  rescue StandardError => e
    Rails.logger.error "NaturalProductsController#Show Action Error: #{e.message} #{e.backtrace}"
    redirect_to natural_products_path, alert: "An error occurred loading natural product details. #{e.message}"
  end

  def external_spectrum_view
    @nmr_submission = ExternalNmrSubmission.find(params[:id])
    @source = @nmr_submission.moldb_id.present? ? 'predicted' : 'experimental'
    @external_submission = @nmr_submission.external_submission
    @natural_product = @external_submission.natural_product
    @nmrml_path = @nmr_submission.nmrml_file.url
  end

  def spectrum_view
    @source = "experimental"
    @chemical_shift_submission = ChemicalShiftSubmission.where(:id => params[:id]).first
    @natural_product = NaturalProduct.find(@chemical_shift_submission.natural_product_id)
    nmrml_name  = "#{@chemical_shift_submission.id}_#{@natural_product.np_mrd_id}_#{@chemical_shift_submission.user_id}.nmrML"
    if @chemical_shift_submission.user_session_id.present?
      @nmrml_path = File.join('/downloads', @chemical_shift_submission.user_session_id, nmrml_name)
    else
      @nmrml_path = nil
    end

    render 'np_spectrum_view'

  end

  def submission_spectrum_view
    nmr_spectrum_type_current = params['spectrum_type']
    @submission = Submission.where(:id => params[:id]).first
    @natural_product = NaturalProduct.find(@submission.natural_product_id)
    @source = 'experimental'
    @two_d = @submission.submission_meta_data.spectrum_type.include? '2D'
    case nmr_spectrum_type_current
    when 'simulation'
      nmrml_name  = "#{@submission.id}_#{@natural_product.np_mrd_id}_#{@submission.user_id}.nmrML"
      @nmrml_path = File.join('/downloads', @submission.user_session_id, nmrml_name)
      @source = 'simulated'
      render 'np_spectrum_view'
    when 'real'
      spectrum_current_id = params['spectrum_id']
      nmr_spectrum = @submission.nmr_submissions.where(id: spectrum_current_id).first
      @file_list = []
      @name_list = []
      @quality_list = []
      @param_list = []
      sp_path = nmr_spectrum.nmr_file.url
      sp_dir = File.dirname(sp_path)
      solvent = nmr_spectrum.solvent
      spectrum_type = nmr_spectrum.nmr_spectrum_type
      reference = nmr_spectrum.chemical_shift_standard.gsub(' ', '_').gsub(/\(|\)/, "")
      frequency = nmr_spectrum.spectrometer_frequency || 'undefined'
      frequency = frequency.split(' ')[0] if frequency.include?(' ')
      unless @two_d
        sp_name = "#{spectrum_type}_#{solvent}_#{reference}_#{frequency}.json"
        sp_out_path = "#{sp_dir}/#{sp_name}"
        @file_list.append(sp_out_path)
        @param_list.append(read_param_sp(File.join('public', sp_out_path)))
      end
      sp_name_out = "#{spectrum_type}_#{solvent}_#{reference}_#{frequency}"
      sp_quality_name = "#{spectrum_type}_#{solvent}_#{reference}_#{frequency}.svg"
      sp_quality_path = "#{sp_dir}/#{sp_quality_name}"
      @name_list.append(sp_name_out)
      @quality_list.append(sp_quality_path)

      render 'np_spectrum_view_real'

    end
  end

  def view_3D
    @natural_product = load_natural_product
    redirect_to @natural_product and return if @natural_product.np_mrd_id != params[:id]

    @natural_product_structure = @natural_product.structure

    # Used to tell _view_3D.html.erb to load smiles or molfile
    @use_smiles = false

    # Choose how to load the mol
    # Prefer threeDmol, then smiles then structure
    if !@natural_product.threeDmol.blank?
      @natural_product_structure = @natural_product.threeDmol
    elsif !@natural_product.smiles.blank?
      @natural_product_structure = standardize_smiles @natural_product.smiles
      @use_smiles = true
    else
      @natural_product_structure = @natural_product.structure
    end

    respond_to do |format|
      format.html
      format.xml
    end

  rescue ActiveRecord::RecordNotFound
    raise
  end

  private

  # @abstract Standardizes a smiles string according to RDKit
  # @note This function is dependent on a python script, see function for details
  # @param [String] smiles the smiles string to standardize
  # @return [String] the standardized smiles string
  def standardize_smiles(smiles)
    smiles_py_script = 'rdkit_smiles_to_smiles.py'
    py_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    convert_smiles_path = Rails.root.join('public', 'python', smiles_py_script)
    # Execute the py script
    `#{py_path} #{convert_smiles_path} "#{smiles}"`

  end

end
