class BatchSubmissionsController < ApplicationController
  include Shared::BatchSubmissionLoader
  require 'fileutils'
  require 'open-uri'
  require 'zip'


  def index

  end

  def new
    if UserSession.try(:find).nil?
      redirect_away sign_in_path
    end
    @batch_submission = BatchSubmission.new
    @batch_upload = BatchUpload.new


  end

  def show
    session[:execl_file_with_path] = session[:execl_file_with_path]
    @execl_file_with_path = session[:execl_file_with_path]
    load_batch_submission_object_without_user_session
    # @excel_submission = @batch_submission.build_excel_submission
  end

  def create
    @batch_submission = BatchSubmission.new
    @batch_submission.user_id = current_user.id
    # @batch_submission.user_session_id = session[:session_id]
    @batch_submission.user_session_id = session.id
    @batch_submission.valid = 0
    # @user_session_id = session[:session_id]
    @user_session_id = session.id
    puts("@user_session_id = #{@user_session_id}")
    @batch_submission.save!

    @batch_submission.create_batch_upload(:batch_submission_id => @batch_submission.id)
    @batch_submission.batch_upload.update(:batch_file => params["batch_submission"]["batch_uploads"]["batch_file"])
    puts @batch_submission.inspect
    puts @batch_submission.batch_upload.inspect

    @batch_upload = BatchUpload.where(:batch_submission_id => @batch_submission.id).first
    puts("@batch_upload = #{@batch_upload}")
    #create session directory if not
    @session_directory=Rails.root.join('public','downloads',"#{@user_session_id}")
    
    Dir.mkdir @session_directory unless File.exists?(@session_directory)
    @batch_directory = Rails.root.join('public','downloads',"#{@user_session_id}","batch_upload")
    puts("\n\n\n@batch_directory = #{@batch_directory}")
    Dir.mkdir @batch_directory unless File.exists?(@batch_directory)

    
    session[:batch_id] = @batch_upload.id

    
  
    @captured_file = @batch_upload.batch_file.path # file name with path
    
    ####With Zip file
    #Zip::File.open(@captured_file) do |zip_file|
    #  # zip_file.first do |f|
    #  @fpath = File.join(@batch_directory, zip_file.first.name)
    #  if @fpath.end_with?(".csv")
    #    temp = @fpath.split("/")[-1]
    #    temp = temp.split(".csv")[0]
    #    @fpath = @fpath.split("#{temp}")[0]
    #    temp = "#{@batch_upload.id}"+"_#{temp}_#{current_user.id}.csv"
    #    @fpath = "#{@fpath}"+"#{temp}"
    #  else
    #    puts("Not a valid file")
    #    return
    #  end
    #  print("@fpath = #{@fpath}")
    #  zip_file.extract(zip_file.first, @fpath) unless File.exist?(@fpath)
    #  # end
    #end
    #######
    #### Without zip file ####
    @fpath = File.join(@batch_directory, @batch_upload.batch_file.original_filename)
    puts("@fpath at the beginning = #{@fpath}")
    if @fpath.end_with?(".csv")
      temp = @fpath.split("/")[-1]
      temp = temp.split(".csv")[0]
      @fpath = @fpath.split("#{temp}")[0]
      temp = "#{@batch_upload.id}"+"_#{temp}_#{current_user.id}.csv"
      @fpath = "#{@fpath}"+"#{temp}"
      puts("@fpath at the end = #{@fpath}")
    else
      puts("Not a valid file")
    end
    FileUtils.cp(@captured_file,@fpath)
    ###################

    @batch_values = BatchUpload.ParseCSV(@fpath,@batch_directory,@user_session_id)

    
    
    #### sidekiq ######
    @var = 1
    @backend_dir="backend"
    @nmr_pred_dir="nmr-pred"
    @draw_mol_script_basename="draw_mol.py"
    @backend_batch_dir = "batch_upload"
    @excel_generator_script_with_path = Rails.root.join("#{@backend_dir}","#{@backend_batch_dir}","excel_form_generation_current.py")
    
    #create a csv file with each generated file information which will be the input in the excel
    fpath_split = @fpath.split(".csv")[0]
    @project_name = @fpath.split("/")[-1].split(".csv")[0]
    @list_of_input_files_for_excel_generator = "#{fpath_split}_list_of_input_files_for_excel_generator.csv"
    
    #### sidekiq ######
  #  CSV.open("#{@list_of_input_files_for_excel_generator}", "wb") do |csv|  
  #    @batch_values.each do |b_v|
  #      @smiles = b_v[1]
  #      #creating mol, imgae and prediction file
  #      @path_to_temp_model, @path_to_temp_image, @path_to_csv, @compound_name = BatchUpload.DrawMol(@smiles,@batch_directory,@backend_dir,@nmr_pred_dir,@draw_mol_script_basename,@batch_upload.id,b_v[0],@batch_upload.user_id,@var)
  #      #creating mol, imgae and prediction file
  #      csv<<["#{@compound_name}","#{@path_to_csv}","#{@path_to_temp_image}"]
  #      @var = @var + 1
  #    end
  #  end
  #  execl_file_with_path = BatchUpload.GenerateExcel(@project_name,@excel_generator_script_with_path, @list_of_input_files_for_excel_generator, @batch_directory)
  #  print("execl_file_with_path = #{execl_file_with_path}")
  #  session[:execl_file_with_path] = "#{execl_file_with_path}"
  #  ###### sidekiq ####
    @batch_directory_string = "#{@batch_directory}"
    # GenerateMolExcelJob.perform_now(@backend_dir,@nmr_pred_dir,"#{@draw_mol_script_basename}",@backend_batch_dir,"#{@excel_generator_script_with_path}","#{@fpath}",@project_name,"#{@list_of_input_files_for_excel_generator}",@batch_values,@batch_submission.user_id,@batch_upload.id,@batch_directory_string,@batch_submission.id)
    GenerateMolExcelJob.set(wait: 2.minutes).perform_later(@backend_dir, @nmr_pred_dir, "#{@draw_mol_script_basename}",
                                                           @backend_batch_dir, "#{@excel_generator_script_with_path}",
                                                           "#{@fpath}",@project_name,
                                                           "#{@list_of_input_files_for_excel_generator}",
                                                           @batch_values, @batch_submission.user_id,
                                                           @batch_upload.id, @batch_directory_string,
                                                           @batch_submission.id,
                                                           @batch_upload)
    # GenerateMolExcelJob.perform_later(@backend_dir,@nmr_pred_dir,"#{@draw_mol_script_basename}",@backend_batch_dir,"#{@excel_generator_script_with_path}","#{@fpath}",@project_name,"#{@list_of_input_files_for_excel_generator}",@batch_values,@batch_submission.user_id,@batch_upload.id,@batch_directory_string,@batch_submission.id)
    #
    # GenerateMolExcelWorker.perform_async(@backend_dir,@nmr_pred_dir,"#{@draw_mol_script_basename}",@backend_batch_dir,"#{@excel_generator_script_with_path}","#{@fpath}",@project_name,"#{@list_of_input_files_for_excel_generator}",@batch_values,@batch_submission.user_id,@batch_upload.id,@batch_directory_string)
    
    respond_to do |format|
      format.html do
        redirect_to submissions_path,
                    notice: "Thank you for your submission. The pre-formatted spreadsheet should be available within
                             half an hour depending on the volume of data submitted. Please come back
                             after half an hour to download the generated excel file from the edit option of the Past
                             Submissions list. After downloading the spreadsheet,
                             please fill it in with the required information at your convenience."
      end
    end
  end
    
    # @msg = "under construction"
  def update
    load_batch_submission_object_without_user_session
    @batch_submission.batch_upload.update(:excel_file => params["batch_submission"]["batch_uploads"]["excel_file"])
    @batch_submission.save


    @batch_upload = BatchUpload.where(:batch_submission_id => @batch_submission.id).first
    @captured_excel_file = @batch_upload.excel_file.path # file name with path

    @batch_directory_excel = Rails.root.join('public','downloads',"#{@batch_submission.user_session_id}","batch_upload")
    Zip::File.open(@captured_excel_file) do |zip_file|
      # zip_file.first do |f|
      @fpath_excel = File.join(@batch_directory_excel, (zip_file.first.name).split(".xlsx")[0],"#{(zip_file.first.name).split(".xlsx")[0]}_extracted.xlsx")
      

      if @fpath_excel.end_with?(".xlsx")
        @fpath_excel = @fpath_excel #final extracted excel with path
      else
        puts("Not a valid file")
        return
      end
      
      zip_file.extract(zip_file.first, @fpath_excel) unless File.exist?(@fpath_excel)
      # end
    end
    @backend_dir="backend"
    @backend_batch_dir = "batch_upload"
    @excel_parser_script_with_path = Rails.root.join("#{@backend_dir}","#{@backend_batch_dir}","excel_form_parser_current.py")
    
    
    @excel_file_with_user_chemical_shifts_and_meta_data = @fpath_excel #final extracted excel with path
    @project_name_parser = @batch_upload.excel_file.original_filename.split(".xlsx")[0] # the folder where subfolders are created with arsed excel for per compound
    

    @existing_location_where_project_directory_will_be_created = Rails.root.join('public','downloads',"#{@batch_submission.user_session_id}","batch_upload")
    
    
    # @project_name_parser = @batch_upload.excel_file.original_filename.split(".xlsx")[0] # the folder where subfolders are created with parsed excel for per compound
    @parsed_excel_file_path = Rails.root.join('public','downloads',"#{@batch_submission.user_session_id}","batch_upload", "#{@project_name_parser}")
    
    
    ParseExcelJob.set(wait: 2.minutes).perform_later("#{@excel_parser_script_with_path}",@excel_file_with_user_chemical_shifts_and_meta_data,@project_name_parser,"#{@existing_location_where_project_directory_will_be_created}","#{@parsed_excel_file_path}")
    # 
    # ParseExcelJob.perform_now(@excel_parser_script_with_path,@excel_file_with_user_chemical_shifts_and_meta_data,@project_name_parser,@existing_location_where_project_directory_will_be_created,"#{@parsed_excel_file_path}")
    # BatchUpload.ParseExcel(@excel_parser_script_with_path,@excel_file_with_user_chemical_shifts_and_meta_data, @project_name_parser, @existing_location_where_project_directory_will_be_created)
    SaveParseExcelJob.set(wait: 2.minutes).perform_later("#{@parsed_excel_file_path}",@batch_upload.id)
    # SaveParseExcelJob.perform_now(@parsed_excel_file_path,@batch_upload.id)
    # BatchUpload.SaveNP(@parsed_excel_file_path)
    respond_to do |format|
      format.html { redirect_to batch_upload_path, notice: "Thank you for your submission. The depositon should be available in within half an hour depending on the volume of your deposition. Please check after half an hour to find the list of your submitted data." }
    end
  
  end

  def downloadexcel
    load_batch_submission_object_without_user_session
    execl_file_with_path = @batch_upload.generated_excel_location
    send_file "#{execl_file_with_path}", :type=>"application/excel", :filename => "#{execl_file_with_path}".split("/")[-1], :disposition => 'attachment'

  end

  def example
    example_file_with_path = Rails.root.join('backend','example.csv')
    send_file "#{example_file_with_path}", :type=>"application/csv", :filename => "example.csv", :disposition => 'attachment'

  end

  def tutorial
    tutorial_file_with_path = Rails.root.join('backend','NPMRD_batch_upload_tutorial.docx')
    send_file "#{tutorial_file_with_path}", :type=>"application/docx", :filename => "NPMRD_batch_upload_tutorial.docx", :disposition => 'attachment'

  end




 


end


