class CarbonpredsController < ApplicationController

  def new
  #  if UserSession.try(:find).nil?
  #    redirect_away sign_in_path
  #  end
    redirect_to "https://caspre.ca"

  end

  def index
  end

  def create
    @carbonpred = Carbonpred.new(carbonpred_params)
    # Rails.logger.debug "session[:session_id] = #{session[:session_id]["public_id"]}"
  #  Rails.logger.debug "session[:session_id] = #{session.id}"
    @carbonpred.save
    # @nmr_pred.user_session_id = session[:session_id]["public_id"]
  #  @carbonpred.user_session_id = session.id
    @carbonpred.save
  #  @user_session_id =  @carbonpred.user_session_id
    @user_session_id =  "carbon_pred"
    Rails.logger.debug "user_session_id = #{@user_session_id}"
    @session_directory=Rails.root.join('public','downloads',"#{@user_session_id}")
    Rails.logger.debug "Creating session_directory = #{@session_directory}"
  #  Dir.mkdir @session_directory unless File.exists?(@session_directory)
    @utility_directory = Rails.root.join('public','downloads',"#{@user_session_id}","utility")
    print("utility_directory = #{@utility_directory}")
    Dir.mkdir @utility_directory unless File.exists?(@utility_directory)

    #save data in table
    # @nmr_pred.user_session_id = "#{@user_session_id}"
  #  @carbonpred.user_id = current_user.id
    @carbonpred.smiles = params[:structure_input]
    @carbonpred.save!



    # @temp_mol_basename="#{@carbonpred.id}" + "_ mol.mol"
    # csv_basename="#{@carbonpred.id}" + "_#{current_user.id}" + "_mol.csv"
    # @mol_csv_url = File.join(@utility_directory,"#{@carbonpred.user_session_id}",csv_basename) 
    @mol_with_path = Carbonpred.DrawMol(params["structure_input"],"backend","nmr-pred","draw_mol.py",@carbonpred,@utility_directory) #.mol file with path
    Rails.logger.debug("@mol_with_path = #{@mol_with_path}")

    # Need only one mol creation script
    stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/rdkit_smiles_to_mol_nmr_pred.py '#{params[:structure_input]}'")
    @mol = stdout.gets(nil).to_s
    predictor = 'cascade'

    # This one is being used
    path_to_model = @mol_with_path
    session_directory = @utility_directory
    submission_prefix = "#{@carbonpred.id}" + "_#{@carbonpred.user_id}"
    @nmr_assignment = Carbonpred.run_nmrpred_intialprediction(path_to_model,session_directory,submission_prefix,predictor)
    Rails.logger.debug("@nmr_assignment = #{@nmr_assignment}")
    @shift_position = Carbonpred.PredictionResult(@nmr_assignment)
    @shift_position.pop
    @shift_position.delete_at(0)
    print("@shift_position = #{@shift_position}")
    @reference = @carbonpred.chemical_shift_reference

  end

  def show
  end

  private

  def carbonpred_params
    params.require(:carbonpred).permit(:nucleus,:chemical_shift_reference)
  end



end