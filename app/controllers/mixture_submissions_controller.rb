# Filename: mixture_submissions_controller.rb
# Description: Controller for the mixture deposition page, creates a new mixture submission for the database
#
# Public Classes:
#   - MixtureSubmissionController
#
# Author: <PERSON>   Creation Date:
# Changes:
#   - <PERSON>         Change Date: 2022/05/12
#     - Added detailed comments describing mixture deposition process (including file header)
#     - Added more fields as well as an additional page for certificates

class MixtureSubmissionsController < ApplicationController

  # Create a new controller with a corresponding mixture submission
  def new
    redirect_away sign_in_path if UserSession.try(:find).nil?  # Prompt login if user is not currently logged in
    @mixture_submission = MixtureSubmission.new()  # Create new mixture submission object for the database
    session[:mixture_submission_id] = @mixture_submission.id  # Set the current session mixture submission id to the one created in new()
  end

  # Fill out the mixture submission object with the required information
  def create
    # Grab the parameters of the mixture from the mixture deposition page
    npm_attributes = params[:mixture_submission][:natural_product_mixture]

    # Check what type of literature reference current mixture is
    if npm_attributes[:literature_reference_type] == "Unpublished / Under Review"
      npm_attributes[:literature_reference] = "Unpublished / Under Review"
    end

    # Create new natural product mixture from the fields on the mixture deposition page
    @natural_product_mixture = NaturalProductMixture.create(supplier_name: npm_attributes[:supplier_name],  # Who provided the mixture
                                                            source_name: npm_attributes[:source_name],  # Where the mixture came from
                                                            characterization: npm_attributes[:characterization],  # Procedure(s) that can be used to confirm the authenticity
                                                            impurities: npm_attributes[:impurities],  # Impurities/containments found in mixture
                                                            reproducibility: npm_attributes[:reproducibility],  # Data on batch-to-data reproducibility
                                                            description: npm_attributes[:description],  # Short description of what the mixture is
                                                            procedure: npm_attributes[:procedure],  # Short explanantion of mixture sample preparation procedure
                                                            composition: npm_attributes[:composition],  # The chemical composition of the mixture
                                                            # The type of state of the mixture: solid, liquid, gas
                                                            physical_state_of_mixture: npm_attributes[:physical_state_of_mixture],
                                                            # What solvent was used for the mixture
                                                            mixture_solvent: npm_attributes[:mixture_solvent],
                                                            ph: npm_attributes[:ph],  # Set the pH of the mixture
                                                            preparation_method: npm_attributes[:preparation_method],  # How the mixture was prepared
                                                            # Set the type of literature reference and its type
                                                            literature_reference: npm_attributes[:literature_reference],
                                                            literature_reference_type: npm_attributes[:literature_reference_type],
                                                            exported: false,  # Hides the mixture until it's submission is complete
                                                            mixture_type: params[:state]
                                                            )

    # Create new mixture submission object with the current session information
    @mixture_submission = @natural_product_mixture.create_mixture_submission(user_id: current_user.id,
                                                                              user_session_id: session.id)
    @mixture_submission.save!  # Save the submission to the database

    session[:npm_id] = @natural_product_mixture.id  # Update the session npm_id to the new natural product mixture id
    @page = 'mixture_certificate_file_submission'  # Go to mixture spectrum file upload page
  end

  def edit
    @mixture_submission = MixtureSubmission.find(params[:id])
    @natural_product_mixture = @mixture_submission.natural_product_mixture
    render 'edit.html.slim'
  end

  # Allows users to change an existing mixture submission
  def update
    case params[:state]  # Check what type of editting the user is attempting
    when 'post_meta'  # Changing the mixture submission fields
      # TODO: Implememt edit logic
      @mixture_submission = MixtureSubmission.find(params[:id])
      @natural_product_mixture = @mixture_submission.natural_product_mixture
      npm_attributes = params[:mixture_submission][:natural_product_mixture]

      # Check what type of literature reference current mixture is
      if npm_attributes[:literature_reference_type] == "Unpublished / Under Review"
        npm_attributes[:literature_reference] = "Unpublished / Under Review"
      end

      @natural_product_mixture.update(supplier_name: npm_attributes[:supplier_name])
      @natural_product_mixture.update(source_name: npm_attributes[:source_name])
      @natural_product_mixture.update(characterization: npm_attributes[:characterization])
      @natural_product_mixture.update(impurities: npm_attributes[:impurities])
      @natural_product_mixture.update(reproducibility: npm_attributes[:reproducibility])
      @natural_product_mixture.update(description: npm_attributes[:description])
      @natural_product_mixture.update(procedure: npm_attributes[:procedure])
      @natural_product_mixture.update(composition: npm_attributes[:composition])
      @natural_product_mixture.update(physical_state_of_mixture: npm_attributes[:physical_state_of_mixture])
      @natural_product_mixture.update(mixture_solvent: npm_attributes[:mixture_solvent])
      @natural_product_mixture.update(ph: npm_attributes[:ph])
      @natural_product_mixture.update(preparation_method: npm_attributes[:preparation_method])
      @natural_product_mixture.update(literature_reference: npm_attributes[:literature_reference])
      @natural_product_mixture.update(literature_reference_type: npm_attributes[:literature_reference_type])

      session[:npm_id] = @natural_product_mixture.id  # Update the session npm_id to the new natural product mixture id
      @page = 'mixture_certificate_file_submission'

    when 'post_mixture_certificate_file_submission'
      begin  # Error handling in case creating/updating/deleting a file causes issues
        @mixture_submission = MixtureSubmission.find(params[:id])  # Ensure that session ID is properly linked to current forms
        submission = params[:mixture_submission][:certificate_file]  # Retrieve data from certificate submission form

        case params[:mixture_submission][:mixture_certificate_type]  # Address the two different types of submission
        when 'Upload'  # When a certificate file was uploaded directly
          # Add the submitted file to the database
          @mixture_submission.update(certificate_file: submission[:certificate_file])
        when 'Input'  # When a user has filled the input fields as a submission
          # Create a new pdf, called Certificate.pdf, from the inputted data
          header = "#{submission[:product_name]}_certificate_input.pdf".tr(' ', '_')
          # In order to generate a pdf we use the gem Prawn: https://github.com/prawnpdf/prawn
          Prawn::Document.generate header do |pdf|
            pdf.text "Product Name: #{submission[:product_name]}"
            pdf.text "Date: #{submission[:date]}"
            pdf.text "Date of Manufacture: #{submission[:date_manufacture]}"
            pdf.text "Lot Number: #{submission[:lot_number]}"
            pdf.text "Expiration Date: #{submission[:date_expiration]}"
            pdf.text "Method of Analysis: #{submission[:method_of_analysis]}"
            pdf.text "Acceptance Criteria: #{submission[:acceptance_criteria]}"
            pdf.text "Result: #{submission[:result]}"
            pdf.text "Storage Conditions: #{submission[:storage_conditions]}"
            pdf.text "Authorizing Signature(s) and Date(s): #{submission[:authorizing_credentials]}"
          end
          # Create a file link to the newly created certificate
          File.open(header, 'r+') do |file|
            # Add the newly created certificate file to the database
            @mixture_submission.update!(certificate_file: file)
          end
          File.delete(header) if File.exists? header
        end
      rescue Exception
        flash[:alert] = "Something went wrong. Please try again."  # Display error message
        @page = 'mixture_certificate_file_submission'
      else
        @page = 'mixture_spectrum_file_upload'  # Go to mixture spectrum page
      end

    when 'post_mixture_spectrum_file_upload'  # Changing the uploaded spectrum files
      @natural_product_mixture = NaturalProductMixture.find(session[:npm_id])  # Get the natural product mixture from the database
      @mixture_submission = @natural_product_mixture.mixture_submission  # Extract the mixture submission from the natural product
      @mixture_nmr_submissions = @mixture_submission.mixture_nmr_submissions  # Extract the nmp submissions from the mixture submissions

      # Retrieve the information for the nmr submission form
      params["mixture_submission"]["mixture_nmr_submissions_attributes"].each_value do |nmr_submission|
        # Edit the path of submitted nmr file to remove any (), [], or {}
        File.rename(nmr_submission[:fid_file].path, nmr_submission[:fid_file].path.delete('()[]{} '))

        # Createa a new nmr submission object
        mixture_nmr_submission = @mixture_nmr_submissions.create!(nmr_spectrum_type: nmr_submission[:nmr_spectrum_type],  # Set the spectrum type
                                       chemical_shift_reference: nmr_submission[:chemical_shift_reference],  # Add the chemical shift reference
                                       spectrometer_frequency: nmr_submission[:spectrometer_frequency],  # Add the frequency of nmr
                                       lock_solvent: nmr_submission[:lock_solvent],  # Add the solvent used in the nmr experiment
                                       processed_spectrum_type: nmr_submission[:processed_spectrum_type],  # Add the processed spectrum type
                                       temperature: nmr_submission[:temperature],  # Set the temperature of nmr
                                       fid_file: nmr_submission[:fid_file])  # Attach the submitted file

        # Create a variable to hold the path to the output directory to store the nmrml file
        Dir.mktmpdir do |output_dir|
          # Create a nmrl file based on the mixture and the nmr into the output directory
          nmrml_file = generate_nmrml_file(@natural_product_mixture, mixture_nmr_submission, output_dir)
          mixture_nmr_submission.nmrml_file = File.open(nmrml_file)  # Finish update by setting nmrml file to the mixture
          mixture_nmr_submission.save!  # Save the updated submission to the database
        end
        @natural_product_mixture.publish  # Finishes up submission and reveals it to the rest of the database (exported = TRUE)
        # TODO: Add mailer
        # Notifier.completed_deposition(current_user().email, @natural_product_mixture.np_mrd_id, {'ValidationReport.zip': 'public/validation_report.zip'}).deliver_now
        @page = 'success'  # Go to success page
      end

    when 'remove'
      @natural_product_mixture = NaturalProductMixture.find(params[:id])
      @mixture_submission = @natural_product_mixture.mixture_submission

      @mixture_submission.update(certificate_file: nil)
      NaturalProductMixture.delete(@natural_product_mixture.id)
      MixtureSubmission.delete(@mixture_submission.id)

      @page = 'success'
    end
  end

  # Get the current user session
  def current_user_session
    return @current_user_session if defined?(@current_user_session)  # If defined return the user session
    @current_user_session = UserSession.find  # Define the user session
  end

  # Get the current user
  def current_user
    return @current_user if defined?(@current_user)  # Return user if defined
    return nil if UserSession.try(:find).nil?  # Check if the session is Null
    @current_user = current_user_session && current_user_session.record  # Set current based on session
  end

  # Create the necessary nmrml file for the mixture submission into the desired directory
  def generate_nmrml_file(natural_product_mixture, nmr_submission, output_dir)
    spectrum_type = nmr_submission.nmr_spectrum_type.gsub('-HSQC', "")  # Set spectrum type
    fid_path = nmr_submission.fid_file.path  # Get the file path of the submitted file
    source_name = natural_product_mixture.source_name.dup  # Get the source name of the mixture

    # Set the desired path for the nmrml file
    result_path = "#{output_dir}/#{nmr_submission.nmr_spectrum_type}_#{source_name.gsub!(/[^0-9A-Za-z]/, '')}.nmrml"

    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    nmrml_creator_script = Rails.root.join('backend','nmr_ml', 'nmrml_creator.py')

    # Start generating the nmrml file with the necessary fields
    nmrml_command = "#{python_path} #{nmrml_creator_script}"
    nmrml_command << " -output_path #{result_path}"
    nmrml_command << " -name #{natural_product_mixture.source_name}"
    nmrml_command << " -solvent '#{nmr_submission.lock_solvent}'"
    nmrml_command << " -freq '#{nmr_submission.spectrometer_frequency}'"
    nmrml_command << " -standard '#{nmr_submission.chemical_shift_reference}'"
    nmrml_command << " -temp #{nmr_submission.temperature}" if nmr_submission.temperature.present?
    nmrml_command << " -spec_type #{spectrum_type}"
    nmrml_command << " -phys_state #{natural_product_mixture.physical_state_of_mixture}"
    nmrml_command << " -ref_type '#{natural_product_mixture.literature_reference_type}'"
    nmrml_command << " -ref '#{natural_product_mixture.literature_reference}'"
    if  spectrum_type.split('')[0] == '2'
      nmrml_command << " -bruker_2d_zip #{fid_path}"
    elsif spectrum_type == "1D-1H"
      nmrml_command << " -json_1h_path '#{process_1d_fid nmr_submission}'"
    else
      nmrml_command << " -json_13c_path '#{process_1d_fid nmr_submission}'"
    end
    nmrml_command << " > #{output_dir}/nmr_creator.log"

    `#{nmrml_command}`

    # Remove the project_2D_Bruker_unzipped file
    FileUtils.rm_rf(Rails.root.join('project_2D_Bruker_unzipped'))

    result_path  # Return generated files
  end

  def process_1d_fid(nmr_submission)
    fid_dir = File.dirname(nmr_submission.fid_file.url)  # Get the directory where the file is stored
    script_dir  = Rails.root.join('backend','magmet')
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    script      = Rails.root.join('backend','magmet/processing_1d.py')

    fid_path = nmr_submission.fid_file.path
    frequency = nmr_submission.spectrometer_frequency.gsub( ' MHz', '')
    reference = nmr_submission.chemical_shift_reference
    spectrum_type = nmr_submission.nmr_spectrum_type
    solvent = nmr_submission.lock_solvent

    process_np = "#{python_path} #{script} -m #{script_dir}/ -i #{fid_path} -o #{fid_path} -f #{frequency}"
    process_np << " -ref #{reference} -sptype #{spectrum_type} -sol #{solvent}"
    `#{process_np}`

    "public#{fid_dir}/#{spectrum_type}_#{solvent}_#{reference}_#{frequency}.json"
  end

end