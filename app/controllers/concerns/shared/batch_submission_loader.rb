module Shared::BatchSubmissionLoader
  extend ActiveSupport::Concern

  #METABOLITE_SHOW_INCLUDES = [
   # :articles, :textbooks, :external_links, :accession_numbers
  #].freeze


  def load_batch_submission_object_without_user_session
    @batch_submission = BatchSubmission.where(:id => params[:id]).first
    @batch_upload = BatchUpload.where(:batch_submission_id => @batch_submission.id).first
    @chemical_shift_submissions = @batch_submission.chemical_shift_submissions
  end
end
