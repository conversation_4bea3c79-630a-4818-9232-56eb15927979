module Shared::ExternalSubmissionLoader
  extend ActiveSupport::Concern
  
  def load_external_submission
    external_submission_id = params[:id]

    @nmr_submission = ExternalNmrSubmission.find(external_submission_id)
    @source = @nmr_submission.moldb_id.present? ? 'predicted' : 'experimental'
    @external_submission = @nmr_submission.external_submission
    @natural_product = @external_submission.natural_product
  end
end