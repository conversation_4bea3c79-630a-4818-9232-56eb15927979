module Shared::ChemicalShiftSubmissionLoader
  extend ActiveSupport::Concern

  #METABOLITE_SHOW_INCLUDES = [
   # :articles, :textbooks, :external_links, :accession_numbers
  #].freeze

  def load_chemical_shift_submission_object_by_user
    @chemical_shift_submission = ChemicalShiftSubmission.where(:user_id => current_user.id, :id => params[:id]).first
    @natural_product = NaturalProduct.find(@chemical_shift_submission.natural_product_id)
  end

  def load_chemical_shift_submission_index_objects_by_user
    @chemical_shift_submissions = ChemicalShiftSubmission.where(:user_id => current_user.id).first
  end

  def load_chemical_shift_submission_object_without_user_session
    @chemical_shift_submission = ChemicalShiftSubmission.where(:id => params[:id]).first
    @natural_product = NaturalProduct.find(@chemical_shift_submission.natural_product_id)
  end
end
