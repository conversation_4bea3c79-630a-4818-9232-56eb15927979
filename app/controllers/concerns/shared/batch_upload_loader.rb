module Shared::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>oader
  extend ActiveSupport::Concern

  #METABOLITE_SHOW_INCLUDES = [
   # :articles, :textbooks, :external_links, :accession_numbers
  #].freeze

  def load_batch_upload_folder_name(b_id)
    batch_upload = BatchUpload.where(:batch_submission_id => b_id).first
    batch_submission = BatchSubmission.find(b_id)  
    file_name = batch_upload.batch_file.original_filename.split(".csv")[0]
    @folder_name = "#{batch_upload.id}_#{file_name}_#{current_user.id}"
    @execl_file_with_path = Rails.root.join("public", "downloads", "#{batch_submission.user_session_id}", "batch_upload" , "#{@folder_name}", "#{@folder_name}.xlsx")
    puts("execl_file_with_path = #{@execl_file_with_path}")
  end

  def load_batch_upload_object_without_user_session
    @batch_upload = BatchUpload.find(params[:id].to_i)
    @batch_submission = @batch_upload.batch_submission
    @chemical_shift_submissions = @batch_submission.chemical_shift_submissions
  end
end
