module Shared::NaturalProductLoader
  extend ActiveSupport::Concern

  METABOLITE_SHOW_INCLUDES = [
    :external_submissions_ordered, 
    :chemical_shift_submissions_ordered,
    :submissions_ordered,
    :articles,
    :textbooks,
    :external_links,
    :accession_numbers
  ].freeze

  # this funtion is used to show the metabocard of a particular natural product. This is called in a natural product controller "show action"
  def load_natural_product
    np_mrd_id = params[:id]
    if params[:id].include?("NP")
      natural_product = NaturalProduct.exported.find_by(np_mrd_id: np_mrd_id)
      # Logic here to find current user and if natural product is unexported, then find the user's natural product
      if current_user && natural_product.nil?
        natural_product = current_user.find_natural_product_by_np_mrd_id(np_mrd_id)
      end
    end
    if natural_product.nil?
      if accession = AccessionNumber.for_natural_products.find_by(number: np_mrd_id)
        return accession.element
      end
    end

    raise ActiveRecord::RecordNotFound if natural_product.nil?
    Moldbi::StructureResource
    Moldbi::CurationResource
    structure_thread = Thread.new do
      natural_product.has_structure?
    end
    curation_thread = Thread.new do
      natural_product.has_curation?
    end
    structure_thread.join
    curation_thread.join
    Rails.cache.fetch([natural_product, 'with-includes']) do
      NaturalProduct.includes(METABOLITE_SHOW_INCLUDES).find_by(id: natural_product.id)
    end
  end

  def return_3D_mol(received_structure)
    stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/smiles_to_canonical_mol.py '#{received_structure}'")
    read_mol = stdout.gets(nil).to_s
    return read_mol
  end

  def return_canonical_smiles(received_structure)
    stdin,stdout,stderr =Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/rdkit_smiles_to_canonical_smiles_withoutH.py '#{received_structure}'")
    smiles_in_canonical_form = stdout.gets(nil).to_s
    return smiles_in_canonical_form
  end


  def set_natural_product(np, structure)
    if np.input_smiles.blank?
      input_smiles_in_canonical_form = return_canonical_smiles(structure)
      np.input_smiles = input_smiles_in_canonical_form
    end

    if !np.threeDmol.present?
      read_three_d_mol = return_3D_mol(np.input_smiles)
      np.threeDmol = read_three_d_mol
    end

    np.save!
    np
  end

  def create_new_natural_product(name, structure)
    np = NaturalProduct.new(name: name, structure: structure)

    input_smiles_in_canonical_form = return_canonical_smiles(structure)
    np.input_smiles = input_smiles_in_canonical_form

    read_three_d_mol = return_3D_mol(np.input_smiles)
    np.threeDmol = read_three_d_mol

    np.save!
    np
  end

  # used in the deposition to either load the existing np or create a new np based on the situation
  # Grab from params from the deposition:
  # the chemox draw box returns ":structure_input" as smiles which you ca grab through -> params[:structure_input])
  # after grabing the smiles, call the load_create_natural_product function: # @natural_product, newly_created = load_create_natural_product(nil, params[:compound_name], params[:structure_input])
  # remember, creating a new compound means creating the metabocard too ( this is automatically done through the pipeline when creating new compound by the command "np.structure = smiles, np.save!")
  def load_create_natural_product(np_mrd_id, name, structure) # see here, we actually is not passing np_mrd_id, this parameter is still kept just incase for future use
    if np_mrd_id.present? # if np_mrd_id is already present that means no need to create a new compound, just load the existing one. which also means newly_created will be = false
      np = NaturalProduct.find_by(np_mrd_id: np_mrd_id)
    elsif name.present?
      np = NaturalProduct.find_by(name: name) #each exported natural product must have moldb_smiles
    elsif structure.present?
      np = NaturalProduct.find_by(moldb_smiles: structure)
      if !np
        input_smiles_in_canonical_form = return_canonical_smiles(structure)
        np = NaturalProduct.find_by(input_smiles: input_smiles_in_canonical_form)
      end
    end

    return set_natural_product(np, structure) if np.present?
    create_new_natural_product(name, structure)
  end

  # used in the deposition to either load the existing np or create a new np based on the situation
  # alternative to the previous load_create_natural_product where the order of finding structures is different
  # In this function, we check if np_mrd_id is present, compound_uuid next, inchikey next, structure next, name final
  # Grab from params from the deposition:
  # the chemox draw box returns ":structure_input" as smiles which you ca grab through -> params[:structure_input])
  # after grabing the smiles, call the load_create_natural_product function: 
  # @natural_product, newly_created = load_create_natural_product(nil, params[:compound_name], params[:structure_input])
  # remember, creating a new compound means creating the metabocard too 
  # this is automatically done through the pipeline when creating new compound by the command "np.structure = smiles, np.save!"
  def load_create_natural_product_for_deposition(np_mrd_id, name, structure, inchikey, external_submission)
    np = nil  # if np_mrd_id is already present that means no need to create a new compound, just load the existing one. which also means newly_created will be = false
    if np_mrd_id.present?
      np = NaturalProduct.find_by(np_mrd_id: np_mrd_id)
      unless np.nil?
        Rails.logger.info "ED DEBUG NPMRD ID PRESENT #{np_mrd_id} #{name} #{inchikey} #{structure}"
        external_submission.npmrd_match_status = 'npmrd_id'
      end
    end
    # TODO: Add compound_uuid based natural product identification. This doesnt work currently

    if inchikey.present? && name.present?
      if np.nil?
        # Full inchikey
        np = NaturalProduct.find_by('moldb_inchikey = ? AND lower(name) = ?', inchikey, name.downcase)
        unless np.nil?
          Rails.logger.info "ED DEBUG INCHIKEY AND NAME PRESENT #{np_mrd_id} #{name} #{inchikey} #{structure}"
          external_submission.npmrd_match_status = 'inchikey_name'
        end
      end

      if np.nil?
        # Flat inchikey
        flat_inchikey = inchikey.split("-")[0]
        np = NaturalProduct.find_by("moldb_inchikey LIKE ? AND lower(name) = ?", "#{flat_inchikey}%", name.downcase)
        unless np.nil?
          Rails.logger.info "ED DEBUG FLAT INCHIKEY AND NAME PRESENT #{np_mrd_id} #{name} #{inchikey} #{structure}"
          external_submission.npmrd_match_status = 'flat_inchikey_name'
        end
      end

      if np.nil?
        inchikey_np = NaturalProduct.find_by(moldb_inchikey: inchikey)
        name_np = NaturalProduct.find_by("lower(name) = ?", name.downcase)
        # TODO: Add synonym for name if a new name is generated. Is it feasible to work with bad quality synonyms
        if inchikey_np.present? && name_np.nil?
          Rails.logger.info "ED DEBUG INCHIKEY PRESENT #{np_mrd_id} #{name} #{inchikey} #{structure}"
          external_submission.npmrd_match_status = 'inchikey'
        elsif name_np.present?
          Rails.logger.info "ED DEBUG NAME PRESENT #{np_mrd_id} #{name} #{inchikey} #{structure}"
          external_submission.npmrd_match_status = 'name'
        end
      end
    else
      Rails.logger.error "No inchikey or name in External Deposition API"
      raise StandardError, "External Depesition API Error: No inchikey or name in exchange JSON"
    end

    if np.present?
      Rails.logger.info "ED DEBUG STRUCTURE FOUND #{np_mrd_id} #{name} #{inchikey} #{structure}"
      return set_natural_product(np, structure)
    end
    Rails.logger.info "ED DEBUG NEW STRUCTURE #{np_mrd_id} #{name} #{inchikey} #{structure}"
    external_submission.npmrd_match_status = 'new_entry' unless external_submission.npmrd_match_status.present?
    create_new_natural_product(name, structure)
  end

  def return_or_handle_iupac_name(structure)
    iupac_name_response = Jchem.structure_to_name(structure)
  
    # Check for HTTP error statuses in the response
    if iupac_name_response.include?('HTTP Status 404')
      Rails.logger.error "HTTP error received in response: #{iupac_name_response}"
      raise StandardError, "HTTP error from Jchem: #{iupac_name_response}"
    end
  
    # Attempt to parse the IUPAC name response as JSON to check for other errors
    begin
      parsed_response = JSON.parse(iupac_name_response)
      if parsed_response.key?('errorCode')
        # If the errorCode key exists, raise an error or handle it as needed
        Rails.logger.error "Error received from Jchem: #{parsed_response['errorMessage']}"
        raise StandardError, "Jchem API error: #{parsed_response['errorMessage']}"
      end
    rescue JSON::ParserError => e
      # If parsing fails, the response was not JSON, and you can proceed normally
      return iupac_name_response
    end
  end

  def load_natural_product_index_objects
    @natural_products = NaturalProduct.exported
                                      .includes(:one_d_spectra, :two_d_spectra, species_mappings: :species,
                                                valid_submissions: [:submission_meta_data, :nmr_submissions],
                                                valid_cs_submissions: :chemical_shift_submission_meta_data)
                                      .page(params[:page])
                                      .order(@sorted_column => @sorted_order)
  end

  def get_all_filtered_natural_products
    @all_filtered = NaturalProduct.exported.order(@sorted_column => @sorted_order)
    @all_filtered
  end

  def load_natural_product_with_id_param(np_mrd_id)
    np_mrd_id = np_mrd_id
    natural_product = NaturalProduct.exported.find_by(np_mrd_id: np_mrd_id)
    if natural_product.nil?
      if accession = AccessionNumber.for_natural_products.find_by(number: np_mrd_id)
        return accession.element
      end
    end

    raise ActiveRecord::RecordNotFound if natural_product.nil?
    Moldbi::StructureResource
    Moldbi::CurationResource
    structure_thread = Thread.new do
      natural_product.has_structure?
    end
    curation_thread = Thread.new do
      natural_product.has_curation?
    end
    structure_thread.join
    curation_thread.join
    Rails.cache.fetch([natural_product, 'with-includes']) do
      NaturalProduct.includes(METABOLITE_SHOW_INCLUDES).find_by(id: natural_product.id)
    end
  end
end