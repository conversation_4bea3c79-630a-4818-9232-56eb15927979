module Shared::CacheGenerator
    extend ActiveSupport::Concern

    def species_cache_key
        @cache_key ||=
          begin
            # Stringifying the params to create uniq cache key
            md5 =
              Digest::MD5.hexdigest(
                "#{params[:id]}|#{params[:order]}|#{params[:start].to_i}|#{params[:length].to_i}|#{"%#{params[:search]["value"]}%"}|#{@sorted_column}|#{@sorted_order}"
              )
            "species_browse/#{md5}"
          end
      end
end