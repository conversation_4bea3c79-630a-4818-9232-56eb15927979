class CustomAtomNumbersController < ApplicationController
  layout false
  def new_modal
    # @custom_atom_number = CustomAtomNumber.new()
    @chemical_shift_submission = ChemicalShiftSubmission.find((params[:id].to_i))

    @natural_product = NaturalProduct.find(@chemical_shift_submission.natural_product_id)
    temp_image_basename="#{@chemical_shift_submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_temp_3D.png"
    temp_mol_basename="#{@chemical_shift_submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_temp_3D.mol"
    @threeD_image_url = File.join("/downloads","#{@chemical_shift_submission.user_session_id}",temp_image_basename)


    
    csv_basename="#{@chemical_shift_submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_custom_table.csv"
    session_directory=Rails.root.join('public','downloads',"#{@chemical_shift_submission.user_session_id}")
    @output_csv_path_with_name = File.join("#{session_directory}", csv_basename)
    @input_threeD_mol_url = Rails.root.join('public','downloads',"#{@chemical_shift_submission.user_session_id}",temp_mol_basename)


    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path=Rails.root.join("public", "python", "npmrd_custom_atom_csv.py")

    python_log_basename="#{@chemical_shift_submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_custom_table.log"
    python_log_abs_path=Rails.root.join("#{session_directory}",python_log_basename)

    script_command = ""
    script_command += "#{python_path} "
    script_command += "#{script_path} "

    script_command += "-input_mol #{@input_threeD_mol_url} "
    script_command += "-output_mol #{@output_csv_path_with_name} "

    script_command += ">> #{python_log_abs_path} "

    puts "script_command = #{script_command}"
    `#{script_command}`


   
    @atom_symbol = ChemicalShiftSubmission.ReadAtoms(@output_csv_path_with_name) # it is a two dimentional array [[atom1,symbol1],[atom2,symbol2]]

    @custom_atom_number = CustomAtomNumber.new()
    @custom_atom_numbers = Array.new
    if CustomAtomNumber.exists?(:chemical_shift_submission_id => @chemical_shift_submission.id)
      @custom_atom_number_relations = CustomAtomNumber.where(:chemical_shift_submission_id => @chemical_shift_submission.id)
      @custom_atom_number_relations.each do |c|
        @custom_atom_numbers.push(c)
      end
      @chemical_shift_submission = ChemicalShiftSubmission.find(@chemical_shift_submission.id)
    else
      @atom_symbol.each do |atom|
        c = CustomAtomNumber.new(atom_id: atom[0], atom_symbol: atom[1])
        @custom_atom_numbers.push(c)
      end
      @chemical_shift_submission = ChemicalShiftSubmission.find(@chemical_shift_submission.id)
    end
    session[:chemical_shift_submission_id] = @chemical_shift_submission.id
    @atom_length = ChemicalShiftSubmission.calc_atom_length(session[:chemical_shift_submission_id]).to_i
  end
  def new
    @atom_length = Submission.calc_atom_length(session[:submission_id]).to_i
    # @custom_atom_number = CustomAtomNumber.new()
    @submission = Submission.find((params["format"].to_i))
    @natural_product = NaturalProduct.find(@submission.natural_product_id)
    temp_image_basename="#{@submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_temp_3D.png"
    temp_mol_basename="#{@submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_temp_3D.mol"
    @threeD_image_url = File.join("/downloads","#{@submission.user_session_id}",temp_image_basename)


    
    csv_basename="#{@submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_custom_table.csv"
    session_directory=Rails.root.join('public','downloads',"#{@submission.user_session_id}")
    @output_csv_path_with_name = File.join("#{session_directory}", csv_basename)
    @input_threeD_mol_url = Rails.root.join('public','downloads',"#{@submission.user_session_id}",temp_mol_basename)





    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path=Rails.root.join("public", "python", "npmrd_custom_atom_csv.py")

    python_log_basename="#{@submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{current_user.id}" + "_custom_table.log"
    python_log_abs_path=Rails.root.join("#{session_directory}",python_log_basename)

    script_command = ""
    script_command += "#{python_path} "
    script_command += "#{script_path} "

    script_command += "-input_mol #{@input_threeD_mol_url} "
    script_command += "-output_mol #{@output_csv_path_with_name} "

    script_command += ">> #{python_log_abs_path} "

    puts "script_command = #{script_command}"
    `#{script_command}`

   
    @atom_symbol = Submission.ReadAtoms(@output_csv_path_with_name) # it is a two dimentional array [[atom1,symbol1],[atom2,symbol2]]

    @custom_atom_number = CustomAtomNumber.new()
    @custom_atom_numbers = Array.new
    if CustomAtomNumber.exists?(:submission_id => @submission.id)
      @custom_atom_number_relations = CustomAtomNumber.where(:submission_id => @submission.id)
      @custom_atom_number_relations.each do |c|
        @custom_atom_numbers.push(c)
      end
      @chemical_shift_submission = Submission.find(@submission.id)
    else
      @atom_symbol.each do |atom|
        c = CustomAtomNumber.new(atom_id: atom[0], atom_symbol: atom[1])
        @custom_atom_numbers.push(c)
      end
      @submission = Submission.find(@submission.id)
    end
    session[:submission_id] = @submission.id
  end

  def create
    status = "pending"
    case params[:state]
    when "c_s_s"
      s = ChemicalShiftSubmission.find(session[:chemical_shift_submission_id])

      if CustomAtomNumber.exists?(:chemical_shift_submission_id => session[:chemical_shift_submission_id])
        for i in 0..(params[:custom_atom_number][:atom_symbol].length() - 1) do
          if CustomAtomNumber.where({:chemical_shift_submission_id => session[:chemical_shift_submission_id], :atom_id => params[:custom_atom_number][:atom_id][i]} ).present?
            @custom_atom_number = CustomAtomNumber.where({:chemical_shift_submission_id => session[:chemical_shift_submission_id], :atom_id => params[:custom_atom_number][:atom_id][i]}).first
            @custom_atom_number.atom_id = params[:custom_atom_number][:atom_id][i]
            @custom_atom_number.atom_symbol = params[:custom_atom_number][:atom_symbol][i]
            @custom_atom_number.custom_atom_id = params[:custom_atom_number][:custom_atom_id][i].empty? ?
                                                   @custom_atom_number.atom_id : params[:custom_atom_number][:custom_atom_id][i]
            @custom_atom_number.save!
          end
        end
      else
        for i in 0..(params[:custom_atom_number][:atom_symbol].length() - 1) do
          a_i = params[:custom_atom_number][:atom_id][i]
          a_s = params[:custom_atom_number][:atom_symbol][i]
          c_a_i =  params[:custom_atom_number][:custom_atom_id][i].empty? ? a_i : params[:custom_atom_number][:custom_atom_id][i]
          @custom_atom_number = CustomAtomNumber.create(atom_id: a_i, atom_symbol: a_s, custom_atom_id: c_a_i, chemical_shift_submission_id: session[:chemical_shift_submission_id])
        end
        CustomAtomNumber.conditional_save(s)
      end

      # From chemical_shift_sumbission controller -- needed to redirect back to chemical_shift_sumbission metadata
      @chemical_shift_submission = ChemicalShiftSubmission.find(session[:chemical_shift_submission_id])
      if !@chemical_shift_submission.chemical_shift_submission_meta_data
        @chemical_shift_submission.build_chemical_shift_submission_meta_data
      end

      @chemical_shift_submission_meta_data = @chemical_shift_submission.chemical_shift_submission_meta_data
      @page = 'chemical_shift_submissions/metadata'

    when "s_s"
      s = Submission.find(session[:submission_id])
      if CustomAtomNumber.exists?(:submission_id => session[:submission_id])
        for i in 0..(params[:custom_atom_number][:atom_symbol].length() - 1) do
          if CustomAtomNumber.where({:submission_id => session[:submission_id], :atom_id => params[:custom_atom_number][:atom_id][i]} ).present?
            @custom_atom_number = CustomAtomNumber.where({:submission_id => session[:submission_id], :atom_id => params[:custom_atom_number][:atom_id][i]}).first
            @custom_atom_number.atom_id = params[:custom_atom_number][:atom_id][i]
            @custom_atom_number.atom_symbol = params[:custom_atom_number][:atom_symbol][i]
            @custom_atom_number.custom_atom_id = params[:custom_atom_number][:custom_atom_id][i].empty? ?
                                                   @custom_atom_number.atom_id : params[:custom_atom_number][:custom_atom_id][i]
              @custom_atom_number.custom_atom_id = @custom_atom_number.atom_id
          end
        end
      else
        for i in 0..(params[:custom_atom_number][:atom_symbol].length() - 1) do
          a_i = params[:custom_atom_number][:atom_id][i]
          a_s = params[:custom_atom_number][:atom_symbol][i]
          if params[:custom_atom_number][:custom_atom_id][i].empty?
            c_a_i = a_i
          else
            c_a_i = params[:custom_atom_number][:custom_atom_id][i]
          end
          @custom_atom_number = CustomAtomNumber.create(atom_id: a_i, atom_symbol: a_s, custom_atom_id: c_a_i, submission_id: session[:submission_id])
        end
        CustomAtomNumber.conditional_save(s)
      end
      @submission = Submission.find(session[:submission_id])

      if !@submission.submission_meta_data
        @submission.build_submission_meta_data
      end

      @natural_product = NaturalProduct.find(@submission.natural_product_id)

      @submission_meta_data = @submission.submission_meta_data
      @page = 'submissions/metadata'
      
      #SUBMISSIONS CONTROLLER STUFF
  
    end

    if @custom_atom_number.save!
      respond_to do |format|
        format.js
      end
    end
    # end
  end


  def edit
  end

  def update
  end

  def custom_atom_number_params 
    params.require(:custom_atom_number).permit(:atom_id, :atom_symbol, :custom_atom_id)
  end
end
