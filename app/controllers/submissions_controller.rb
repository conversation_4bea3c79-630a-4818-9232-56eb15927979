class SubmissionsController < ApplicationController
  include Shared::SubmissionLoader
  include Shared::NaturalProductLoader
  # include SubmissionLoader
  require 'fileutils'
  require 'open-uri'

  skip_before_action :verify_authenticity_token
  before_action :user_jwt_token

  def new
    redirect_away sign_in_path if UserSession.try(:find).nil?
    @submission = Submission.new()
    @submission.build_submission_meta_data
  end

  def user_jwt_token
    @user_token = session[:user_token] if current_user
  end

  ############## create action starts #####################
  def create
    status = 'pending'
    case params[:state]
    when 'post_np'
      # Grab from params (if submissions exist for the compound, cannot save metadata until after the alert)
      @natural_product = load_create_natural_product(nil, params[:compound_name], params[:structure_input])
      @input_smiles = @natural_product.input_smiles
      session[:structure_input] = @input_smiles
      session[:genus] = params[:submission][:submission_meta_data_attributes][:genus]
      session[:species] = params[:submission][:submission_meta_data_attributes][:species]
      session[:physical_state_of_compound] = params[:submission][:submission_meta_data_attributes][:physical_state_of_compound]
      session[:melting_point] = params[:submission][:submission_meta_data_attributes][:melting_point]
      session[:boiling_point] = params[:submission][:submission_meta_data_attributes][:boiling_point]
      session[:literature_reference] = params[:submission][:submission_meta_data_attributes][:literature_reference]
      session[:literature_reference_type] = params[:submission][:submission_meta_data_attributes][:literature_reference_type]
      if session[:literature_reference_type] == 'Unpublished / Under Review'
        session[:literature_reference] = 'Unpublished / Under Review'
      end
      session[:provenance] = params[:submission][:submission_meta_data_attributes][:provenance]
      # Check to see if natural product already existed in NP-MRD
      if !@natural_product.exported?
        save_new_chemical_shift_submission
      else # Display alert
        @submissions = Submission.where(natural_product_id: @natural_product.id, valid: true)
        @chemical_shift_submissions = ChemicalShiftSubmission.where(natural_product_id: @natural_product.id, valid: true)
        @page = 'alert'
      end
    when 'post_alert'
      # User chooses to save or cancel the submission
      case params[:user_input]
      when 'Continue'
        save_new_chemical_shift_submission
      when 'Cancel'
        status = 'cancelled'
      end
    end
    if status == 'pending'
      respond_to do |format|
        format.js
      end
    else
      if status == 'cancelled'
        respond_to do |format|
          format.js { render ajax_redirect_to('/submissions?submission=cancelled') }
        end
      end
    end
  end
######################### Create action finished ######################################

##################### index action starts ####################
  def index
    if UserSession.try(:find).nil?
      redirect_away sign_in_path
    else
      load_submission_index_objects_by_user
      respond_to do |format|
         format.html
       end
   end
   end
######################### Index action finished ######################################

############## Edit action starts #####################
  def edit
    @submission = Submission.find(params[:id])

    @submission.build_submission_meta_data unless @submission.submission_meta_data

    @natural_product = NaturalProduct.find(@submission.natural_product_id)


    temp_image_basename = "#{@submission.id.to_s}_#{@natural_product.np_mrd_id}_#{current_user.id.to_s}_temp_3D.png"
    @temp_mol_basename = "#{@submission.id.to_s}_#{@natural_product.np_mrd_id}_#{current_user.id.to_s}_temp_3D.mol"
    csv_basename = "#{@submission.id.to_s}_#{@natural_product.np_mrd_id}_#{current_user.id.to_s}_mol.csv"
    @threeD_image_url = File.join('/downloads',@submission.user_session_id.to_s,temp_image_basename)
    @threeD_mol_url = File.join('/downloads',@submission.user_session_id.to_s,@temp_mol_basename)
    stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root}/public/python/rdkit_smiles_to_mol_nmr_pred.py '#{@natural_product.moldb_smiles}'")
    @read_mol = stdout.gets(nil).to_s
    @mol_csv_url = File.join('public','downloads',@submission.user_session_id.to_s,csv_basename)

    session[:threeD_image] = @threeD_image_url
    session[:mol_csv_url] = @mol_csv_url

    session[:threeD_mol_name] = @temp_mol_basename
    session[:threeD_mol_url] = @threeD_mol_url
  end
######################### Edit action finished ######################################

############## Update action starts #####################
  def update
    status = 'pending'
    case params[:state]
    when 'post_np'
      # Save meta data
      @submission = Submission.find(params[:id])
      @submission.update(params.require(:submission).permit!)
      # Store in session
      natural_product = NaturalProduct.find(@submission.natural_product_id)
      session[:structure_input] = session[:structure_input]
      session[:natural_product_id] = natural_product.id
      session[:natural_product_np_mrd_id] = natural_product.np_mrd_id
      session[:submission_id] = @submission.id
      session[:structure_input] = session[:structure_input]
      @threeD_image_url = session[:threeD_image]
      # Pass to metadata page
      @submission_meta_data = @submission.submission_meta_data
      @page = 'metadata'
    when 'post_meta'
      # Save meta data
      @submission = Submission.find(params[:id])
      @submission.update(params.require(:submission).permit!)
      @submission.save!

      if @submission.submission_meta_data.temperature.nil? or @submission.submission_meta_data.temperature.empty? or @submission.submission_meta_data.temperature == 'NA'
        @submission.submission_meta_data.update(temperature: '20')
      end
      if @submission.submission_meta_data.melting_point.empty?
        @submission.submission_meta_data.update(melting_point: 'NA')
      end
      if @submission.submission_meta_data.boiling_point.empty?
        @submission.submission_meta_data.update(boiling_point: 'NA')
      end


      # Store in session
      session[:natural_product_id] = session[:natural_product_id]
      session[:submission_id] = session[:submission_id]
      session[:threeD_image] = session[:threeD_image]
      # session[:mol_csv_url] = session[:mol_csv_url]
      session[:spectrum_type] = params[:submission][:submission_meta_data_attributes][:spectrum_type]
      session[:solvent] = params[:submission][:submission_meta_data_attributes][:solvent]
      session[:spectrometer_frequency] = params[:submission][:submission_meta_data_attributes][:spectrometer_frequency]
      session[:temperature] = params[:submission][:submission_meta_data_attributes][:temperature]
      session[:chemical_shift_standard] = params[:submission][:submission_meta_data_attributes][:chemical_shift_standard]
      session[:submission] = params[:submission]
      session[:structure_input] = session[:structure_input]


      session[:threeD_mol_name] = session[:threeD_mol_name]
      session[:threeD_mol_url] = session[:threeD_mol_url]

      # paths
      puts 'PATHS AFTER POST META'
      puts session[:natural_product_id]
      puts session[:submission_id]
      puts session[:threeD_mol_name]
      puts session[:threeD_mol_url]

      natural_product = NaturalProduct.find(@submission.natural_product_id)
      session_id = @submission.user_session_id.to_s
      submission_prefix = @submission.id.to_s + "_#{natural_product.np_mrd_id}" + "_#{@submission.user_id}"
      temp_mol_name = "#{submission_prefix.to_s}_temp_3D.mol"
      session_directory = Rails.root.join('public','downloads',session_id.to_s)
      path_to_model = Rails.root.join('public','downloads',session_id.to_s,temp_mol_name.to_s)

      # if mol.csv exists do not redo prediction, just get the file...
      if not File.exist?(Rails.root.join('public','downloads',session_id.to_s,"#{submission_prefix}_mol.csv"))

        puts 'PATHS FOR NMRPRED'
        puts session_id
        puts submission_prefix
        puts temp_mol_name
        puts session_directory
        puts path_to_model

          # uses 3d mol to make intial nmrshiftdb predictions, returns full path
        nmrpred_assignment = run_nmrpred_intialprediction(path_to_model.to_s, session_directory, submission_prefix)

          # takes nmrpred output assignmenttable.txt, makes mol.csv, for website table, returns file name
        csv_basename = run_npmrd_make_mol_csv(path_to_model.to_s, nmrpred_assignment, session_directory, submission_prefix)

        @mol_csv_url = File.join('public','downloads',session_id.to_s,csv_basename)
      else
        puts 'MOLCSV ALREADY GENERATED, SKIPPING PREDICTION'
        @mol_csv_url = File.join('public','downloads',session_id.to_s,"#{submission_prefix}_mol.csv")
      end
      session[:mol_csv_url] = @mol_csv_url
      @chsqc_table_url = Rails.root.join('public','downloads',session_id.to_s,"#{submission_prefix}_chsqc_assignmenttable.txt")
      session[:chsqc_table_url] = @chsqc_table_url

      @custom_atom_numbers = get_custom_atom_numbers @submission

      @threeD_image_url = session[:threeD_image]
      @page = 'chemical_shift_new'
      ##### take the atoms and symbol for the submitted molecule
      preparation_for_building_shift_table(session[:mol_csv_url], session[:chsqc_table_url], session[:submission_id],
                                           session[:spectrum_type])

      # make atom length available for custom numbering restrictions
      # @atom_length = Submission.calc_atom_length(session[:submission_id]).to_i
    when 'post_chemical_shift'
      session[:structure_input] = session[:structure_input]
      session[:submission_id] = session[:submission_id]
      session[:natural_product_id] = session[:natural_product_id]
      session[:threeD_image] = session[:threeD_image]
      session[:mol_csv_url] = session[:mol_csv_url]
      session[:spectrum_type] = session[:spectrum_type]
      session[:threeD_mol_name] = session[:threeD_mol_name]
      session[:threeD_mol_url] = session[:threeD_mol_url]
      shift_hash = params[:submission]['chemical_shifts_attributes']
      s = Submission.find(session[:submission_id])
      if ChemicalShift.exists?(submission_id: session[:submission_id])
        shift_hash.each do |key, values|
          if ChemicalShift.where({submission_id: session[:submission_id], atom_id: values['atom_id']} ).present?
            cs = ChemicalShift.where({submission_id: session[:submission_id], atom_id: values['atom_id']} ).first
            values.each do |column, value|
              if column != 'id'
                cs[column] = value
                cs.save!
              end
            end
            s = Submission.find(session[:submission_id])
            s.valid = false
            s.save!
          end
        end

      else
        shift_hash.each do |key, values|
          cs = ChemicalShift.new()
          values.each do |column, value|
            cs[column] = value
          end
          s.chemical_shifts << cs
          s.save!
          s = Submission.find(session[:submission_id])
          s.valid = false
          s.save!
        end
        ChemicalShift.conditional_save(s)
      end
      @natural_product = NaturalProduct.find(s.natural_product_id)
      @meta_data = s.submission_meta_data
      @submission = Submission.find(session[:submission_id])
      @chemical_shifts = s.chemical_shifts
      calc_assignment_score


      # puts "@chemical shift ............. #{@chemical_shifts}"
      # puts "s.chemical_shifts .....#{s.chemical_shifts.inspect()}"
      s.chemical_shifts << @chemical_shifts
      s.save!




      @page = 'spectrum_file_upload'
      session[:submission_id] = session[:submission_id]
      session[:natural_product_id] = session[:natural_product_id]
      session[:threeD_image] = session[:threeD_image]
      session[:mol_csv_url] = session[:mol_csv_url]
      session[:spectrum_type] = session[:spectrum_type]
      session[:spectrometer_frequency] = session[:spectrometer_frequency]
      session[:nmr_spectrum_type] = session[:spectrum_type]
      session[:nmr_solvent] = @meta_data.solvent
      session[:nmr_chemical_shift_standard] = @meta_data.chemical_shift_standard
      unless @submission.nmr_submissions.exists?
        @submission.nmr_submissions.create(submission_id: session[:submission_id], spectrometer_frequency: session[:spectrometer_frequency],nmr_spectrum_type: session[:nmr_spectrum_type], chemical_shift_standard: session[:nmr_chemical_shift_standard], solvent: session[:nmr_solvent])
      end
    when 'post_spectral_file'
      session[:submission_id] = session[:submission_id]
      session[:natural_product_id] = session[:natural_product_id]
      session[:threeD_image] = session[:threeD_image]
      session[:mol_csv_url] = session[:mol_csv_url]
      session[:spectrum_type] = session[:spectrum_type] # this is in submission_meta_data table
      session[:renumbered_mol] = session[:renumbered_mol]
      @submission = Submission.find(session[:submission_id])
      @submission.update(params.require(:submission).permit!)

      @natural_product = NaturalProduct.find(@submission.natural_product_id)
      preparation_for_building_shift_table(session[:mol_csv_url], session[:chsqc_table_url], session[:submission_id],
                                           session[:spectrum_type])
      @meta_data = @submission.submission_meta_data
      preparation_for_building_shift_table(session[:mol_csv_url], session[:chsqc_table_url], session[:submission_id],
                                           session[:spectrum_type])

      @new_renumbered_mol = session[:renumbered_mol]

      session_name = session[:session_name]
      score_image_name = "#{session_name}_assignment_score.svg"
      @score_image_path = File.join('/downloads',session.id.to_s,score_image_name)


      @custom_atom_numbers = Array.new
      if CustomAtomNumber.exists?(submission_id: @submission.id)
        @custom_atom_number_relations = CustomAtomNumber.where(submission_id: @submission.id)
        @custom_atom_number_relations.each do |c|
          @custom_atom_numbers.push(c)
        end
      else
        @atom_symbol.each do |atom|
          c = CustomAtomNumber.new(atom_id: atom[0], atom_symbol: atom[1])
          @custom_atom_numbers.push(c)
        end
        @submission = Submission.find(@submission.id)
      end

      @spectrum_type = session[:spectrum_type]

 ############################
      @page = 'verification'
    when 'post_verification'
      @submission = Submission.find(params[:id])
      # User chooses whether or not to verify the submission
      case params[:user_input]
      when 'Continue'
        @submission.update(params.require(:submission).permit!)
        @submission.valid = true # Finished submission mark as valid
        @submission.save!
        if @submission.submission_meta_data.physical_state_of_compound.nil? or @submission.submission_meta_data.physical_state_of_compound.empty?
          @submission.submission_meta_data.update(physical_state_of_compound: 'NA')
        end
        if @submission.submission_meta_data.literature_reference_type == 'Unpublished / Under Review'
          @submission.submission_meta_data.update(literature_reference: 'Unpublished / Under Review')
        end
        # Export natural product
        natural_product = @submission.natural_product
        natural_product.export = true
        natural_product.save!

        # save or create the reference. for now only PMID
        Submission.Generate_Reference_Pubmed(params[:id])

        status = 'verified'
      when 'Cancel'
        status = 'cancelled'
      end
    end
    if status == 'pending'
      respond_to do |format|
        format.js
      end
    else # if something != something then execute the codes
      case status
      when 'verified'
        # Notifier.completed_deposition(current_user.email, natural_product.np_mrd_id, {'ValidationReport.zip': 'public/validation_report.zip'}).deliver_now
        redirect_to action: :show, notice: 'Submission saved successfully!'
      when 'cancelled'
        redirect_to action: :show, notice: 'Verification still required!'
      end
    end
  end

######################### Update action finished ######################################
############## Show action starts #####################
  def show
    session_name = session[:session_name]

    if UserSession.try(:find).nil?
      redirect_away sign_in_path
    else
      load_submission_object_by_user
      runNMRpred
      process_data

      nmrml_name = "#{session_name}.nmrml"
      # @nmrml_path = File.join("/downloads","#{session[:session_id]}",nmrml_name)
      @source = 'experimental'
      @nmrml_path = File.join('/downloads',session.id.to_s,nmrml_name)
      @spectrum_type = session[:spectrum_type]
      @file_list = session[:file_list]
      @name_list = session[:name_list]
      @quality_list = session[:sp_quality_img_list]
      @param_list = session[:param_list]
      @nmrml_list = session[:nmrml_list]
      @jcamp_list = session[:jcamp_list]

      score_image_name = "#{session_name}_assignment_score.svg"
      @score_image_path = File.join('/downloads',session.id.to_s,score_image_name)




      @chemical_shifts = @submission.chemical_shifts
      calc_assignment_score
      @submission.chemical_shifts = @chemical_shifts
      @submission.save!
      assignment_report_file_name = "#{session_name}_assignment_report.txt"
      @assignment_report_file_path = File.join('public/downloads',session.id.to_s,assignment_report_file_name)
      Submission.save_assignment_report(@submission,file_path = @assignment_report_file_path)
      @custom_atom_numbers = get_custom_atom_numbers @submission

      respond_to do |format|
        format.html
      end
    end
  end
######################## Show action finished ######################################

############## Custom "Download" action starts #####################
  def download_submitted_chemical_shift_data
    load_submission_object_without_user_session
    file_name = Submission.Download_file(@submission)
    send_file Rails.root.join('public','downloads',file_name).to_s.to_s, type: 'application/csv', filename: file_name.to_s, disposition: 'attachment'
  end

######################## Custom "Download" action finished ######################################
  def download_renumbered_mol
    load_submission_object_without_user_session
    temp_mol_basename_out = "#{@submission.id.to_s}_#{@natural_product.np_mrd_id}_#{@submission.user_id.to_s}_temp_3D_out.mol"
    # threeD_mol_url_out = Rails.root.join("public","downloads","#{@chemical_shift_submission.user_session_id}","#{temp_mol_basename_out}")
    send_file Rails.root.join('public','downloads',@submission.user_session_id,temp_mol_basename_out).to_s.to_s, type: 'application/mol', filename: temp_mol_basename_out.to_s, disposition: 'attachment'
  end

############## Method used by the create action starts #####################
  def save_new_chemical_shift_submission
    # After alert
    if params[:natural_product_id]
      @natural_product = NaturalProduct.find(params[:natural_product_id])
      @input_smiles = @natural_product.input_smiles
      @submission = Submission.new(chemical_shift_submission_from_session)
    else # No alert
      @submission = Submission.new(params.require(:submission).permit!)
    end

    # Save chemical shift submission and metadata
    @submission.user_id = current_user.id
    # puts"before submission save, the session id = #{session[:session_id]}"
    puts "before submission save, the session id = #{session.id}"
    # @submission.user_session_id = session[:session_id]
    @submission.user_session_id = session.id
    @submission.natural_product_id = @natural_product.id
    @submission.save!
    puts @submission.inspect
    session[:submission_id] = @submission.id


    # Store in session
    session[:submission_id] = @submission.id
    session[:natural_product_id] = @natural_product.id
    session[:natural_product_name] = @natural_product.name
    session[:natural_product_np_mrd_id] = @natural_product.np_mrd_id
    session[:user_id] = @submission.user_id
    session[:input_smiles_key] = session[:structure_input]

    # puts "@input_smiles ....................#{@input_smiles}"
    puts "INPUT SMILES #{session[:structure_input]}"
    smiles = session[:input_smiles_key]
    session_name = session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + (session[:user_id]).to_s
    session[:session_name] = session_name

    # session_id=session[:session_id]
    session_id = session.id
    # puts "session_id= #{session[:session_id]}"
    puts "session_id= #{session.id}"

    session_directory = Rails.root.join('public','downloads',session_id.to_s)
    puts "Creating session_directory = #{session_directory}"
    Dir.mkdir session_directory unless File.exists?(session_directory)



    puts 'SMILES INPUT DEBUGGING2'
    puts @natural_product.smiles
    puts @input_smiles
    puts session[:input_smiles_key]

    #
    # This takes the submitted smiles string and generates 3d mol files and images
    #
    smiles = session[:input_smiles_key]
    non_canonical_mol_abs_path, non_canonical_base_image_abs_path, non_canonical_equiv_image_abs_path = runDrawMol(smiles, session_directory, session_name)
    puts 'drawmol paths:'
    puts non_canonical_mol_abs_path, non_canonical_base_image_abs_path, non_canonical_equiv_image_abs_path
    #
    # makes copies of mol and image file for the website use
    # returns file names WITHOUT full path
    #
    temp_model_basename, temp_image_basename = make_files_from_drawmol(non_canonical_mol_abs_path, non_canonical_base_image_abs_path, session_directory, session_name)
    path_to_temp_model = Rails.root.join(session_directory.to_s,temp_model_basename.to_s)
    path_to_temp_image = Rails.root.join(session_directory.to_s,temp_image_basename.to_s)
    puts 'temp files paths:'
    puts temp_model_basename, temp_image_basename, path_to_temp_model, path_to_temp_image


    # new_file_name = session[:natural_product_np_mrd_id].to_s+"_image3D.png"
    # new_file_path = Rails.root.join('public', 'structures',new_file_name).to_s
    # File.rename(non_canonical_base_image_abs_path,new_file_path)
    # @threeD_image_url  = "/structures/"+new_file_name
    @threeD_image_url = File.join('/downloads',session_id.to_s,temp_image_basename)
    session[:threeD_image] = @threeD_image_url

    session[:temp_model_basename] = temp_model_basename
    session[:path_to_temp_model] = path_to_temp_model

    puts session[:temp_model_basename]
    puts session[:path_to_temp_model]



    @threeD_image_url = File.join('/downloads',session_id.to_s,temp_image_basename)
    session[:threeD_image] = @threeD_image_url

    session[:temp_model_basename] = temp_model_basename
    session[:path_to_temp_model] = path_to_temp_model

    puts session[:temp_model_basename]
    puts session[:path_to_temp_model]

    @page = 'metadata'
    @submission_meta_data = @submission.submission_meta_data
  end

  # form_for returns js requests which doesn't allow the html redirect
  def ajax_redirect_to(redirect_uri)
    { js: "window.location.replace('#{redirect_uri}');" }
  end

  ###### used by "save_new_chemical_shift_submission" starts #########
  def runDrawMol(smiles, session_directory, session_name)

    #
    # This takes the submitted smiles string and generates 3d mol files and images
    #

    puts 'Running runDrawMol() function'
    puts "smiles = #{smiles}"
    puts "session_directory = #{session_directory}"

    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    draw_mol_script = Rails.root.join('backend', 'nmr-pred','draw_mol.py')
    puts "draw_mol_script = #{draw_mol_script}"


    draw_mol_log_basename = "#{session_name}_draw_mol.log"
    outputprefix = "#{session_name}_draw_mol"
    draw_mol_log_abs_path = Rails.root.join(session_directory.to_s,draw_mol_log_basename)

    puts "draw_mol_log_abs_path = #{draw_mol_log_abs_path}"

    draw_mol_command = ''
    draw_mol_command += "#{python_path} "
    draw_mol_command += "#{draw_mol_script} "

    draw_mol_arguments = ''
    draw_mol_arguments += "--smiles '#{smiles}' "
    draw_mol_arguments += "--outputpath '#{session_directory}' "
    draw_mol_arguments += '--writemol '
    draw_mol_arguments += '--optmol '
    # draw_mol_arguments+="--showstereo "
    # draw_mol_arguments+="--showequiv "
    draw_mol_arguments += '--smilesorder '

    draw_mol_arguments_non_canonical = draw_mol_arguments
    draw_mol_arguments_non_canonical += "--outputprefix '#{outputprefix}' "
    draw_mol_arguments_non_canonical += " > '#{draw_mol_log_abs_path}' "

    draw_mol_command_non_canonical = draw_mol_command
    draw_mol_command_non_canonical += "#{draw_mol_arguments_non_canonical} "
    puts "draw_mol_command_non_canonical: #{draw_mol_command_non_canonical}"
    `#{draw_mol_command_non_canonical}`

    non_canonical_mol_abs_path = Rails.root.join(session_directory.to_s,"#{outputprefix}_output.mol")
    non_canonical_base_image_abs_path = Rails.root.join(session_directory.to_s,"#{outputprefix}_2d.png")
    non_canonical_equiv_image_abs_path = Rails.root.join(session_directory.to_s,"#{outputprefix}_equiv.png")

    puts "non_canonical_mol_abs_path: #{non_canonical_mol_abs_path}"
    puts "non_canonical_base_image_abs_path: #{non_canonical_base_image_abs_path}"
    puts "non_canonical_equiv_image_abs_path: #{non_canonical_equiv_image_abs_path}"

    return non_canonical_mol_abs_path, non_canonical_base_image_abs_path, non_canonical_equiv_image_abs_path
  end
  ####### user by "save_new_chemical_shift_submission" ends"



  def run_nmrpred_intialprediction(input_mol_path, session_directory, session_name)

    #
    # This uses a 3d mol to make intial nmrshiftdb prediction with nmrpred
    # Returns full paths
    #

    puts 'running nmrpred (nmrshiftdb) for intial prediction)'
    puts "input mol file: #{input_mol_path}"
    puts "output folder: #{session_directory}"
    puts "output prefix: #{session_name}"

    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    nmrpred_script = Rails.root.join('backend', 'nmr-pred', 'nmrpred.py')
    puts "nmrpred_script = #{nmrpred_script}"

    python_log_basename = "#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path = Rails.root.join(session_directory.to_s,python_log_basename)

    nmrpred_command = "#{python_path} "
    nmrpred_command += "#{nmrpred_script} "

    nmrpred_command += "--mol #{input_mol_path} "
    nmrpred_command += "--outputpath #{session_directory} "
    nmrpred_command += "--outputprefix #{session_name} "
    nmrpred_command += '--writeassignmenttable '
    nmrpred_command += '--write1h  '
    nmrpred_command += '--write1hcoup '
    nmrpred_command += '--write13c '
    nmrpred_command += '--writechsqctable'
    nmrpred_command += ">>  #{python_log_abs_path} "

    puts "nmrpred_command = #{nmrpred_command}"
    `#{nmrpred_command}`

    nmrpred_assignment = Rails.root.join(session_directory.to_s,"#{session_name}_assignmenttable.txt")
    puts "nmrpred_assignment = #{nmrpred_assignment}"

    # cs_dictionary=parse_nmrpred_assignment(nmrpred_assignment)
    return nmrpred_assignment

  end

  def make_files_from_drawmol(mol_file, image_file, session_directory, session_name)

    #
    # This takes nmrpred output assignmenttable.txt and makes mol.csv which is parsed into the website table
    # Appends to log file from run_nmrpred_intialprediction above
    # Also makes copies of mol and image file for the website use
    # returns file names WITHOUT full path
    #

    puts "copying files to 'temp' image and mol file"

    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    script_path = Rails.root.join('public', 'python', 'npmrd_copy_drawmol_for_site.py')
    puts "script = #{script_path}"

    temp_model_basename = "#{session_name}_temp_3D.mol"
    temp_image_basename = "#{session_name}_temp_3D.png"

    output_mol_path = Rails.root.join(session_directory.to_s, temp_model_basename)
    output_img_path = Rails.root.join(session_directory.to_s, temp_image_basename)

    python_log_basename = "#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path = Rails.root.join(session_directory.to_s,python_log_basename)

    script_command = ''
    script_command += "#{python_path} "
    script_command += "#{script_path} "

    script_command += "-sid #{session_name} "
    script_command += "-odir_path #{session_directory} "

    script_command += "-input_mol #{mol_file} "
    script_command += "-input_img #{image_file} "

    script_command += "-mol_path #{output_mol_path} "
    script_command += "-mol_img_path #{output_img_path} "

    script_command += ">> #{python_log_abs_path} "

    puts "script_command = #{script_command}"
    `#{script_command}`

    return temp_model_basename, temp_image_basename
  end

  def run_npmrd_make_mol_csv(mol_file, table_file, session_directory, session_name)

    #
    # This takes nmrpred output assignmenttable.txt and makes mol.csv which is parsed into the website table
    # Appends to log file from run_nmrpred_intialprediction above
    # returns file names WITHOUT full path
    #

    puts 'converting nmrpred file to mol.csv file'

    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    script_path = Rails.root.join('public', 'python', 'npmrd_make_mol_csv.py')
    puts "script = #{script_path}"

    csv_basename = "#{session_name}_mol.csv"
    output_csv_path = Rails.root.join(session_directory.to_s, csv_basename)

    python_log_basename = "#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path = Rails.root.join(session_directory.to_s,python_log_basename)

    script_command = ''
    script_command += "#{python_path} "
    script_command += "#{script_path} "

    script_command += "-sid #{session_name} "
    script_command += "-odir_path #{session_directory} "

    script_command += "-input_mol #{mol_file} "
    script_command += "-inputtable_path #{table_file} "

    script_command += "-cs_path #{output_csv_path} "

    script_command += ">> #{python_log_abs_path} "

    puts "script_command = #{script_command}"
    `#{script_command}`

    return csv_basename
  end


############## Method used by the create action finished #####################
###########################################################



  def runNMRpred()
    # output from nmrpred --writeassignmenttable will generally have
    # extra atoms especially if there are unassigned couplings

    # default options and files:
    # --pred1hcsv         'results/example_1h_shifts.txt'
    # --pred1hcoupcsv     'results/example_1h_couplings.txt'
    # --userinput         'results/example_assignmenttable_manual.txt'
    # --write1hshift      'results/example_parsed1hshifts.txt'
    # --write1hcoup       'results/example_parsed1hcoup.txt'

    # for simulation, after running script:
    # nmrpred.py --smiles CCO --noprediction --input1h results/example_parsed1hshifts.txt --input1hcoup results/example_parsed1hcoup.txt --plot1h

    puts 'Running function runNMRpred()'
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    backend_dir = 'backend'
    nmr_pred_dir = 'nmr-pred'
    parseuserassignment_script_basename = 'parseuserassignment.py'
    nmrpred_script_basename = 'nmrpred.py'

    # smiles=NaturalProduct.find_by(np_mrd_id: session[:natural_product_np_mrd_id]).moldb_smiles


    session_name = session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + @current_user.id.to_s
    # session_id=session[:session_id]
    session_id = session.id
    spectrum_type = session[:spectrum_type]
    parseuserassignment_script = Rails.root.join(backend_dir.to_s, nmr_pred_dir.to_s,parseuserassignment_script_basename.to_s)
    nmrpred_script = Rails.root.join(backend_dir.to_s, nmr_pred_dir.to_s,nmrpred_script_basename.to_s)
    nmrpred_dir = Rails.root.join(backend_dir.to_s, nmr_pred_dir.to_s)


    session_directory = Rails.root.join('public','downloads',session_id.to_s)

    inputmol_basename = predicted_shifts_basename = "#{session_name}_output.mol"
    inputmol_path = Rails.root.join(session_directory.to_s,inputmol_basename.to_s)

    predicted_shifts_basename = "#{session_name}_1h_shifts.txt"
    predicted_13c_shifts_basename = "#{session_name}_13c_shifts.txt"
    predicted_couplings_basename = "#{session_name}_1h_couplings.txt"
    assignmenttable_manual_basename = "#{session_name}_assignmenttable.txt"
    parsed_shifts_basename = "#{session_name}_parsed_1h_shifts.txt"
    parsed_13c_shifts_basename = "#{session_name}_parsed_13c_shifts.txt"
    parsed_couplings_basename = "#{session_name}_parsed_1h_couplings.txt"
    parseuserassignment_log_basename = "#{session_name}_parseuserassignment.log"
    user_assignmenttable_basename = "#{session_name}_user_assignmenttable.txt"
    nmrpred_log_basename = "#{session_name}_nmrepred.log"

    predicted_shifts_path = Rails.root.join(session_directory.to_s,predicted_shifts_basename.to_s)
    predicted_13c_shifts_path = Rails.root.join(session_directory.to_s,predicted_13c_shifts_basename.to_s)
    predicted_couplings_path = Rails.root.join(session_directory.to_s,predicted_couplings_basename.to_s)
    assignmenttable_manual_path = Rails.root.join(session_directory.to_s,assignmenttable_manual_basename.to_s)
    parsed_shifts_path = Rails.root.join(session_directory.to_s,parsed_shifts_basename.to_s)
    parsed_13c_shifts_path = Rails.root.join(session_directory.to_s,parsed_13c_shifts_basename.to_s)
    parsed_couplings_path = Rails.root.join(session_directory.to_s,parsed_couplings_basename.to_s)
    parseuserassignment_log_path = Rails.root.join(session_directory.to_s,parseuserassignment_log_basename.to_s)
    user_assignmenttable_path = Rails.root.join(session_directory.to_s,user_assignmenttable_basename.to_s)
    nmrpred_log_path = Rails.root.join(session_directory.to_s,nmrpred_log_basename.to_s)

    puts "Creating session_directory = #{session_directory}"
    puts "parseuserassignment_script = #{parseuserassignment_script}"
    puts 'session_directory = ',session_directory
    puts 'backend_dir= ',backend_dir
    puts 'nmr_pred_dir = ',nmr_pred_dir
    puts 'parseuserassignment_script_basename = ',parseuserassignment_script_basename
    puts 'predicted_shifts_path = ',predicted_shifts_path
    puts 'predicted_13c_shifts_path = ',predicted_13c_shifts_path
    puts 'predicted_couplings_path = ',predicted_couplings_path
    puts 'assignmenttable_manual_path = ',assignmenttable_manual_path
    puts 'parsed_shifts_path = ',parsed_shifts_path
    puts 'parsed_13c_shifts_path = ',parsed_13c_shifts_path
    puts 'parsed_couplings_path = ',parsed_couplings_path
    puts 'parseuserassignment_log_path = ',parseuserassignment_log_path
    puts 'user_assignmenttable_path = ',user_assignmenttable_path
    puts 'nmrpred_script = ',nmrpred_script
    # puts "input_smiles =  #{smiles}"
    puts "inputmol_path =  #{inputmol_path}"
    puts "spectrum_type =  #{spectrum_type}"


    user_input_command = ''
    user_input_command += "#{python_path} "
    user_input_command += "#{parseuserassignment_script} "

    user_input_arguments = ''
    user_input_arguments += '--userinput %s ' % user_assignmenttable_path
    user_input_arguments += '--predtable %s ' % assignmenttable_manual_path

    user_input_arguments += '--pred1hcsv %s ' % predicted_shifts_path
    user_input_arguments += '--pred1hcoupcsv %s ' % predicted_couplings_path
    user_input_arguments += '--write1hshift %s ' % parsed_shifts_path
    user_input_arguments += '--write1hcoup %s ' % parsed_couplings_path

    if spectrum_type.include? '13C'
      user_input_arguments += '--pred13ccsv %s ' % predicted_13c_shifts_path
      user_input_arguments += '--write13cshift %s ' % parsed_13c_shifts_path
    end

    user_input_arguments += ' > %s ' % parseuserassignment_log_path

    user_input_command += user_input_arguments

    Submission.save_user_table(@submission,file_path = user_assignmenttable_path)

    puts "user_input_command : #{user_input_command}"
    `#{user_input_command}`

    # nmrpred_prefix="nmrpred"
    nmrpred_prefix = session_name.to_s

    nmrpred_command = ''
    nmrpred_command += "#{python_path} "
    nmrpred_command += "#{nmrpred_script} "
    nmrpred_arguments = ''
    # nmrpred_arguments+=" --smiles '#{smiles}' "
    nmrpred_arguments += ' --mol %s ' % inputmol_path
    nmrpred_arguments += ' --noprediction '

    spectrometer_frequency = session[:spectrometer_frequency]
    nmrpred_arguments += " --sfrq #{spectrometer_frequency}"

    nmrpred_arguments += ' --input1h %s ' % parsed_shifts_path
    nmrpred_arguments += ' --input1hcoup %s ' % parsed_couplings_path
    nmrpred_arguments += " --inputassignfile %s "  % user_assignmenttable_path
    nmrpred_arguments += ' --plot1h '

    if spectrum_type.include? '13C'
      nmrpred_arguments += ' --input13c %s ' % parsed_13c_shifts_path
      nmrpred_arguments += ' --plot13c '
    end

    case spectrum_type
    when '2D-1H-1H-COSY'
      nmrpred_arguments += ' --writecosytable --bmrbformattable '
    when '2D-1H-1H-TOCSY', '2D-1H-1H-ROESY'
      nmrpred_arguments += ' --writehh2dtable --bmrbformattable '
    when '2D-1H-13C-HSQC'
      nmrpred_arguments += ' --writechsqctable --bmrbformattable '
    when '2D-1H-13C-HMBC'
      nmrpred_arguments += ' --writechmbctable --bmrbformattable '
    end

    nmrpred_arguments += ' --outputpath %s ' % session_directory
    nmrpred_arguments += ' --outputprefix %s ' % nmrpred_prefix
    nmrpred_arguments += ' > %s ' % nmrpred_log_path

    working_directory = Dir.pwd
    puts "working_directory : #{working_directory}"
    Dir.chdir(nmrpred_dir)
    puts "Changed to NMRpred directory: #{nmrpred_dir}"

    nmrpred_command += nmrpred_arguments

    puts "nmrpred_ command : #{nmrpred_command}"
    `#{nmrpred_command}`

    puts "Changed to back to working directory: #{working_directory}"
    Dir.chdir(working_directory)


  end

  def generate_nmrml(magmet_json: false, suffix: false, two_d: false, fid_path: false)
    puts 'Running function generate_nmrml()'
    unless two_d
      puts 'magmet_json file is %s' % magmet_json
      puts 'suffix is %s' % suffix
    end
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    backend_dir = 'backend'
    nmr_pred_dir = 'nmr_ml'
    nmrml_creator_script_basename = 'nmrml_creator.py'
    if suffix == false
      session_name = session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + (session[:user_id]).to_s
    else
      session_name = session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{session[:user_id]}_" + suffix.to_s
    end
    # session_id=session[:session_id]
    session_id = session.id
    nmrml_creator_script = Rails.root.join(backend_dir.to_s, nmr_pred_dir.to_s,nmrml_creator_script_basename.to_s)
    session_directory = Rails.root.join('public','downloads',session_id.to_s)
    natural_product_name = session[:natural_product_name]
    # New arguments, August 12th 2020:
    genus = session[:genus]
    species = session[:species]
    literature_reference = session[:literature_reference]
    solvent = session[:solvent]
    spectrum_type = session[:spectrum_type]
    spectrometer_frequency = session[:spectrometer_frequency]
    temperature = session[:temperature]
    chemical_shift_standard = session[:chemical_shift_standard].gsub(' ', '_')
    literature_reference_type = session[:literature_reference_type]
    physical_state_of_compound = session[:physical_state_of_compound]
    melting_point = session[:melting_point]
    boiling_point = session[:boiling_point]

    puts "genus=  #{genus}"
    puts "natural_product_name=  #{natural_product_name} "
    puts "species=  #{species} "
    puts "literature_reference= #{literature_reference} "
    puts "solvent= #{solvent} "
    puts "session_id = #{session_id}"
    puts "spectrum_type = #{spectrum_type}"
    puts "spectrometer_frequency = #{spectrometer_frequency}"
    puts "temperature = #{temperature}"
    puts "chemical_shift_standard = #{chemical_shift_standard}"
    puts "literature_reference_type = #{literature_reference_type}"
    puts "physical_state_of_compound = #{physical_state_of_compound}"
    puts "melting_point = #{melting_point}"
    puts "boiling_point = #{boiling_point}"


    proton_dimension = false
    carbon_dimension = false

    case spectrum_type
    when '1D-1H', '1D-1H-DEPT90', '2D-1H-1H-COSY', '2D-1H-1H-TOCSY', '2D-1H-1H-ROESY'
      proton_dimension = true

    when '1D-13C', '1D-13C-DEPT90', '2D-13C-13C-COSY', '2D-13C-13C-INADEQUATE'
      carbon_dimension = true
    when '2D-1H-13C-HSQC'
      spectrum_type = '2D-1H-13C'
    end


    nmrpred_1h_peaklist_abs_path = Rails.root.join(session_directory.to_s,"#{session_name}_1h_peaklist.txt")
    nmrpred_output_mol_abs_path = Rails.root.join(session_directory.to_s,"#{session_name}_output.mol")
    nmrml_creator_log_abs_path = Rails.root.join(session_directory.to_s,"#{session_name}_creator.log")
    # New NMRpred inputs August 12th 2020
    nmrpred_param_abs_path = Rails.root.join(session_directory.to_s,"#{session_name}_1h_params.txt")
    nmrpred_fid_abs_path = Rails.root.join(session_directory.to_s,"#{session_name}_1h_fid.txt")
    nmrpred_spectrum_abs_path = Rails.root.join(session_directory.to_s,"#{session_name}_1h_spectrum.txt")
    nmrpred_13c_peaklist_abs_path = Rails.root.join(session_directory.to_s,"#{session_name}_13c_peaklist.txt")
    nmrpred_13c_param_abs_path = Rails.root.join(session_directory.to_s,"#{session_name}_13c_params.txt")
    nmrpred_13c_fid_abs_path = Rails.root.join(session_directory.to_s,"#{session_name}_13c_fid.txt")
    nmrpred_13c_spectrum_abs_path = Rails.root.join(session_directory.to_s,"#{session_name}_c13_spectrum.txt")

    nmrml_basename = "#{session_name}.nmrML"
    nmrml_url = "public/downloads/#{session_id}/#{session_name}.nmrML"
    nmrml_abs_path = Rails.root.join(session_directory.to_s,nmrml_basename.to_s)

    session[:nmrml_basename] = nmrml_basename
    session[:nmrml_url] = nmrml_url
    session[:nmrml_abs_path] = nmrml_abs_path

    puts "nmrml_creator_script = #{nmrml_creator_script}"
    puts "session_directory = #{session_directory}"
    puts "natural_product_name = #{natural_product_name}"
    puts "nmrml_basename = #{nmrml_basename}"
    puts "nmrml_abs_path = #{nmrml_abs_path}"
    puts "nmrml_url = #{nmrml_url}"

    nmrml_command = ''
    nmrml_command += "#{python_path} "
    nmrml_command += "#{nmrml_creator_script} "
    nmrml_arguments = ''

    if two_d
      csv_path = Rails.root.join(session_directory.to_s, "#{session_name}_2d_assignment_table.csv")

      nmrml_arguments += " -mol #{nmrpred_output_mol_abs_path} "
      nmrml_arguments += " -output_path #{nmrml_abs_path} "
      nmrml_arguments += ' -name "%s" ' % natural_product_name

      nmrml_arguments += " -genus '#{genus}' "
      nmrml_arguments += " -solvent '#{solvent}' "
      nmrml_arguments += " -species '#{species}' "
      nmrml_arguments += " -freq #{spectrometer_frequency} "
      nmrml_arguments += " -ref '#{literature_reference}' "
      nmrml_arguments += " -standard '#{chemical_shift_standard}' "
      nmrml_arguments += " -temp #{temperature} "
      nmrml_arguments += " -spec_type #{spectrum_type}"
      nmrml_arguments += " -param_path #{nmrpred_param_abs_path} "

      nmrml_arguments += " -ref_type '#{literature_reference_type}' "
      nmrml_arguments += " -phys_state '#{physical_state_of_compound}' "
      nmrml_arguments += " -melt_point '#{melting_point}' "
      nmrml_arguments += " -boil_point '#{boiling_point}' "

      #if spectrum_type.include? 'HSQC'
      #  nmrml_arguments += " -1H_13C_HSQC_assign #{csv_path}"
      #elsif spectrum_type.include? 'TOCSY'
      #  nmrml_command += " --1H_13C_HSQC_bmrb_assign #{csv_path}"
      #end

      nmrml_arguments += " -2d_bmrb_assign #{csv_path} "

      magmet_json_abs_path = 'public%s' % magmet_json
      puts "magmet_json_abs_path = #{magmet_json_abs_path}"

      # nmrml_arguments += " -bruker_2d_zip #{fid_path}"
      nmrml_arguments += " -json_2d_path #{magmet_json_abs_path}"

    else


      nmrml_arguments += " -mol #{nmrpred_output_mol_abs_path} "
      nmrml_arguments += " -pl #{nmrpred_1h_peaklist_abs_path} "
      nmrml_arguments += " -output_path #{nmrml_abs_path} "
      nmrml_arguments += ' -name "%s" ' % natural_product_name
      # New arguments, August 12th 2020
      nmrml_arguments += " -genus '#{genus}' "
      nmrml_arguments += " -solvent '#{solvent}' "
      nmrml_arguments += " -species '#{species}' "
      nmrml_arguments += " -freq #{spectrometer_frequency} "
      nmrml_arguments += " -ref '#{literature_reference}' "
      nmrml_arguments += " -standard '#{chemical_shift_standard}' "
      nmrml_arguments += " -temp #{temperature} "
      nmrml_arguments += " -spec_type '#{spectrum_type}' "
      nmrml_arguments += " -param_path #{nmrpred_param_abs_path} "
      nmrml_arguments += " -fid_path #{nmrpred_fid_abs_path} "
      nmrml_arguments += " -spec_path #{nmrpred_spectrum_abs_path} "

      nmrml_arguments += " -13C_pl #{nmrpred_13c_peaklist_abs_path} "
      nmrml_arguments += " -13C_param_path #{nmrpred_13c_param_abs_path} "
      nmrml_arguments += " -13C_fid_path #{nmrpred_13c_fid_abs_path} "
      nmrml_arguments += " -13C_spec_path #{nmrpred_13c_spectrum_abs_path} "

      nmrml_arguments += " -ref_type '#{literature_reference_type}' "
      nmrml_arguments += " -phys_state '#{physical_state_of_compound}' "
      nmrml_arguments += " -melt_point '#{melting_point}' "
      nmrml_arguments += " -boil_point '#{boiling_point}' "


      magmet_json_abs_path = 'public%s' % magmet_json
      puts "magmet_json_abs_path = #{magmet_json_abs_path}"

      nmrml_arguments += if proton_dimension == true
                           " -json_1h_path '#{magmet_json_abs_path}' "
                         else
                           " -json_13c_path '#{magmet_json_abs_path}' "
                         end

    end
    nmrml_arguments += " > #{nmrml_creator_log_abs_path} "
    nmrml_command += nmrml_arguments

    puts "nmrml_command = #{nmrml_command}"
    `#{nmrml_command}`
    FileUtils.rm_rf(Rails.root.join('project_2D_Bruker_unzipped'))
    return nmrml_basename,nmrml_url,nmrml_abs_path

    # puts session.to_hash
  end

  def generate_HSQC_csv(writer)
    writer << %w[Peak_ID Atom_ID Val Spectral_dim_ID]
    peak_id = 0
    @submission.chemical_shifts.each do |cs|
      shift = cs.chemical_shift_true != 'NA' ? cs.chemical_shift_true : cs.chemical_shift_pred
      atom_id = "#{cs.atom_symbol}#{cs.atom_id}"
      if cs.atom_symbol == 'C'
        peak_id += 1
        peak_dim = 2
      else
        peak_dim = 1
      end
      writer << [peak_id, atom_id, shift, peak_dim]
    end
  end

  def generate_TOCSY_csv(writer)
    writer << %w[Peak_ID Spectral_dim_ID Val Entity_ID Comp_index_ID Atom_ID Details Entry_ID Spectral_peak_list_ID]
    peak_id = 1
    @submission.chemical_shifts.each do |cs1|
      @submission.chemical_shifts.each do |cs2|
        shift1 = cs1.chemical_shift_true != 'NA' ? cs1.chemical_shift_true : cs1.chemical_shift_pred
        shift2 = cs2.chemical_shift_true != 'NA' ? cs2.chemical_shift_true : cs2.chemical_shift_pred
        next unless shift1 != 'NA' && shift2 != 'NA'
        atom_id_1 = "#{cs1.atom_symbol}#{cs1.atom_id}"
        atom_id_2 = "#{cs2.atom_symbol}#{cs2.atom_id}"
        writer << [peak_id, 1, shift1, 1, 1, atom_id_1, '?', '?', '5']
        writer << [peak_id, 2, shift2, 1, 1, atom_id_2, '?', '?', '5']
        peak_id += 1
      end
    end
  end

  def generate_2d_csv(spectrum_type)
    session_name = session[:submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + (session[:user_id]).to_s
    session_id = session.id
    session_directory = Rails.root.join('public','downloads',session_id.to_s)
    csv_path = Rails.root.join(session_directory.to_s, "#{session_name}_2d_assignment_table.csv")
    CSV.open(csv_path, 'w') do |writer|
      if spectrum_type.include? 'HSQC'
        generate_HSQC_csv writer
      elsif spectrum_type.include? 'TOCSY'
        generate_TOCSY_csv writer
      end
    end
  end

  def download_nmrml()
    load_submission_object_by_user
    nmrml_basename = session[:nmrml_basename]
    nmrml_url = session[:nmrml_url]
    nmrml_abs_path = session[:nmrml_abs_path]
    # session_id=session[:session_id]
    session_id = session.id
    puts "nmrml_basename = #{nmrml_basename}"
    puts "nmrml_abs_path = #{nmrml_abs_path}"
    puts "nmrml_url = #{nmrml_url}"
    puts "session_id = #{session_id}"
    file_name = nmrml_basename

    # file_name = ChemicalShiftSubmission.Download_file(@chemical_shift_submission)
    send_file nmrml_abs_path.to_s.to_s, type: 'application/csv', filename: file_name.to_s, disposition: 'attachment'
  end

  def download_2d_json
    load_submission_object_by_user
    send_file session[:json_2d_path], type: 'application/json', disposition: 'attachment'
  end

  def download_nmrml_from_outside
    load_submission_object_without_user_session
    file_name = "#{@submission.id}_#{@natural_product.np_mrd_id}_#{@submission.user_id}.nmrML"
    nmrml_abs_path = Rails.root.join('public', 'downloads', @submission.user_session_id.to_s,file_name.to_s).to_s
    send_file nmrml_abs_path.to_s, type: 'application/csv', filename: file_name.to_s, disposition: 'attachment'
  end

  def download_spectra_image_from_outside
    load_submission_object_without_user_session
    file_name = "#{@submission.id}_#{@natural_product.np_mrd_id}_#{@submission.user_id}_1h_1d.png"
    image_abs_path = Rails.root.join('public', 'downloads', @submission.user_session_id.to_s,file_name.to_s).to_s
    send_file image_abs_path.to_s, type: 'application/png', filename: file_name.to_s, disposition: 'attachment'
  end

  def process_data
    load_submission_object_without_user_session

    pub_dir     = Rails.root.join('public')
    script_dir  = Rails.root.join('backend','magmet')
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    #1d and 2d scripts
    #script      = Rails.root.join('backend','magmet/processing_np.py')
    file_list = []
    name_list = []
    quality_list = []
    param_list = []
    nmrml_list = []
    jcamp_list = []

    @submission.nmr_submissions.each do |file|
      solvent = 'D2O'
      spectrum_type = 'spectrum_type'
      reference = 'DSS'
      fid_path = file.nmr_file.path.delete('()[]{} ')
      File.rename(file.nmr_file.path, fid_path)

      remove_ident_info(fid_path)

      sp_url = file.nmr_file.url
      sp_dir = File.dirname(sp_url)

      if file.solvent
        solvent = file.solvent
      end
      if file.nmr_spectrum_type
        spectrum_type = file.nmr_spectrum_type
      end
      if file.chemical_shift_standard
        reference = file.chemical_shift_standard
      end
      if file.spectrometer_frequency
        frequency = file.spectrometer_frequency
        frequency = frequency.gsub(' MHz','')
      else
        frequency = 'undefined'
      end

      #if spectrum is "2D..."
      if spectrum_type.split('')[0] == '2'

        #run magmet
        script      = Rails.root.join('backend','magmet/processing_2d.py')
        process_np = "#{python_path} #{script} -m #{script_dir}/ -i #{fid_path} -o #{fid_path} -f #{frequency} -ref #{reference.gsub(" ", "_").gsub(/\(|\)/, "")}"
        process_np += " -sptype #{spectrum_type} -sol '#{solvent}' "
        puts "magmet command: #{process_np}"
        `#{process_np}`

        #files from magmet (no quality score)
        sp_name = "#{spectrum_type}_#{solvent}_#{reference.gsub(" ", "_").gsub(/\(|\)/, "")}_#{frequency}.json"
        #sp_quality_name = "#{spectrum_type}_#{solvent}_#{reference.gsub(" ", "_").gsub(/\(|\)/, "")}_#{frequency}.svg"
        sp_name_out = "#{spectrum_type}_#{solvent}_#{reference.gsub(" ", "_").gsub(/\(|\)/, "")}_#{frequency}"
        sp_out_path = "#{sp_dir}/#{sp_name}"
        puts "json path: #{sp_out_path}"
        #sp_quality_path = "#{sp_dir}/#{sp_quality_name}"
        name_list.append(sp_name_out)
        file_list.append(sp_out_path)
        #quality_list.append(sp_quality_path)
        #param_list.append(read_param_sp(File.join('public',sp_out_path)))

        # Generate assignment CSV for nmrML creator
        if spectrum_type == "2D-1H-1H-TOCSY"
            generate_2d_csv spectrum_type
        elsif spectrum_type == "2D-1H-13C-HSQC"
            generate_2d_csv spectrum_type
        #else let nmrpred generate it
        end


        # Generate 2D nmrML
        #nmrml_basename,nmrml_url,nmrml_abs_path = generate_nmrml(two_d: true, fid_path: fid_path)
        nmrml_basename,nmrml_url,nmrml_abs_path = generate_nmrml(two_d: true, magmet_json: sp_out_path)
        nmrml_list.append(nmrml_url)
        puts "nmrml_url =  #{nmrml_url}"

        #generate jcamp
        jcamp_dir = "#{file.submission_id}_#{@natural_product.np_mrd_id}_#{@submission.user_id}"
        jcamp_path = File.join('downloads', @submission.user_session_id.to_s,jcamp_dir.to_s).to_s
        outputjcamp = make_jcamp(spec_file = sp_out_path, output_base = jcamp_path.to_s, solvent: solvent, spectrum_type: spectrum_type, reference: reference, frequency: frequency)
        puts "jcamp return path #{outputjcamp}"
        jcamp_list.append(outputjcamp)

      #if spectrum is "1D..."
      else

        #run magmet
        script      = Rails.root.join('backend','magmet/processing_1d.py')
        process_np = "#{python_path} #{script} -m #{script_dir}/ -i #{fid_path} -o #{fid_path} -f #{frequency} -ref #{reference.gsub(" ", "_").gsub(/\(|\)/, "")}"
        process_np += " -sptype #{spectrum_type} -sol '#{solvent}' "
        puts "magmet command: #{process_np}"
        `#{process_np}`

        #files from magmet
        sp_name = "#{spectrum_type}_#{solvent}_#{reference.gsub(" ", "_").gsub(/\(|\)/, "")}_#{frequency}.json"
        sp_quality_name = "#{spectrum_type}_#{solvent}_#{reference.gsub(" ", "_").gsub(/\(|\)/, "")}_#{frequency}.svg"
        sp_name_out = "#{spectrum_type}_#{solvent}_#{reference.gsub(" ", "_").gsub(/\(|\)/, "")}_#{frequency}"
        sp_out_path = "#{sp_dir}/#{sp_name}"
        sp_quality_path = "#{sp_dir}/#{sp_quality_name}"
        name_list.append(sp_name_out)
        file_list.append(sp_out_path)
        quality_list.append(sp_quality_path)
        param_list.append(read_param_sp(File.join('public',sp_out_path)))

        #generate nmrml
        nmrml_basename,nmrml_url,nmrml_abs_path = generate_nmrml(magmet_json: sp_out_path)
        # nmrml_basename,nmrml_url,nmrml_abs_path=generate_nmrml(magmet_json: sp_out_path, suffix:sp_name_out)
        nmrml_list.append(nmrml_url)
        puts "nmrml_url =  #{nmrml_url}"

        #generate jcamp
        jcamp_dir = "#{file.submission_id}_#{@natural_product.np_mrd_id}_#{@submission.user_id}"
        jcamp_path = File.join('downloads', @submission.user_session_id.to_s,jcamp_dir.to_s).to_s
        outputjcamp = make_jcamp(spec_file = sp_out_path, output_base = jcamp_path.to_s, solvent: solvent, spectrum_type: spectrum_type, reference: reference, frequency: frequency)
        puts "jcamp return path #{outputjcamp}"
        jcamp_list.append(outputjcamp)

      end

      session[:submission_spectra_type] = spectrum_type

    end
    session[:name_list] = name_list
    session[:file_list] = file_list
    session[:sp_quality_img_list] = quality_list
    session[:param_list] = param_list
    session[:nmrml_list] = nmrml_list
    session[:jcamp_list] = jcamp_list

  end

  def remove_ident_info(specfile)
    #python script: makes backup (.bak) of spectrum zip file
    #remakes zip with identifying information removed from files (paths etc)
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    script_path = Rails.root.join('backend', 'magmet','strip_ident.py')
    `#{python_path} #{script_path} #{specfile}`
  end

  def make_jcamp(spec_file='', output_base='', solvent: '', spectrum_type: '', reference: '', frequency: '')

    # spec_file : path without? .../public/
    # must be json file for 1D, bruker processed zip file for 2D
    # output_base: not used
    # return: path with public

    load_submission_object_by_user

    puts 'making jcamp file...'

    npid = @natural_product.np_mrd_id
    subid = @submission.id
    userid = @submission.user_id
    session_id = @submission.user_session_id

    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    script_path = Rails.root.join('backend', 'jcampdx','createjcamp.py')
    session_directory = Rails.root.join('public','downloads',session_id.to_s)
    session_name = "#{subid}_#{npid}_#{userid}"

    natural_product_name = session[:natural_product_name]
    literature_reference = session[:literature_reference]
    literature_reference_type = session[:literature_reference_type]
    # solvent=session[:solvent]
    temperature = 'NA'
    # chemical_shift_standard=session[:chemical_shift_standard]
    # spectrum_type=session[:spectrum_type]
    # spectrometer_frequency=session[:spectrometer_frequency]

    ownerstr = ' --origin NP-MRD --owner NP-MRD '
    condstr = " --temp #{temperature} --sol \"#{solvent}\" --refcpd \"#{reference}\""
    molfile = Rails.root.join(session_directory.to_s,"#{session_name}_output.mol")

    jcampcmd = "#{python_path} #{script_path} --title \"#{npid}, Submission ID #{subid}, Experimental spectrum\" #{ownerstr}  "
    jcampcmd += "--block STRUCTURE --file #{molfile} --title \"#{natural_product_name}\" #{ownerstr}  " # block 1

    spectrum_type_split = spectrum_type.split("-")
    spec_dim = spectrum_type_split[0]

    frq_scale = {"1H" => 1,
                 "13C" => 10.7084 / 42.57638474,
                 "15N" => 4.316 / 42.57638474,
                 "31P" => 17.235 / 42.57638474,
                 "19F" => 40.078 / 42.57638474,
                 "2H" => 6.536,
                 }



    if spec_dim == '1D'

      nuc = spectrum_type_split[1]
      nuc_lower = nuc.downcase

      frq = if frq_scale.key?(nuc)
              (frequency.to_f * frq_scale[nuc]).to_s
            else
              "UNKNOWN"
            end

      peaks = Rails.root.join(session_directory.to_s,"#{session_name}_#{nuc_lower}_peaklist.txt")
      peaksjson = File.join(Rails.root.to_s, 'public', spec_file.gsub(".json", ".peaklist.json"))
      spec = File.join(Rails.root.to_s, 'public', spec_file)

      if File.exist?(spec)
        jcampcmd += "--block 1DSPECTRUM --file #{spec} json --title \"Experimental spectrum\" --frq #{frq} --nuc #{nuc} #{ownerstr} #{condstr} "
      end

      if File.exist?(peaksjson)
        jcampcmd += "--block 1DPEAKS --file #{peaksjson} json --title \"Peaks\"  #{ownerstr} #{condstr} --frq #{frq} --nuc #{nuc} "
      elsif File.exist?(peaks)
        jcampcmd += "--block 1DPEAKS --file #{peaks} csv --title \"Simulated peaks\"  #{ownerstr} #{condstr} --frq #{frq} --nuc #{nuc} "
      end

      if File.exist?(peaks)
        jcampcmd += "--block ASSIGNMENTS --file #{peaks} --title \"Assignments\" --ref \"#{literature_reference_type} #{literature_reference}\" --link 1 #{ownerstr} #{condstr} --frq #{frq} --nuc #{nuc} "
      end

    end

    if spec_dim == '2D'

      spec = File.join(Rails.root.to_s, 'public', spec_file)
      nuc = "#{spectrum_type_split[1]} #{spectrum_type_split[2]}"

      #get the spectrum_type_split[1] freq value
      nuc_upper = spectrum_type_split[1].upcase
      frq = if frq_scale.key?(nuc_upper)
              (frequency.to_f * frq_scale[nuc_upper]).to_s
            else
              "UNKNOWN"
            end

      if File.exist?(spec)
        jcampcmd += "--block 2DSPECTRUM --file #{spec} json --spectype #{spectrum_type} --title \"Experimental spectrum\" #{ownerstr} #{condstr} --frq #{frq} --nuc #{nuc} "
      end

      peaks = File.join(Rails.root.to_s, 'public', spec_file.gsub(".json", ".peaklist.json"))
      if File.exist?(peaks)
        jcampcmd += "--block 2DPEAKS --file #{peaks} --title \"Peaks\" --ref \"#{literature_reference_type} #{literature_reference}\" --link 1 #{ownerstr} #{condstr} --frq #{frq} --nuc #{nuc} "
      end

      for nuc in spectrum_type_split[1..2]

        nuc_lower = nuc.downcase

        frq = if frq_scale.key?(nuc)
                (frequency.to_f * frq_scale[nuc]).to_s
              else
                "UNKNOWN"
              end

        assignm = Rails.root.join(session_directory.to_s,"#{session_name}_#{nuc_lower}_peaklist.txt")
        puts assignm
        if File.exist?(assignm)
          jcampcmd += "--block ASSIGNMENTS --file #{assignm} --title \"Assignments\" --ref \"#{literature_reference_type} #{literature_reference}\" --link 1 #{ownerstr} #{condstr} --frq #{frq} --nuc #{nuc} "
        end


      end

    end

    puts jcampcmd.to_s
    outputfile = "#{session_name}.jdx"
    outputfile_path = Rails.root.join(session_directory.to_s,outputfile.to_s)
    puts outputfile_path.to_s
    # APPEND
    `#{jcampcmd} >> #{outputfile_path}`

    return File.join('/public','downloads',session_id.to_s,outputfile.to_s)
  end

  def download_jcamp
    load_submission_object_without_user_session
    file_name = "#{@submission.id}_#{@natural_product.np_mrd_id}_#{@submission.user_id}.jdx"
    file_path = Rails.root.join('public', 'downloads', @submission.user_session_id.to_s, file_name.to_s).to_s
  
    @file_exists = File.exist?(file_path)
  
    if @file_exists
      send_file file_path, type: 'text/plain', filename: file_name, disposition: 'attachment'
    else
      flash[:error] = "File does not exist"
      redirect_to root_path
    end
  end


############## Method used by the update  action starts #####################
  def preparation_for_building_shift_table(session_mol_csv, session_chsqc_table, session_submission_id, spectrum_type)
    ##### take the atoms and symbol for the submitted molecule
    @atom_symbol = Submission.ReadAtoms(session_mol_csv) # it is a two dimentional array [[atom1,symbol1],[atom2,symbol2]]
    @spectrum_type = spectrum_type
    @chemical_shifts = Array.new
    @parsed_chsqc_table = Submission.read_CHSQC_table(session_chsqc_table)

    if ChemicalShift.exists?(submission_id: session_submission_id)
      @chemical_shifts_relations = ChemicalShift.where(submission_id: session_submission_id)
      @chemical_shifts_relations.each do |c|
        case spectrum_type
        when '1D-1H', '1D-1H-DEPT90', '2D-1H-1H-COSY', '2D-1H-1H-TOCSY', '2D-1H-1H-ROESY'
          if c.atom_symbol == 'C'
            next
          else
            @chemical_shifts.push(c)
          end
        when '1D-13C', '1D-13C-DEPT90', '2D-13C-13C-COSY', '2D-13C-13C-INADEQUATE'
          if c.atom_symbol == 'H'
            next
          else
            @chemical_shifts.push(c)
          end
        else
          @chemical_shifts.push(c)
        end
      end
      @submission = Submission.find(session_submission_id)
    else
      iterator = if spectrum_type.include? "HSQC"
                   @parsed_chsqc_table
                 else
                   @atom_symbol
                 end
      iterator.each do |atom|
        case spectrum_type
        when '1D-1H', '1D-1H-DEPT90', '2D-1H-1H-COSY', '2D-1H-1H-TOCSY', '2D-1H-1H-ROESY'
          if atom[1] == 'C'
            next
          else
            c = ChemicalShift.new(atom_id: atom[0], atom_symbol: atom[1], chemical_shift_pred: atom[2],multiplet_pred: atom[3],jcoupling_pred: atom[4])
            @chemical_shifts.push(c)
          end
        when '1D-13C', '1D-13C-DEPT90', '2D-13C-13C-COSY', '2D-13C-13C-INADEQUATE'
          if atom[1] == 'H'
            next
          else
            c = ChemicalShift.new(atom_id: atom[0], atom_symbol: atom[1], chemical_shift_pred: atom[2],multiplet_pred: atom[3],jcoupling_pred: atom[4])
            @chemical_shifts.push(c)
          end
        else
          c = ChemicalShift.new(atom_id: atom[0], atom_symbol: atom[1], chemical_shift_pred: atom[2],multiplet_pred: atom[3],jcoupling_pred: atom[4])
          @chemical_shifts.push(c)
        end

      end
      @submission = Submission.find(session_submission_id)
      end
  end
############## Method used by the update  action starts #####################

  def calc_assignment_score
    cm_score = 0
    cm_i     = 0
    @chemical_shifts.each do |cs|
      test_list = ['NA','',nil]
      cs.chemical_shift_true = 'NA' if test_list.include? cs.chemical_shift_true
      cs.multiplet_true = 'NA' if test_list.include? cs.multiplet_true
      cs.jcoupling_true = 'NA' if test_list.include? cs.jcoupling_true
      # if test_list.include? cs.assigned_peaks
      #   cs.assigned_peaks = "NA"
      # end

      if [cs.chemical_shift_true,cs.multiplet_true,cs.jcoupling_true].all? { |e| e != 'NA' }
        cs.assignment_level = 'Level-1'
        cs.assignment_score = 100.0.to_s
      elsif [cs.chemical_shift_true,cs.multiplet_true].all? { |e| e != 'NA' } and cs.jcoupling_true == 'NA'
        if cs.multiplet_true == 's' or cs.multiplet_true == 'm'
          cs.assignment_level = 'Level-1'
          cs.assignment_score = 100.0
        else
          cs.assignment_level = 'Level-2'
          cs.assignment_score = 70.0
        end
      elsif cs.chemical_shift_true != 'NA' and cs.jcoupling_true == 'NA' and cs.multiplet_true == 'NA'
        cs.assignment_level = 'Level-3'
        cs.assignment_score = 50.0
      # elsif cs.chemical_shift_true == 'NA'  and cs.assigned_peaks != "NA"
      #   cs.assignment_level = "level-4"
      #   cs.assignment_score = 25.0
      elsif cs.chemical_shift_true == 'NA'
        cs.assignment_level = 'Level-4'
        cs.assignment_score = 0.0
      else
        cs.assignment_level = 'NA'
        cs.assignment_score = 'NA'
      end
      cm_score = cm_score + cs.assignment_score.to_f
      cm_i     = cm_i + 1

    end
    # pub_dir     = Rails.root.join("public")
    script_dir  = Rails.root.join('backend','magmet')
    python_path = PYTHON_ENV[Rails.env.to_s]['python_path']
    script      = Rails.root.join('backend','magmet/assignment_score_plot.py')

    session_name = session[:session_name]
    score_image_name = "#{session_name}_assignment_score.svg"
    score_image_path = Rails.root.join('public','downloads',session.id.to_s,score_image_name)
    final_score = cm_score / cm_i
    assignment_plot = "#{python_path} #{script} -s #{final_score} -f #{score_image_path}"
    `#{assignment_plot}`
    session[:score_image_path] = score_image_path
  end

  def show_assignment_report
    @score_rules = score_rules = [[1,'Yes','Yes','Yes','Level-1','100%'],
                                [2,'Yes','Yes','No','Level-2','75%'],
                                [3,'Yes','No','No','Level-3','50%'],
                                [4,'No','No','No','Level-4','0%']]

    load_submission_object_by_user
    session_name = session[:session_name]
    session_id   = session.id
    assignment_report_file = "#{session_name}_assignment_report.txt"
    @assignment_report_file_path = File.join('/downloads',session_id.to_s,assignment_report_file)

    score_image_name = "#{session_name}_assignment_score.svg"
    @score_image_path = File.join('/downloads',session.id.to_s,score_image_name)

    @chemical_shifts = @submission.chemical_shifts

    @custom_atom_numbers = get_custom_atom_numbers @submission

    render 'shared/_assignment_report', locals: {file_path: download_assignment_report_submission_path}

  end

  def download_assignment_report
    load_submission_object_by_user
    session_name = session[:session_name]
    session_id   = session.id
    assignment_report_file = "#{session_name}_assignment_report.txt"
    assignment_report_file_path = Rails.root.join('public','downloads',session_id.to_s,assignment_report_file)
    send_file assignment_report_file_path.to_s.to_s, type: 'application/csv', filename: assignment_report_file.to_s, disposition: 'attachment'
  end




############## Method used by the update  action starts #####################

  def chemical_shift_submission_from_session
    {
      'submission_meta_data_attributes' => {
        'provenance' => session[:provenance],
        'genus' => session[:genus],
        'species' => session[:species],
        'physical_state_of_compound' => session[:physical_state_of_compound],
        'melting_point' => session[:melting_point],
        'boiling_point' => session[:boiling_point],
        'literature_reference' => session[:literature_reference],
        'literature_reference_type' => session[:literature_reference_type]
        # "submission_id"  => session[:submission_id]

      }
    }
  end

  def get_custom_atom_numbers(submission)
    custom_atom_numbers = []
    if CustomAtomNumber.exists?(submission_id: submission.id)
      custom_atom_number_relations = CustomAtomNumber.where(submission_id: submission.id)
      custom_atom_number_relations.each do |c|
        custom_atom_numbers.push(c)
      end
    end
    custom_atom_numbers
  end

end


