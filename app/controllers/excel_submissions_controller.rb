class ExcelSubmissionsController < ApplicationController

  def index
  end

  def new
  end

  def create
    
    @excel_submission = ExcelSubmission.new
    @batch_submission.user_id = current_user.id
    @batch_submission.user_session_id = session[:session_id]
    print("user_session_id = #{@user_session_id}")
    @user_session_id = session[:session_id]
    @batch_submission.save!

    @batch_submission.create_batch_upload(:batch_submission_id => @batch_submission.id)
    @batch_submission.batch_upload.update(:batch_file => params["batch_submission"]["batch_uploads"]["batch_file"])

    @batch_upload = BatchUpload.where(:batch_submission_id => @batch_submission.id).first
    
    #create session directory if not
    @session_directory=Rails.root.join('public','downloads',"#{@user_session_id}")
    puts "Creating session_directory = #{@session_directory}"
    Dir.mkdir @session_directory unless File.exists?(@session_directory)
    @batch_directory = Rails.root.join('public','downloads',"#{@user_session_id}","batch_upload")
    print("batch_directory = #{@batch_directory}")
    Dir.mkdir @batch_directory unless File.exists?(@batch_directory)

    #save data in table
    # @batch_upload.user_session_id = "#{@user_session_id}"
    # @batch_upload.user_id = current_user.id
    
    # @batch_upload.save!
    session[:batch_id] = @batch_upload.id

    
  
    @captured_file = @batch_upload.batch_file.path # file name with path
    print("captured_file=#{@captured_file}")

    Zip::File.open(@captured_file) do |zip_file|
      # zip_file.first do |f|
      @fpath = File.join(@batch_directory, zip_file.first.name)
      if @fpath.end_with?(".csv")
        temp = @fpath.split("/")[-1]
        temp = temp.split(".csv")[0]
        @fpath = @fpath.split("#{temp}")[0]
        temp = "#{@batch_upload.id}"+"_#{temp}_#{current_user.id}.csv"
        @fpath = "#{@fpath}"+"#{temp}"
      else
        puts("Not a valid file")
        return
      end
      print("@fpath = #{@fpath}")
      zip_file.extract(zip_file.first, @fpath) unless File.exist?(@fpath)
      # end
    end


    @batch_values = BatchUpload.ParseCSV(@fpath,@batch_directory,@user_session_id)

    # @file_name_prefix = @fpath.split("/")[-1]
    # @file_name_prefix = @file_name_prefix.split(".mol")[0]
    # print("file_name_prefix=#{@file_name_prefix}")
    # @train_file_with_path = Rails.root.join('backend', 'nmr-pred', 'NmrPred', 'get_descriptor', 'train_hmdb_onlybmrb_swapped_COH2_fixed_7point92_with_uncommon_consistentCH2_no_null.csv')
    # NmrPred.FeatureCreation(@fpath,@utility_directory,@file_name_prefix)
    # @test_file_with_path = "#{@fpath}".split(".mol")[0]+"_testfile.csv"
    # print("@test_file_with_path = #{@test_file_with_path}")
    # NmrPred.PredictionShift(@train_file_with_path,@test_file_with_path,@utility_directory,@file_name_prefix)
    # puts"nmrpred H2O done"
    # @shift_file = "#{@utility_directory}"+"/"+"#{@file_name_prefix}_H2O_prediction.txt_after_swap"
    # puts("shift_file = #{@shift_file}")
    # @shift_position = NmrPred.PredictionResult(@shift_file,@user_session_id,@nmr_pred.id)
    # print("@shift_position = #{@shift_position}")
    
    @var = 1
    @backend_dir="backend"
    @nmr_pred_dir="nmr-pred"
    @draw_mol_script_basename="draw_mol.py"
    @backend_batch_dir = "batch_upload"
    @excel_generator_script_with_path = Rails.root.join("#{@backend_dir}","#{@backend_batch_dir}","excel_form_generation_current.py")
    puts("@excel_generator_script_with_path = #{@excel_generator_script_with_path}")
    #create a csv file with each generated file information which will be the input in the excel
    fpath_split = @fpath.split(".csv")[0]
    @project_name = @fpath.split("/")[-1].split(".csv")[0]
    @list_of_input_files_for_excel_generator = "#{fpath_split}_list_of_input_files_for_excel_generator.csv"
    puts("list_of_input_files_for_excel_generator = #{@list_of_input_files_for_excel_generator}")
    CSV.open("#{@list_of_input_files_for_excel_generator}", "wb") do |csv|  
      @batch_values.each do |b_v|
        @smiles = b_v[1]
        # creating mol, imgae and prediction file
        @path_to_temp_model, @path_to_temp_image, @path_to_csv, @compound_name, image_names =
          BatchUpload.DrawMol(@smiles, @batch_directory, @backend_dir, @nmr_pred_dir, @draw_mol_script_basename,
                              @batch_upload.id, b_v[0], @batch_upload.user_id, @var)
        # creating mol, imgae and prediction file
        csv << [@compound_name.to_s, @path_to_csv.to_s].push(*image_names, @compound_name.to_s, @smiles.to_s)
        @var = @var + 1
      end
    end
    execl_file_with_path = BatchUpload.GenerateExcel(@project_name,@excel_generator_script_with_path, @list_of_input_files_for_excel_generator, @batch_directory)
    print("execl_file_with_path = #{execl_file_with_path}")
    session[:execl_file_with_path] = "#{execl_file_with_path}"
    respond_to do |format|
      format.html { redirect_to @batch_submission }
    end
  end

  def show
  end





end
