class UserSessionsController < ApplicationController

  # Creates a new user session. Encodes a JWT token and checks if a `return_token` parameter is present.
  # If it is, it decodes the token and sets the `return_url` session variable.
  # If the current user is already logged in, it redirects to the `return_url` with the user token.
  # If the user is not logged in, it initializes a new `UserSession` object.
  def new_old
    encode_jwt
    if params[:return_token]
      begin
        decoded_jwt, _header = decode_jwt(params[:return_token])
        session[:return_url] = decoded_jwt['page_to_return_to']
        if current_user
          redirect_to "#{session[:return_url]}?token=#{session[:user_token]}"
          session.delete(:return_url)
        else
          @user_session = UserSession.new
        end
      rescue JWT::DecodeError
        flash[:error] = 'Invalid token'
      end
    else
      if current_user
        redirect_to root_path
      else
        @user_session = UserSession.new
      end
    end
  end

  def new
    encode_jwt
    if current_user
      # Construct the redirect URL with the user_token as a query parameter
      redirect_to "#{ENV['deposition_site']}#{ENV['login']}?user_token=#{session[:user_token]}"
    else
      @user_session = UserSession.new
    end
  end

  # Creates a new user session when a user logs in. Initializes a new `UserSession` object with the provided parameters.
  # If the user session is saved successfully, it encodes a JWT token and sets the redirect URL.
  # It then renders a JSON response with the user token and redirect URL.
  def create_old
    @user_session = UserSession.new(user_session_params)
    if @user_session.save
      encode_jwt
      
      redirect_url = if session[:return_url].present?
        session.delete(:return_url)
      else
        flash[:notice] = "Successfully Logged in!"
        root_path
      end
      
      # Return the user_token and redirect_url
      render json: { user_token: session[:user_token], redirect_url: redirect_url }
    else
      render :new
    end
  end

  def create
    @user_session = UserSession.new(user_session_params)
    if @user_session.save
      encode_jwt   
      # Construct the redirect URL with the user_token as a query parameter
      redirect_to "#{ENV['deposition_site']}#{ENV['login']}?user_token=#{session[:user_token]}"
    else
      render :new
    end
  end

  # Destroys a user session when a user logs out. If a user session is present, it destroys the session,
  # deletes the user token from the session, and sets a flash message.
  # It then renders a JSON response with the user token and redirect URL.
  def destroy_old
    if current_user_session.present?
      current_user_session.destroy
      session.delete(:user_token)
      flash[:notice] = "Successfully Logged out!"

      redirect_url = root_path
      render json: { user_token: session[:user_token], redirect_url: redirect_url }
    else 
      Rails.logger.error "DEPOSITION LOGOUT DEBUG => Logout failure User not synced"
      flash[:notice] = "Logout Failure User not synced!"
      redirect_back_or_default(root_path)
    end
  end


  def destroy
    if current_user_session.present?
      current_user_session.destroy
      session.delete(:user_token)
      flash[:notice] = "Successfully Logged out!"

      redirect_to "#{ENV['deposition_site']}#{ENV['logout']}"
    else 
      Rails.logger.error "DEPOSITION LOGOUT DEBUG => Logout failure User not synced"
      flash[:notice] = "Logout Failure User not synced!"
      redirect_back_or_default(root_path)
    end
  end
  
  # Logs out a user remotely from the deposition website to sync logout at the database website.
  # Decodes the provided JWT token and checks if a user session is present and if the user ID in the decoded JWT matches the current user's ID.
  # If it does, it destroys the user session, deletes the user token from the session, and sets a flash message.
  # It then redirects to the default or back URL.
  def remote_logout
    decoded_jwt, _header = decode_jwt(params[:user_token])
    if current_user_session.present? && decoded_jwt['user_id'].to_i == current_user.id
      current_user_session.destroy
      session.delete(:user_token)
      flash[:notice] = "Successfully Logged out!"
    else 
      Rails.logger.error "DEPOSITION LOGOUT DEBUG => Logout failure User not synced"
      flash[:notice] = "Logout Failure User not synced!"
    end
    redirect_back_or_default(root_path)
  end

  private

  def user_session_params
    params.require(:user_session).permit(:email, :password, :remember_me).to_h
  end

  # Encodes a JWT token. If a user is logged in, it sets the payload with the user's ID and encodes a JWT token with the payload and secret key.
  # It then sets the user token in the session.
  def encode_jwt
    if current_user.present?
      payload = { 
        user_id: current_user.id
      }

      session[:user_token] = JWT.encode(payload, Rails.application.secrets.jwt_secret_key, 'HS256')
      puts "jwt #{session[:user_token]}"
    end
  end

  # Decodes a provided JWT token. Decodes the token with the secret key and returns the decoded JWT and header.
  def decode_jwt(token)
    JWT.decode(token, Rails.application.secrets.jwt_secret_key, true, { algorithm: 'HS256' })
  end

  # DEPRECATED: Synced login to the deposition website so that users are logged in at both database
  # and deposition website. Function made into a javascript fetch call from the views/submissions/new.html.slim
  def deposition_login
    # Define the request URL and body
    url = URI.parse(ENV['deposition_api'] + LOGIN_ENDPOINT) 
    body = { token: session[:user_token] }.to_json
    Rails.logger.error body
  
    # Create the HTTP request
    request = Net::HTTP::Post.new(url.path)
    request['Content-Type'] = 'application/json'
    request['Cookie'] = "cookie_name=#{session[:cookie_value]}" # Include the cookie
    request['Origin'] = 'https://dev.np-mrd.org' # Set the Origin header
  
    request.body = body
  
    # Send the request
    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true  # Use SSL for HTTPS
    http.request(request)
  end

  # DEPRECATED: Synced logout to the deposition website so that users are logged out at both database
  # and deposition website. Function made into a javascript fetch call from the views/navigation/_navigation.html.slim
  def deposition_logout
    # Define the request URL and body
    url = URI.parse(ENV['deposition_api'] + LOGOUT_ENDPOINT) 

    # Create the HTTP request
    request = Net::HTTP::Post.new(url.path)
    # Send the request
    http = Net::HTTP.new(url.host, url.port)
    http.use_ssl = true  # Use SSL for HTTPS
    http.request(request)
  end
end
