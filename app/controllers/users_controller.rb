class UsersController < ApplicationController
  before_action :require_correct_user, only: [:edit, :update, :destroy]

  def new
    @user = User.new
  end

  def edit
    @user = User.find(params[:id])
  end  

  def create
    captcha_verified = verify_recaptcha
    @user = User.new(users_params)
    if @user.save && captcha_verified
      flash[:success] = "Thank you for registration!\nWe will send you an email with confirmation. Please check your e-mail inbox and confirm your account."
      @user.deliver_verification_instructions!
      redirect_to sign_in_path
    else
      @user.errors[:captcha] = "Are you sure you are a human? Try again." unless captcha_verified
      render :new
    end
  end

  def update
    @user = User.find(params[:id])
    require_correct_user
    if @user.update(users_params)
      flash[:success] = "Profile updated successfully"
      redirect_to sign_in_path
    else
      render :edit
    end
  end

  def destroy
    @user = User.find(params[:id])
    @user.destroy
    flash[:success] = "User deleted successfully"
    redirect_to root_path
  end

  # Prevent users modifying other users login credentials
  def require_correct_user
    @user = User.find(params[:id])
    unless current_user == @user
      flash[:error] = "You are not authorized to access this page"
      redirect_to root_path
    end
  end  
  
  private

  def users_params
    params.require(:user).permit(:email, :password, :password_confirmation, :name, :organization).to_h
  end
end
