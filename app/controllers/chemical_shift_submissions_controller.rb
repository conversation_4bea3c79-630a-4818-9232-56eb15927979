class ChemicalShiftSubmissionsController < ApplicationController
  include Shared::ChemicalShiftSubmissionLoader
  include Shared::NaturalProductLoader
  require 'fileutils'
  require 'open-uri'
  skip_before_action :verify_authenticity_token

  def new
    if UserSession.try(:find).nil?
      redirect_away sign_in_path
    end
    @chemical_shift_submission = ChemicalShiftSubmission.new()
    @chemical_shift_submission.build_chemical_shift_submission_meta_data
  end
############## create action starts #####################
  def create
    status = "pending"
    case params[:state]
    when "post_np"
    # Mark: collect info about referenfe speciies, etc from params here

      # Grab from params (if submissions exist for the compound, cannot save metadata until after the alert)
      @natural_product = load_create_natural_product(nil, params[:compound_name], params[:structure_input])
      @smiles_structure = @natural_product.input_smiles
      @input_smiles = @natural_product.input_smiles
      session[:structure_input] = @input_smiles

      puts "SMILES INPUT DEBUGGING"
      puts params[:structure_input]
      puts @input_smiles
      puts session[:structure_input]

      session[:genus] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:genus]
      session[:species] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:species]
      session[:physical_state_of_compound] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:physical_state_of_compound]
      session[:melting_point] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:melting_point]
      session[:boiling_point] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:boiling_point]
      session[:literature_reference] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:literature_reference]
      session[:literature_reference_type] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:literature_reference_type]
      if session[:literature_reference_type] == "Unpublished / Under Review"
        session[:literature_reference] = "Unpublished / Under Review"
      end
      session[:provenance] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:provenance]
      # Check to see if natural product already existed in NP-MRD
      if !@natural_product.exported?
        save_new_chemical_shift_submission
      else # Display alert
        @chemical_shift_submissions = ChemicalShiftSubmission.where(natural_product_id: @natural_product.id, valid: true)
        @page = "alert"
      end
    when "post_alert"
      # User chooses to save or cancel the submission
      if params[:user_input] == "Continue"
        save_new_chemical_shift_submission
      elsif params[:user_input] == "Cancel"
        status = "cancelled"
      end
    end
    unless status == "pending"
      if status == "cancelled"
        respond_to do |format|
          format.js { render ajax_redirect_to("/submissions?submission=cancelled") }
        end
      end
    else
      respond_to do |format|
        format.js
      end
    end
  end
######################### Create action finished ######################################

##################### index action starts ####################
  def index
    if UserSession.try(:find).nil?
      redirect_away sign_in_path
    else
      load_chemical_shift_submission_index_objects_by_user
      respond_to do |format|
        format.html
      end
    end
  end
######################### Index action finished ######################################

############## Edit action starts #####################
  def edit
    @chemical_shift_submission = ChemicalShiftSubmission.find(params[:id])

    if !@chemical_shift_submission.chemical_shift_submission_meta_data
      @chemical_shift_submission.build_chemical_shift_submission_meta_data
    end

    @natural_product = NaturalProduct.find(@chemical_shift_submission.natural_product_id)


    temp_image_basename = "#{@chemical_shift_submission.id}" + "_#{@natural_product.np_mrd_id}_" +
                            "#{current_user.id}" + "_temp_3D.png"
    @temp_mol_basename = "#{@chemical_shift_submission.id}" + "_#{@natural_product.np_mrd_id}_" +
                          "#{current_user.id}" + "_temp_3D.mol"
    csv_basename = "#{@chemical_shift_submission.id}" + "_#{@natural_product.np_mrd_id}_" +
                    "#{current_user.id}" + "_mol.csv"
    @threeD_image_url = File.join("/downloads",
                                  "#{@chemical_shift_submission.user_session_id}",temp_image_basename)
    @threeD_mol_url = File.join("/downloads",
                                "#{@chemical_shift_submission.user_session_id}",@temp_mol_basename)
    stdin,stdout,stderr =
      Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} " +
                     "#{Rails.root}/public/python/smiles_to_canonical_mol.py '#{@natural_product.input_smiles}'")
    @read_mol = stdout.gets(nil).to_s
    @mol_csv_url = File.join("public",
                             "downloads","#{@chemical_shift_submission.user_session_id}",csv_basename)

    session[:threeD_image] = @threeD_image_url
    session[:mol_csv_url] = @mol_csv_url
    session[:threeD_mol_name] = @temp_mol_basename
    session[:threeD_mol_url] = @threeD_mol_url
  end
######################### Edit action finished ######################################

############## Update action starts #####################
  def update
    status = "pending"
    case params[:state]
    when "post_np"
      # Save meta data
      @chemical_shift_submission = ChemicalShiftSubmission.find(params[:id])
      @chemical_shift_submission.update(params.require(:chemical_shift_submission).permit!)
      # Store in session
      natural_product = NaturalProduct.find(@chemical_shift_submission.natural_product_id)
      session[:structure_input] = session[:structure_input]
      session[:natural_product_id] = natural_product.id
      session[:natural_product_np_mrd_id] = natural_product.np_mrd_id
      session[:chemical_shift_submission_id] = @chemical_shift_submission.id
      @threeD_image_url = session[:threeD_image]
      # Pass to metadata page
      @chemical_shift_submission_meta_data = @chemical_shift_submission.chemical_shift_submission_meta_data

      session[:threeD_mol_name] = session[:threeD_mol_name]
      session[:threeD_mol_url] = session[:threeD_mol_url]
      @page = "metadata"




    when "post_meta"
      # Save meta data
      @chemical_shift_submission = ChemicalShiftSubmission.find(params[:id])
      @chemical_shift_submission.update(params.require(:chemical_shift_submission).permit!)
      @chemical_shift_submission.save!
      if @chemical_shift_submission.chemical_shift_submission_meta_data.temperature.nil? or @chemical_shift_submission.chemical_shift_submission_meta_data.temperature.empty? or @chemical_shift_submission.chemical_shift_submission_meta_data.temperature == "NA"
        @chemical_shift_submission.chemical_shift_submission_meta_data.update(temperature: "20")
      end
      if @chemical_shift_submission.chemical_shift_submission_meta_data.melting_point.empty?
        @chemical_shift_submission.chemical_shift_submission_meta_data.update(melting_point: "NA")
      end
      if @chemical_shift_submission.chemical_shift_submission_meta_data.boiling_point.empty?
        @chemical_shift_submission.chemical_shift_submission_meta_data.update(boiling_point: "NA")
      end

      # Store in session
      session[:natural_product_id] = session[:natural_product_id]
      session[:chemical_shift_submission_id] = session[:chemical_shift_submission_id]
      session[:threeD_image] = session[:threeD_image]
      #session[:mol_csv_url] = session[:mol_csv_url]
      session[:spectrum_type] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:spectrum_type]
      session[:solvent] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:solvent]
      session[:spectrometer_frequency] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:spectrometer_frequency]
      session[:temperature] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:temperature]
      session[:chemical_shift_standard] = params[:chemical_shift_submission][:chemical_shift_submission_meta_data_attributes][:chemical_shift_standard]
      session[:chemical_shift_submission] = params[:chemical_shift_submission]
      session[:structure_input] = session[:structure_input]

      session[:threeD_mol_name] = session[:threeD_mol_name]
      session[:threeD_mol_url] = session[:threeD_mol_url]

      #paths
      puts "PATHS AFTER POST META"
      puts session[:natural_product_id]
      puts session[:chemical_shift_submission_id]
      puts session[:threeD_mol_name]
      puts session[:threeD_mol_url]

      # if mol.csv exists do not redo prediction, just get the file...

      natural_product = NaturalProduct.find(@chemical_shift_submission.natural_product_id)
      session_id = "#{@chemical_shift_submission.user_session_id}"
      submission_prefix = "#{@chemical_shift_submission.id}" + "_#{natural_product.np_mrd_id}" + "_#{@chemical_shift_submission.user_id}"
      temp_mol_name = "#{submission_prefix}" + "_temp_3D.mol"

      session_directory = Rails.root.join("public","downloads","#{session_id}")
      path_to_model = Rails.root.join("public","downloads","#{session_id}","#{temp_mol_name}")

      if not File.exist?(Rails.root.join("public","downloads","#{session_id}","#{submission_prefix}_mol.csv"))

          puts "PATHS FOR NMRPRED"
          puts session_id
          puts submission_prefix
          puts temp_mol_name
          puts session_directory
          puts path_to_model

          #
          # This uses a 3d mol to make intial nmrshiftdb prediction with nmrpred
          # Returns full path
          #
          nmrpred_assignment = run_nmrpred_intialprediction("#{path_to_model}", session_directory, submission_prefix)
          #
          # This takes nmrpred output assignmenttable.txt and makes mol.csv which is parsed into the website table
          # returns file name
          #
          csv_basename = run_npmrd_make_mol_csv("#{path_to_model}", nmrpred_assignment, session_directory, submission_prefix)
          #path_to_csv=Rails.root.join("#{session_directory}","#{csv_basename}")

          @mol_csv_url = File.join("public","downloads","#{session_id}",csv_basename)
          session[:mol_csv_url] = @mol_csv_url

      else
          puts "MOLCSV ALREADY GENERATED, SKIPPING PREDICTION"
          @mol_csv_url = File.join("public","downloads","#{session_id}","#{submission_prefix}_mol.csv")
          session[:mol_csv_url] = @mol_csv_url

      end






#      #### generating predictions using nmrpred ##########
#      ### It will return back the file with path that has predicrion result###
#      if session[:solvent] == "H2O" or session[:solvent] == "CHCl3" or session[:solvent] == "DMSO" and session[:spectrum_type] == "1D-1H"
#        if session[:solvent] == "H2O"
#          @sol = "D2O"
#        elsif session[:solvent] == "CDCl3"
#          @sol = "CDCL3"
#        else
#          @sol = session[:solvent]
#        end
#        @spec_type = "1H"
#        @c_s_s_id = session[:chemical_shift_submission_id]
#        @prediction_result_file_with_path = NmrPred.create_agent(session[:structure_input],@sol,@spec_type,@c_s_s_id)
#        @prediction_result_file_with_path = @prediction_result_file_with_path.gsub(".txt",".csv")
#        puts("@prediction_result_file_with_path = #{@prediction_result_file_with_path}")
#      end


       #### merge machine learing output with original output
       #### this overwrites the original mol.csv but a copy is kept as mol.csv.orig
       #### after this the website can continue loading the mol.csv as usual

       #merge_shiftdb_ml_pred(path_to_csv, @prediction_result_file_with_path)


      ##################################

      @custom_atom_numbers = get_custom_atom_numbers @chemical_shift_submission

      @threeD_image_url = session[:threeD_image]
      @page = "chemical_shift_new"
      ##### take the atoms and symbol for the submitted molecule
      puts session[:mol_csv_url]
      puts session[:chemical_shift_submission_id]
      puts session[:spectrum_type]
      preparation_for_bulding_shift_table(session[:mol_csv_url],session[:chemical_shift_submission_id],session[:spectrum_type])

      # make atom length available for custom numbering restrictions
      #@atom_length = Submission.calc_atom_length(session[:submission_id]).to_i
    when "post_chemical_shift"
      session[:structure_input] = session[:structure_input]
      session[:chemical_shift_submission_id] = session[:chemical_shift_submission_id]
      session[:natural_product_id] = session[:natural_product_id]
      session[:threeD_image] = session[:threeD_image]
      session[:mol_csv_url] = session[:mol_csv_url]
      session[:spectrum_type] = session[:spectrum_type]
      session[:threeD_mol_name] = session[:threeD_mol_name]
      session[:threeD_mol_url] = session[:threeD_mol_url]
      shift_hash = params[:chemical_shift_submission]["chemical_shifts_attributes"]
      s = ChemicalShiftSubmission.find(session[:chemical_shift_submission_id])
      if ChemicalShift.exists?(chemical_shift_submission_id: session[:chemical_shift_submission_id])
        for key, values in shift_hash do
          if ChemicalShift.where({chemical_shift_submission_id: session[:chemical_shift_submission_id], atom_id: values["atom_id"]} ).present?
            cs = ChemicalShift.where({chemical_shift_submission_id: session[:chemical_shift_submission_id], atom_id: values["atom_id"]} ).first
            for column, value in values do
              if column != "id"
                cs[column] = value
                cs.save!
              end
            end
            s = ChemicalShiftSubmission.find(session[:chemical_shift_submission_id])
            s.valid = false
            s.save!
          end
        end

      else
        for key, values in shift_hash do
          cs = ChemicalShift.new()
          for column, value in values do
            cs[column] = value
          end
          s.chemical_shifts << cs
          s.save!
          s = ChemicalShiftSubmission.find(session[:chemical_shift_submission_id])
          s.valid = false
          s.save!
        end
        ChemicalShift.conditional_save(s)
      end
      @natural_product = NaturalProduct.find(s.natural_product_id)
      preparation_for_bulding_shift_table(session[:mol_csv_url],session[:chemical_shift_submission_id],session[:spectrum_type])
      @meta_data = s.chemical_shift_submission_meta_data
      preparation_for_bulding_shift_table(session[:mol_csv_url],session[:chemical_shift_submission_id],session[:spectrum_type])

      calc_assignment_score
      session_name = session[:session_name]
      score_image_name = "#{session_name}_assignment_score.svg"
      @score_image_path = File.join("/downloads","#{session.id}",score_image_name)


      @custom_atom_numbers = Array.new
      if CustomAtomNumber.exists?(chemical_shift_submission_id: @chemical_shift_submission.id)
        @custom_atom_number_relations = CustomAtomNumber.where(chemical_shift_submission_id: @chemical_shift_submission.id)
        @custom_atom_number_relations.each do |c|
          @custom_atom_numbers.push(c)
        end
        @chemical_shift_submission = ChemicalShiftSubmission.find(@chemical_shift_submission.id)
      else
        @atom_symbol.each do |atom|
          c = CustomAtomNumber.new(atom_id: atom[0], atom_symbol: atom[1])
          @custom_atom_numbers.push(c)
        end
        @chemical_shift_submission = ChemicalShiftSubmission.find(@chemical_shift_submission.id)
      end
      # puts "@chemical shift ............. #{@chemical_shifts}"
      # puts "s.chemical_shifts .....#{s.chemical_shifts.inspect()}"
      s.chemical_shifts << @chemical_shifts
      s.save!
      # puts "s.chemical_shifts .....after save #{s.chemical_shifts.inspect()}"


      @page = "verification"
    when "post_verification"
      @chemical_shift_submission = ChemicalShiftSubmission.find(params[:id])
      # User chooses whether or not to verify the submission
      if params[:user_input] == "Continue"
        @chemical_shift_submission.update(params.require(:chemical_shift_submission).permit!)
        @chemical_shift_submission.valid = true # Finished submission mark as valid
        @chemical_shift_submission.save!
        if @chemical_shift_submission.chemical_shift_submission_meta_data.physical_state_of_compound.nil? or @chemical_shift_submission.chemical_shift_submission_meta_data.physical_state_of_compound.empty?
          @chemical_shift_submission.chemical_shift_submission_meta_data.update(physical_state_of_compound: "NA")
        end

        if @chemical_shift_submission.chemical_shift_submission_meta_data.literature_reference_type == "Unpublished / Under Review"
          @chemical_shift_submission.chemical_shift_submission_meta_data.update(literature_reference: "Unpublished / Under Review")
        end


        # Export natural product
        natural_product = @chemical_shift_submission.natural_product
        natural_product.export = true
        natural_product.save!

        # save or create the reference. for now only PMID
        ChemicalShiftSubmission.Generate_Reference_Pubmed(params[:id])
      #  ######## create new order again ####
      #  returned_atom_order = ChemicalShiftSubmission.new_atom_order(@chemical_shift_submission.id)
      #  puts("returned_atom_order = #{returned_atom_order}")
      #  returned_atom_order_string = returned_atom_order.map(&:inspect).join(',')
      #  np = NaturalProduct.find((@chemical_shift_submission.natural_product_id).to_i)
      #  puts("outside np = #{np}")
      #  temp_mol_basename="#{@chemical_shift_submission.id}" + "_#{np.np_mrd_id}_" + "#{@chemical_shift_submission.user_id}" + "_temp_3D.mol"
      #  temp_mol_basename_out="#{@chemical_shift_submission.id}" + "_#{np.np_mrd_id}_" + "#{@chemical_shift_submission.user_id}" + "_temp_3D_out.mol"
      #  threeD_mol_url = Rails.root.join("public","downloads","#{@chemical_shift_submission.user_session_id}","#{temp_mol_basename}")
      #  threeD_mol_url_out = Rails.root.join("public","downloads","#{@chemical_shift_submission.user_session_id}","#{temp_mol_basename_out}")
      #  puts("outside threeD_mol_url = #{threeD_mol_url}")
      #  puts("outside threeD_mol_url_out = #{threeD_mol_url_out}")
      #  renumbering_script_path = "#{Rails.root}/public/python/renumber_atoms.py"
      #  renumbering_script_log_path = Rails.root.join("public","downloads","#{@chemical_shift_submission.user_session_id}","renumbering.txt")
      #  python_path = PYTHON_ENV["#{Rails.env}"]['python_path']
      #  python_renumbering_command=""
      #  python_renumbering_command+="#{python_path} "
      #  python_renumbering_command+="#{renumbering_script_path} "
      #  python_renumbering_arguments=""
      #  python_renumbering_arguments+="-i '#{threeD_mol_url}' "
      #  python_renumbering_arguments+="-o '#{threeD_mol_url_out}' "
      #  python_renumbering_arguments+="-n '#{returned_atom_order_string}' "
      #  python_renumbering_arguments+=" > '#{renumbering_script_log_path}' "
      #  python_renumbering_command+="#{python_renumbering_arguments} "
      #  puts "Python log is:"
      #  puts "#{renumbering_script_log_path}"
      #  puts "Running atom renumbering Python script:"
      #  puts "#{python_renumbering_command}"
#
      #  `#{python_renumbering_command}`
      #  stdin,stdout,stderr = Open3.popen3("#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/rdkit_mol_to_mol_renumbering.py '#{threeD_mol_url_out}'")
      #  renumbered_mol = stdout.gets(nil).to_s
      #  puts("renumbered_mol = #{renumbered_mol}")
      #  @chemical_shift_submission.renumberedMol = renumbered_mol
      #  @chemical_shift_submission.save!
      #  puts("saved renumbered in chemical shift submissions table in post verification")
      #  # `#{PYTHON_ENV["#{Rails.env}"]['python_path']} #{Rails.root}/public/python/renumber_atoms.py -i #{threeD_mol_url} -o #{threeD_mol_url_out} -n #{returned_atom_order_string}" `
      #  #################################################

        status = "verified"
      elsif params[:user_input] == "Cancel"
        status = "cancelled"
      end
    end
    unless status == "pending"
      if status == "verified"
        # Notifier.completed_deposition(current_user.email, natural_product.np_mrd_id, {'ValidationReport.zip': 'public/validation_report.zip'}).deliver_now
        redirect_to action: :show, notice: 'Submission saved successfully!'
      elsif status == "cancelled"
        redirect_to action: :show, notice: 'Verification still required!'
      end
    else
      respond_to do |format|
        format.js
      end
    end
  end

######################### Update action finished ######################################

############## Show action starts #####################
  def show
    session_name = session[:session_name]
    # session_id   = session[:session_id]
    session_id   = session.id
    nmrml_name  = "#{session_name}.nmrML"
    # @nmrml_path = File.join("/downloads","#{session[:session_id]}",nmrml_name)
    @nmrml_path = File.join("/downloads","#{session.id}",nmrml_name)
    # @score_image_path = File.join("/downloads","#{session[:session_id]}",'assignment_score.svg')
    score_image_name = "#{session_name}_assignment_score.svg"
    @score_image_path = File.join("/downloads","#{session.id}",score_image_name)

    if UserSession.try(:find).nil?
      redirect_away sign_in_path
    else
      @source = 'experimental'
      load_chemical_shift_submission_object_by_user
      @chemical_shifts = @chemical_shift_submission.chemical_shifts

      calc_assignment_score
      @chemical_shift_submission.chemical_shifts = @chemical_shifts
      @chemical_shift_submission.save!
      assignment_report_file_name = "#{session_name}_assignment_report.txt"
      # assignment_report_file_path = File.join("public/downloads","#{session[:session_id]}",assignment_report_file_name)
      @assignment_report_file_path = File.join("public/downloads","#{session.id}",assignment_report_file_name)
      ChemicalShiftSubmission.save_assignment_report(@chemical_shift_submission,file_path = @assignment_report_file_path)

      @custom_atom_numbers = get_custom_atom_numbers @chemical_shift_submission
      respond_to do |format|
        format.html
      end
    end

    # @test = "test header"
    puts("No. of Deposited Atoms With True Chemical Shift Data=#{@percentage_of_assigned_atoms}%")
    puts("No. of Deposited Atoms With True Chemical Shift Data Similar to Predicted Values(Difference With Predcted Value < 0.20ppm)  =#{@percentage_of_good_true_values}%")
    puts("No. of Deposited Jcoupling Constant Data  =#{@percentage_of_assigned_jcoupling}%")
    puts("No. of Deposited Multiplet Data  =#{@percentage_of_assigned_multiplet}%")
    runNMRpred()
    generate_nmrml()
    make_jcamp()
    # make_spectra_json

    #bll:
    #
    #peaklist is the ..._1h_peaklist.txt etc generated by nmrpred using OLD numbering
    #
    #need a file with mapping of old numbers to new numbers in csv format eg
    ##header/comments starting with "#"
    #1,2
    #2,3
    #3,1
    #
    #if 1H peaklist exists:
    #new_1h_peaklist = remap_nmrpred_peaklist_file(peaklist, mappingfile)
    #if 13C peaklist exists:
    #new_13c_peaklist = remap_nmrpred_peaklist_file(peaklist, mappingfile)
    #
    #some new version of generate_nmrml() BUT using NEW peaklists (and mol with NEW numbering?)
    #
    #...then what?

  end
######################## Show action finished ######################################

############## Custom "Download" action starts #####################
  def download_submitted_data
    load_chemical_shift_submission_object_without_user_session
    file_name = ChemicalShiftSubmission.Download_file(@chemical_shift_submission)
    send_file "#{Rails.root.join('public','downloads',file_name).to_s}", type: "application/csv", filename: "#{file_name}", disposition: 'attachment'
  end
######################## Custom "Download" action finished ######################################

  def download_renumbered_mol
    load_chemical_shift_submission_object_without_user_session
    temp_mol_basename_out="#{@chemical_shift_submission.id}" + "_#{@natural_product.np_mrd_id}_" + "#{@chemical_shift_submission.user_id}" + "_temp_3D_out.mol"
    # threeD_mol_url_out = Rails.root.join("public","downloads","#{@chemical_shift_submission.user_session_id}","#{temp_mol_basename_out}")
    send_file "#{Rails.root.join('public','downloads',@chemical_shift_submission.user_session_id,temp_mol_basename_out).to_s}", type: "application/mol", filename: "#{temp_mol_basename_out}", disposition: 'attachment'
  end

############## Method used by the create action starts #####################
  def save_new_chemical_shift_submission
    puts
    # After alert
    if params[:natural_product_id]
      puts("with alert save_new_chemical_shift_submission")
      @natural_product = NaturalProduct.find(params[:natural_product_id])
      @input_smiles = @natural_product.input_smiles
      @chemical_shift_submission = ChemicalShiftSubmission.new(chemical_shift_submission_from_session)
    else # No alert
      puts("no alert save_new_chemical_shift_submission")
      @chemical_shift_submission = ChemicalShiftSubmission.new(params.require(:chemical_shift_submission).permit!)
    end

    # Save chemical shift submission and metadata
    @chemical_shift_submission.user_id = current_user.id
    # @chemical_shift_submission.user_session_id = session[:session_id]
    @chemical_shift_submission.user_session_id = session.id
    puts("\n\n\n\n\nsession[:session_id] = #{session[:session_id]} and session = #{session}")
    puts("\n\n\n\n\nsession[:session_id] = #{session.id}")
    puts("\n\n\n\n\nsession[:session_id] = #{request.session_options[:id]}")
    puts("\n\n\n\n\nsession keys = #{session.keys}")
    puts("\n\n\n\n\nsession values = #{session.values}")
    @chemical_shift_submission.natural_product_id = @natural_product.id
    @chemical_shift_submission.save!
    session[:chemical_shift_submission_id] = @chemical_shift_submission.id


    # Store in session
    session[:chemical_shift_submission_id] = @chemical_shift_submission.id
    session[:natural_product_id] = @natural_product.id
    session[:natural_product_name] = @natural_product.name
    session[:natural_product_np_mrd_id] = @natural_product.np_mrd_id
    session[:user_id] = @chemical_shift_submission.user_id
    session[:input_smiles_key] = session[:structure_input]


    #Paths for python script outputs
    session_name=session[:chemical_shift_submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{session[:user_id]}"
    session[:session_name] = session_name
    # session_id=session[:session_id]
    session_id=session.id
    # puts "session_id= #{session[:session_id]}"
    puts "session_id= #{session.id}"
    session_directory=Rails.root.join('public','downloads',"#{session_id}")
    puts "Creating session_directory = #{session_directory}"
    Dir.mkdir session_directory unless File.exists?(session_directory)

    puts "SMILES INPUT DEBUGGING2"
    puts @natural_product.smiles
    puts @input_smiles
    puts session[:input_smiles_key]

    #
    # This takes the submitted smiles string and generates 3d mol files and images
    #
    smiles=session[:input_smiles_key]
    non_canonical_mol_abs_path, non_canonical_base_image_abs_path, non_canonical_equiv_image_abs_path = runDrawMol(smiles, session_directory, session_name)
    puts "drawmol paths:"
    puts non_canonical_mol_abs_path, non_canonical_base_image_abs_path, non_canonical_equiv_image_abs_path
    #
    # makes copies of mol and image file for the website use
    # returns file names WITHOUT full path
    #
    temp_model_basename, temp_image_basename = make_files_from_drawmol(non_canonical_mol_abs_path, non_canonical_base_image_abs_path, session_directory, session_name)
    path_to_temp_model=Rails.root.join("#{session_directory}","#{temp_model_basename}")
    path_to_temp_image=Rails.root.join("#{session_directory}","#{temp_image_basename}")
    puts "temp files paths:"
    puts temp_model_basename, temp_image_basename, path_to_temp_model, path_to_temp_image

    #new_file_name = session[:natural_product_np_mrd_id].to_s+"_image3D.png"
    #new_file_path = Rails.root.join('public', 'structures',new_file_name).to_s
    #File.rename(non_canonical_base_image_abs_path,new_file_path)
    #@threeD_image_url  = "/structures/"+new_file_name
    @threeD_image_url = File.join("/downloads","#{session_id}",temp_image_basename)
    session[:threeD_image] = @threeD_image_url

    session[:temp_model_basename] = temp_model_basename
    session[:path_to_temp_model] = path_to_temp_model

    puts session[:temp_model_basename]
    puts session[:path_to_temp_model]








    # ##### add nmrpred code #####
    # @nmrpred_input_mol_file = path_to_temp_model
    # @out_put_dir = session_directory
    # @mol_prefix_name = session_name
    # @train_file_with_path = Rails.root.join('backend', 'nmr-pred', 'NmrPred', 'get_descriptor', 'train_hmdb_onlybmrb_swapped_COH2_fixed_7point92_with_uncommon_consistentCH2_no_null.csv')
    # NmrPred.FeatureCreation(@nmrpred_input_mol_file,@out_put_dir,@mol_prefix_name)
    # @test_file_with_path = "#{@nmrpred_input_mol_file}".split(".mol")[0]+"_testfile.csv"
    # puts"@test_file_with_path = #{@test_file_with_path}"
    # NmrPred.PredictionShift(@train_file_with_path,@test_file_with_path,@out_put_dir,@mol_prefix_name)
    # puts"nmrpred H2O done"
    ############

    #session[:temp_model_basename] = temp_model_basename
    #session[:path_to_temp_model] = path_to_temp_model
    @page = "metadata"
    @chemical_shift_submission_meta_data = @chemical_shift_submission.chemical_shift_submission_meta_data
    #session[:temp_model_basename] = temp_model_basename
    #session[:path_to_temp_model] = path_to_temp_model
  end

  # form_for returns js requests which doesn't allow the html redirect
  def ajax_redirect_to(redirect_uri)
    { js: "window.location.replace('#{redirect_uri}');" }
  end

  def runDrawMol(smiles, session_directory, session_name)

    #
    # This takes the submitted smiles string and generates 3d mol files and images
    #

    puts "Running runDrawMol() function"
    puts "smiles = #{smiles}"
    puts "session_directory = #{session_directory}"

    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    draw_mol_script=Rails.root.join("backend", "nmr-pred","draw_mol.py")
    puts "draw_mol_script = #{draw_mol_script}"


    draw_mol_log_basename="#{session_name}_draw_mol.log"
    outputprefix="#{session_name}_draw_mol"
    draw_mol_log_abs_path=Rails.root.join("#{session_directory}",draw_mol_log_basename)

    puts "draw_mol_log_abs_path = #{draw_mol_log_abs_path}"

    draw_mol_command=""
    draw_mol_command+="#{python_path} "
    draw_mol_command+="#{draw_mol_script} "

    draw_mol_arguments=""
    draw_mol_arguments+="--smiles '#{smiles}' "
    draw_mol_arguments+="--outputpath '#{session_directory}' "
    draw_mol_arguments+="--writemol "
    draw_mol_arguments+="--optmol "
    #draw_mol_arguments+="--showstereo "
    #draw_mol_arguments+="--showequiv "
    draw_mol_arguments+="--smilesorder "

    draw_mol_arguments_non_canonical=draw_mol_arguments
    draw_mol_arguments_non_canonical+="--outputprefix '#{outputprefix}' "
    draw_mol_arguments_non_canonical+=" > '#{draw_mol_log_abs_path}' "

    draw_mol_command_non_canonical=draw_mol_command
    draw_mol_command_non_canonical+="#{draw_mol_arguments_non_canonical} "
    puts "draw_mol_command_non_canonical: #{draw_mol_command_non_canonical}"
    `#{draw_mol_command_non_canonical}`

    non_canonical_mol_abs_path=Rails.root.join("#{session_directory}","#{outputprefix}_output.mol")
    non_canonical_base_image_abs_path=Rails.root.join("#{session_directory}","#{outputprefix}_2d.png")
    non_canonical_equiv_image_abs_path=Rails.root.join("#{session_directory}","#{outputprefix}_equiv.png")

    puts "non_canonical_mol_abs_path: #{non_canonical_mol_abs_path}"
    puts "non_canonical_base_image_abs_path: #{non_canonical_base_image_abs_path}"
    puts "non_canonical_equiv_image_abs_path: #{non_canonical_equiv_image_abs_path}"

    return non_canonical_mol_abs_path, non_canonical_base_image_abs_path, non_canonical_equiv_image_abs_path
  end



  def run_nmrpred_intialprediction(input_mol_path, session_directory, session_name)

    #
    # This uses a 3d mol to make intial nmrshiftdb prediction with nmrpred
    # Returns full paths
    #

    puts "running nmrpred (nmrshiftdb) for intial prediction)"
    puts "input mol file: #{input_mol_path}"
    puts "output folder: #{session_directory}"
    puts "output prefix: #{session_name}"

    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    nmrpred_script=Rails.root.join("backend", "nmr-pred", "nmrpred.py")
    puts "nmrpred_script = #{nmrpred_script}"

    python_log_basename="#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path=Rails.root.join("#{session_directory}",python_log_basename)

    nmrpred_command="#{python_path} "
    nmrpred_command+="#{nmrpred_script} "

    nmrpred_command+="--mol #{input_mol_path} "
    nmrpred_command+="--outputpath #{session_directory} "
    nmrpred_command+="--outputprefix #{session_name} "
    nmrpred_command+="--writeassignmenttable "
    nmrpred_command+="--write1h  "
    nmrpred_command+="--write1hcoup "
    nmrpred_command+="--write13c "
    nmrpred_command+=">>  #{python_log_abs_path} "

    puts "nmrpred_command = #{nmrpred_command}"
    `#{nmrpred_command}`

    nmrpred_assignment=Rails.root.join("#{session_directory}","#{session_name}_assignmenttable.txt")
    puts "nmrpred_assignment = #{nmrpred_assignment}"

    #cs_dictionary=parse_nmrpred_assignment(nmrpred_assignment)
    return nmrpred_assignment

  end


  def make_files_from_drawmol(mol_file, image_file, session_directory, session_name)

    #
    # This takes nmrpred output assignmenttable.txt and makes mol.csv which is parsed into the website table
    # Appends to log file from run_nmrpred_intialprediction above
    # Also makes copies of mol and image file for the website use
    # returns file names WITHOUT full path
    #

    puts "copying files to 'temp' image and mol file"

    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path=Rails.root.join("public", "python", "npmrd_copy_drawmol_for_site.py")
    puts "script = #{script_path}"

    temp_model_basename="#{session_name}_temp_3D.mol"
    temp_image_basename="#{session_name}_temp_3D.png"

    output_mol_path = Rails.root.join("#{session_directory}", temp_model_basename)
    output_img_path = Rails.root.join("#{session_directory}", temp_image_basename)

    python_log_basename="#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path=Rails.root.join("#{session_directory}",python_log_basename)

    script_command = ""
    script_command += "#{python_path} "
    script_command += "#{script_path} "

    script_command += "-sid #{session_name} "
    script_command += "-odir_path #{session_directory} "

    script_command += "-input_mol #{mol_file} "
    script_command += "-input_img #{image_file} "

    script_command += "-mol_path #{output_mol_path} "
    script_command += "-mol_img_path #{output_img_path} "

    script_command += ">> #{python_log_abs_path} "

    puts "script_command = #{script_command}"
    `#{script_command}`

    return temp_model_basename, temp_image_basename
  end


  def run_npmrd_make_mol_csv(mol_file, table_file, session_directory, session_name)

    #
    # This takes nmrpred output assignmenttable.txt and makes mol.csv which is parsed into the website table
    # Appends to log file from run_nmrpred_intialprediction above
    # returns file names WITHOUT full path
    #

    puts "converting nmrpred file to mol.csv file"

    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path=Rails.root.join("public", "python", "npmrd_make_mol_csv.py")
    puts "script = #{script_path}"

    csv_basename="#{session_name}_mol.csv"
    output_csv_path = Rails.root.join("#{session_directory}", csv_basename)

    python_log_basename="#{session_name}_nmrpred_initial_predictions.log"
    python_log_abs_path=Rails.root.join("#{session_directory}",python_log_basename)

    script_command = ""
    script_command += "#{python_path} "
    script_command += "#{script_path} "

    script_command += "-sid #{session_name} "
    script_command += "-odir_path #{session_directory} "

    script_command += "-input_mol #{mol_file} "
    script_command += "-inputtable_path #{table_file} "

    script_command += "-cs_path #{output_csv_path} "

    script_command += ">> #{python_log_abs_path} "

    puts "script_command = #{script_command}"
    `#{script_command}`

    return csv_basename
  end

  def merge_shiftdb_ml_pred(shiftdbfile, mlfile)

    #should be full paths for files

    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path=Rails.root.join("public", "python", "merge_shiftdb_java_csv.py")
    puts "script = #{script_path}"

    script_command = "#{python_path} #{script_path} #{shiftdbfile} #{mlfile} #{outputfile}"
    puts "running script_command = #{script_command}"
    `#{script_command}`

  end



  def remap_nmrpred_peaklist_file(peaklist, mappingfile)

    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path=Rails.root.join("public", "python", "npmrd_remap_peaklist.py")
    puts "script = #{script_path}"

    script_command = "#{python_path} #{script_path} #{peaklist} #{mappingfile}"
    puts "running script_command = #{script_command}"
    `#{script_command}`

    return "#{peaklist}.remapped"
  end

############## Method used by the create action finished #####################
###########################################################

  def runNMRpred()
    # output from nmrpred --writeassignmenttable will generally have
    # extra atoms especially if there are unassigned couplings

    # default options and files:
    # --pred1hcsv         'results/example_1h_shifts.txt'
    # --pred1hcoupcsv     'results/example_1h_couplings.txt'
    # --userinput         'results/example_assignmenttable_manual.txt'
    # --write1hshift      'results/example_parsed1hshifts.txt'
    # --write1hcoup       'results/example_parsed1hcoup.txt'

    # for simulation, after running script:
    # nmrpred.py --smiles CCO --noprediction --input1h results/example_parsed1hshifts.txt --input1hcoup results/example_parsed1hcoup.txt --plot1h

    puts "Running function runNMRpred()"
    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    backend_dir="backend"
    nmr_pred_dir="nmr-pred"
    parseuserassignment_script_basename="parseuserassignment.py"
    nmrpred_script_basename="nmrpred.py"

    #smiles=NaturalProduct.find_by(np_mrd_id: session[:natural_product_np_mrd_id]).moldb_smiles


    session_name=session[:chemical_shift_submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{@current_user.id}"
    # session_id=session[:session_id]
    session_id=session.id
    spectrum_type=session[:spectrum_type]
    parseuserassignment_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{parseuserassignment_script_basename}")
    nmrpred_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{nmrpred_script_basename}")
    nmrpred_dir=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}")


    session_directory=Rails.root.join('public','downloads',"#{session_id}")

    inputmol_basename = predicted_shifts_basename="#{session_name}_output.mol"
    inputmol_path=Rails.root.join("#{session_directory}","#{inputmol_basename}")

    predicted_shifts_basename="#{session_name}_1h_shifts.txt"
    predicted_13c_shifts_basename="#{session_name}_13c_shifts.txt"
    predicted_couplings_basename="#{session_name}_1h_couplings.txt"
    assignmenttable_manual_basename="#{session_name}_assignmenttable.txt"
    parsed_shifts_basename="#{session_name}_parsed_1h_shifts.txt"
    parsed_13c_shifts_basename="#{session_name}_parsed_13c_shifts.txt"
    parsed_couplings_basename="#{session_name}_parsed_1h_couplings.txt"
    parseuserassignment_log_basename="#{session_name}_parseuserassignment.log"
    user_assignmenttable_basename="#{session_name}_user_assignmenttable.txt"
    nmrpred_log_basename="#{session_name}_nmrepred.log"

    predicted_shifts_path=Rails.root.join("#{session_directory}","#{predicted_shifts_basename}")
    predicted_13c_shifts_path=Rails.root.join("#{session_directory}","#{predicted_13c_shifts_basename}")
    predicted_couplings_path=Rails.root.join("#{session_directory}","#{predicted_couplings_basename}")
    assignmenttable_manual_path=Rails.root.join("#{session_directory}","#{assignmenttable_manual_basename}")
    parsed_shifts_path=Rails.root.join("#{session_directory}","#{parsed_shifts_basename}")
    parsed_13c_shifts_path=Rails.root.join("#{session_directory}","#{parsed_13c_shifts_basename}")
    parsed_couplings_path=Rails.root.join("#{session_directory}","#{parsed_couplings_basename}")
    parseuserassignment_log_path=Rails.root.join("#{session_directory}","#{parseuserassignment_log_basename}")
    user_assignmenttable_path=Rails.root.join("#{session_directory}","#{user_assignmenttable_basename}")
    nmrpred_log_path=Rails.root.join("#{session_directory}","#{nmrpred_log_basename}")

    puts "Creating session_directory = #{session_directory}"
    puts "parseuserassignment_script = #{parseuserassignment_script}"
    puts "session_directory = ",session_directory
    puts "backend_dir= ",backend_dir
    puts "nmr_pred_dir = ",nmr_pred_dir
    puts "parseuserassignment_script_basename = ",parseuserassignment_script_basename
    puts "predicted_shifts_path = ",predicted_shifts_path
    puts "predicted_13c_shifts_path = ",predicted_13c_shifts_path
    puts "predicted_couplings_path = ",predicted_couplings_path
    puts "assignmenttable_manual_path = ",assignmenttable_manual_path
    puts "parsed_shifts_path = ",parsed_shifts_path
    puts "parsed_13c_shifts_path = ",parsed_13c_shifts_path
    puts "parsed_couplings_path = ",parsed_couplings_path
    puts "parseuserassignment_log_path = ",parseuserassignment_log_path
    puts "user_assignmenttable_path = ",user_assignmenttable_path
    puts "nmrpred_script = ",nmrpred_script
    #puts "input_smiles =  #{smiles}"
    puts "inputmol_path =  #{inputmol_path}"
    puts "spectrum_type =  #{spectrum_type}"


    user_input_command=""
    user_input_command+="#{python_path} "
    user_input_command+="#{parseuserassignment_script} "

    user_input_arguments=""
    user_input_arguments+="--userinput %s " % user_assignmenttable_path
    user_input_arguments+="--predtable %s " % assignmenttable_manual_path

    user_input_arguments+="--pred1hcsv %s " % predicted_shifts_path
    user_input_arguments+="--pred1hcoupcsv %s " % predicted_couplings_path
    user_input_arguments+="--write1hshift %s " % parsed_shifts_path
    user_input_arguments+="--write1hcoup %s " % parsed_couplings_path

    if spectrum_type.include? "13C"
       user_input_arguments+="--pred13ccsv %s " % predicted_13c_shifts_path
       user_input_arguments+="--write13cshift %s " % parsed_13c_shifts_path
     end

    user_input_arguments+=" > %s " % parseuserassignment_log_path

    user_input_command+=user_input_arguments

    ChemicalShiftSubmission.save_user_table(@chemical_shift_submission,file_path=user_assignmenttable_path)

    puts "user_input_command : #{user_input_command}"
    `#{user_input_command}`

    # nmrpred_prefix="nmrpred"
    nmrpred_prefix = "#{session_name}"

    nmrpred_command=""
    nmrpred_command+="#{python_path} "
    nmrpred_command+="#{nmrpred_script} "
    nmrpred_arguments=""
    #nmrpred_arguments+=" --smiles '#{smiles}' "
    nmrpred_arguments+=" --mol %s "  % inputmol_path
    nmrpred_arguments+=" --noprediction "

    spectrometer_frequency=session[:spectrometer_frequency]
    nmrpred_arguments+=" --sfrq #{spectrometer_frequency}"


    nmrpred_arguments+=" --input1h %s "  % parsed_shifts_path
    nmrpred_arguments+=" --input1hcoup %s "  % parsed_couplings_path
    nmrpred_arguments+=" --inputassignfile %s "  % user_assignmenttable_path
    nmrpred_arguments+=" --plot1h "
    #nmrpred_arguments+=" --mergepeaks "

    if spectrum_type.include? "13C"
      nmrpred_arguments+=" --input13c %s "  % parsed_13c_shifts_path
      nmrpred_arguments+=" --plot13c "
    end

    #if spectrum_type==="2D-1H-13C"
    #  nmrpred_arguments+=" --mergepeaks "
    #end
    nmrpred_arguments+=" --outputpath %s "  % session_directory
    nmrpred_arguments+=" --outputprefix %s "  % nmrpred_prefix
    nmrpred_arguments+=" > %s " % nmrpred_log_path

    working_directory=Dir.pwd
    puts "working_directory : #{working_directory}"
    Dir.chdir(nmrpred_dir)
    puts "Changed to NMRpred directory: #{nmrpred_dir}"

    nmrpred_command+=nmrpred_arguments

    puts "nmrpred_ command : #{nmrpred_command}"
    `#{nmrpred_command}`

    puts "Changed to back to working directory: #{working_directory}"
    Dir.chdir(working_directory)


  end

  def generate_nmrml()
    puts "Running function generate_nmrml()"
    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    backend_dir="backend"
    nmr_pred_dir="nmr_ml"
    nmrml_creator_script_basename="nmrml_creator.py"
    session_name=session[:chemical_shift_submission_id].to_s + "_#{session[:natural_product_np_mrd_id]}_" + "#{session[:user_id]}"
    # session_id=session[:session_id]
    session_id=session.id
    nmrml_creator_script=Rails.root.join("#{backend_dir}", "#{nmr_pred_dir}","#{nmrml_creator_script_basename}")
    session_directory=Rails.root.join('public','downloads',"#{session_id}")
    natural_product_name=session[:natural_product_name]
    #New arguments, August 12th 2020:
    genus=session[:genus]
    species=session[:species]
    literature_reference=session[:literature_reference]
    solvent=session[:solvent]
    spectrum_type=session[:spectrum_type]
    spectrometer_frequency=session[:spectrometer_frequency]
    temperature=session[:temperature]
    chemical_shift_standard=session[:chemical_shift_standard]
    literature_reference_type=session[:literature_reference_type]
    physical_state_of_compound=session[:physical_state_of_compound]
    melting_point=session[:melting_point]
    boiling_point=session[:boiling_point]

    puts "genus=  #{genus}"
    puts "natural_product_name=  #{natural_product_name} "
    puts "species=  #{species} "
    puts "literature_reference= #{literature_reference} "
    puts "solvent= #{solvent} "
    puts "session_id = #{session_id}"
    puts "spectrum_type = #{spectrum_type}"
    puts "spectrometer_frequency = #{spectrometer_frequency}"
    puts "temperature = #{temperature}"
    puts "chemical_shift_standard = #{chemical_shift_standard}"
    puts "literature_reference_type = #{literature_reference_type}"
    puts "physical_state_of_compound = #{physical_state_of_compound}"
    puts "melting_point = #{melting_point}"
    puts "boiling_point = #{boiling_point}"


    nmrpred_1h_peaklist_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_peaklist.txt")
    nmrpred_output_mol_abs_path=Rails.root.join("#{session_directory}","#{session_name}_output.mol")
    nmrml_creator_log_abs_path=Rails.root.join("#{session_directory}","#{session_name}_creator.log")
    # New NMRpred inputs August 12th 2020
    nmrpred_param_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_params.txt")
    nmrpred_fid_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_fid.txt")
    nmrpred_spectrum_abs_path=Rails.root.join("#{session_directory}","#{session_name}_1h_spectrum.txt")
    nmrpred_13c_peaklist_abs_path=Rails.root.join("#{session_directory}","#{session_name}_13c_peaklist.txt")
    nmrpred_13c_param_abs_path=Rails.root.join("#{session_directory}","#{session_name}_13c_params.txt")
    nmrpred_13c_fid_abs_path=Rails.root.join("#{session_directory}","#{session_name}_13c_fid.txt")
    nmrpred_13c_spectrum_abs_path=Rails.root.join("#{session_directory}","#{session_name}_c13_spectrum.txt")

    nmrml_basename="#{session_name}.nmrML"
    nmrml_url="public/downloads/#{session_name}.nmrML"
    nmrml_abs_path=Rails.root.join("#{session_directory}","#{nmrml_basename}")

    session[:nmrml_basename] = nmrml_basename
    session[:nmrml_url] = nmrml_url
    session[:nmrml_abs_path] = nmrml_abs_path

    puts "nmrml_creator_script = #{nmrml_creator_script}"
    puts "session_directory = #{session_directory}"
    puts "natural_product_name = #{natural_product_name}"
    puts "nmrml_basename = #{nmrml_basename}"
    puts "nmrml_abs_path = #{nmrml_abs_path}"

    nmrml_command=""
    nmrml_command+="#{python_path} "
    nmrml_command+="#{nmrml_creator_script} "
    nmrml_arguments=""
    nmrml_arguments+=" -mol #{nmrpred_output_mol_abs_path} "
    nmrml_arguments+=" -pl #{nmrpred_1h_peaklist_abs_path} "
    nmrml_arguments+=" -output_path #{nmrml_abs_path} "
	   nmrml_arguments+=' -name "%s" ' % natural_product_name
    #New arguments, August 12th 2020
    nmrml_arguments+=" -genus '#{genus}' "
    nmrml_arguments+=" -solvent '#{solvent}' "
    nmrml_arguments+=" -species '#{species}' "
    nmrml_arguments+=" -freq #{spectrometer_frequency} "
    nmrml_arguments+=" -ref '#{literature_reference}' "
    nmrml_arguments+=" -standard '#{chemical_shift_standard}' "
    nmrml_arguments+=" -temp #{temperature} "
    nmrml_arguments+=" -spec_type '#{spectrum_type}' "
    nmrml_arguments+=" -param_path #{nmrpred_param_abs_path} "
    nmrml_arguments+=" -fid_path #{nmrpred_fid_abs_path} "
    nmrml_arguments+=" -spec_path #{nmrpred_spectrum_abs_path} "

    nmrml_arguments+=" -13C_pl #{nmrpred_13c_peaklist_abs_path} "
    nmrml_arguments+=" -13C_param_path #{nmrpred_13c_param_abs_path} "
    nmrml_arguments+=" -13C_fid_path #{nmrpred_13c_fid_abs_path} "
    nmrml_arguments+=" -13C_spec_path #{nmrpred_13c_spectrum_abs_path} "

    nmrml_arguments+=" -ref_type '#{literature_reference_type}' "
    nmrml_arguments+=" -phys_state '#{physical_state_of_compound}' "
    nmrml_arguments+=" -melt_point '#{melting_point}' "
    nmrml_arguments+=" -boil_point '#{boiling_point}' "

    nmrml_arguments+=" > #{nmrml_creator_log_abs_path} "
    nmrml_command+=nmrml_arguments

    puts "nmrml_command = #{nmrml_command}"
    `#{nmrml_command}`

    #puts session.to_hash
  end

  def download_nmrml()
    load_chemical_shift_submission_object_by_user
    nmrml_basename=session[:nmrml_basename]
    nmrml_url=session[:nmrml_url]
    #nmrml_abs_path=session[:nmrml_abs_path]["path"]
    # session_id=session[:session_id]
    session_id=session.id
    puts "nmrml_basename = #{nmrml_basename}"
    puts "nmrml_abs_path = #{nmrml_abs_path}"
    puts "nmrml_url = #{nmrml_url}"
    puts "session_id = #{session_id}"
    file_name=nmrml_basename

    nmrml_abs_path = Rails.root.join("public", "downloads", "#{session_id}", "#{nmrml_basename}")
    #file_name = ChemicalShiftSubmission.Download_file(@chemical_shift_submission)
    send_file "#{nmrml_abs_path.to_s}", type: "application/csv", filename: "#{file_name}", disposition: 'attachment'
  end

  def download_nmrml_from_outside
    load_chemical_shift_submission_object_without_user_session
    file_name = "#{@chemical_shift_submission.id}_#{@natural_product.np_mrd_id}_#{@chemical_shift_submission.user_id}.nmrML"
    nmrml_abs_path = Rails.root.join("public", "downloads", "#{@chemical_shift_submission.user_session_id}","#{file_name}").to_s
    send_file "#{nmrml_abs_path}", type: "application/csv", filename: "#{file_name}", disposition: 'attachment'
  end

  def download_spectra_image_from_outside
    load_chemical_shift_submission_object_without_user_session
    file_name = "#{@chemical_shift_submission.id}_#{@natural_product.np_mrd_id}_#{@chemical_shift_submission.user_id}_1h_1d.png"
    image_abs_path = Rails.root.join("public", "downloads", "#{@chemical_shift_submission.user_session_id}","#{file_name}").to_s
    send_file "#{image_abs_path}", type: "application/png", filename: "#{file_name}", disposition: 'attachment'
  end


  def make_jcamp()

    puts "making jcamp file..."

    npid = session[:natural_product_np_mrd_id]
    subid = session[:chemical_shift_submission_id].to_s
    userid = session[:user_id]

    session_id=session.id
    session_name="#{subid}_#{npid}_#{userid}"

    python_path=PYTHON_ENV["#{Rails.env}"]['python_path']
    script_path=Rails.root.join("backend", "jcampdx","createjcamp.py")
    session_directory=Rails.root.join('public','downloads',"#{session_id}")

    natural_product_name=session[:natural_product_name]
    literature_reference=session[:literature_reference]
    literature_reference_type=session[:literature_reference_type]

    solvent=session[:solvent]
    temperature=session[:temperature]
    if temperature.blank?
      temperature="NA"
    end
    chemical_shift_standard=session[:chemical_shift_standard]

    spectrum_type=session[:spectrum_type]
    spectrometer_frequency=session[:spectrometer_frequency]

    molfile=Rails.root.join("#{session_directory}","#{session_name}_output.mol")

    ownerstr = " --origin NP-MRD --owner NP-MRD "

    jcampcmd = "#{python_path} #{script_path} --title \"#{npid}, Submission ID #{subid}, Simulated spectrum\" #{ownerstr}  "
    jcampcmd += "--block STRUCTURE --file #{molfile} --title \"#{natural_product_name}\"  #{ownerstr}  "

    #cover 1H, 13C, and 1H-13C
    if "#{spectrum_type}".include? "1H"
      nuc = "1H"
      frq = spectrometer_frequency
      condstr = " --temp #{temperature} --sol \"#{solvent}\" --refcpd \"#{chemical_shift_standard}\" --frq #{frq} --nuc #{nuc} "
      spec=Rails.root.join("#{session_directory}","#{session_name}_1h_spectrum.txt")
      peaks=Rails.root.join("#{session_directory}","#{session_name}_1h_peaklist.txt")
      usertbl=Rails.root.join("#{session_directory}","#{session_name}_user_assignmenttable.txt")
      jcampcmd += "--block 1DSPECTRUM --file #{spec} --title \"Simulated spectrum\" #{ownerstr} #{condstr}  "
      jcampcmd += "--block 1DPEAKS --file #{peaks} --title \"Simulated peaks\" #{ownerstr} #{condstr} "
      jcampcmd += "--block ASSIGNMENTS --file #{peaks} --jassigntbl #{usertbl} --title \"Assignments\" --ref \"#{literature_reference_type} #{literature_reference}\" --link 1 #{ownerstr} #{condstr} "
    end

    if "#{spectrum_type}".include? "13C"
      nuc = "13C"
      frq = (spectrometer_frequency.to_f / 4).to_s
      condstr = " --temp #{temperature} --sol \"#{solvent}\" --refcpd \"#{chemical_shift_standard}\" --frq #{frq} --nuc #{nuc} "
      spec=Rails.root.join("#{session_directory}","#{session_name}_c13_spectrum.txt")
      peaks=Rails.root.join("#{session_directory}","#{session_name}_13c_peaklist.txt")
      jcampcmd += "--block 1DSPECTRUM --file #{spec} --title \"Simulated spectrum\"  #{ownerstr} #{condstr} "
      jcampcmd += "--block 1DPEAKS --file #{peaks} --title \"Simulated peaks\"  #{ownerstr} #{condstr} "
      jcampcmd += "--block ASSIGNMENTS --file #{peaks} --title \"Assignments\" --ref \"#{literature_reference_type} #{literature_reference}\" --link 1  #{ownerstr} #{condstr} "
    end

    outputfile = "#{session_name}.jdx"
    outputfile_path=Rails.root.join("#{session_directory}","#{outputfile}")
    
    puts "#{jcampcmd}"
    `#{jcampcmd} > #{outputfile_path}`

  end


  def download_jcamp
    load_chemical_shift_submission_object_without_user_session
    file_name = "#{@chemical_shift_submission.id}_#{@natural_product.np_mrd_id}_#{@chemical_shift_submission.user_id}.jdx"
    file_path = Rails.root.join("public", "downloads", "#{@chemical_shift_submission.user_session_id}","#{file_name}").to_s
    send_file "#{file_path}", type: "text/plain", filename: "#{file_name}", disposition: 'attachment'

    #session_id=session.id
    #npid = session[:natural_product_np_mrd_id]
    #subid = session[:chemical_shift_submission_id].to_s
    #userid = session[:user_id]
    #session_directory=Rails.root.join('public','downloads',"#{session_id}")
    #session_name="#{subid}_#{npid}_#{userid}"
    #outputfile = "#{session_name}.jdx"
    #outputfile_path=Rails.root.join("#{session_directory}","#{outputfile}")
    #download_name = "#{subid}_#{npid}.jdx"
    #send_file "#{outputfile_path.to_s}", type: "chemical/x-jcamp-dx", filename: "#{download_name}", disposition: 'attachment'
  end

############## Method used by the update  action starts #####################
  def preparation_for_bulding_shift_table(session_mol_csv,session_chemical_shift_submission_id,spectrum_type)
    ##### take the atoms and symbol for the submitted molecule
    @atom_symbol = ChemicalShiftSubmission.ReadAtoms(session_mol_csv) # it is a two dimentional array [[atom1,symbol1],[atom2,symbol2]]
    @chemical_shifts = Array.new
    if ChemicalShift.exists?(chemical_shift_submission_id: session_chemical_shift_submission_id)
      @chemical_shifts_relations = ChemicalShift.where(chemical_shift_submission_id: session_chemical_shift_submission_id)
      @chemical_shifts_relations.each do |c|
        if spectrum_type == "1D-1H" or spectrum_type == "1D-1H-DEPT90" or spectrum_type =="2D-1H-1H-COSY" or spectrum_type == "2D-1H-1H-TOCSY" or spectrum_type == "2D-1H-1H-ROESY"
          if c.atom_symbol == "C"
            next
          else
            @chemical_shifts.push(c)
          end
        elsif spectrum_type == "1D-13C" or spectrum_type == "1D-13C-DEPT90" or spectrum_type == "2D-13C-13C-COSY" or spectrum_type == "2D-13C-13C-INADEQUATE"
          if c.atom_symbol == "H"
            next
          else
            @chemical_shifts.push(c)
          end
        elsif spectrum_type == "2D-1H-13C" or spectrum_type=="2D-1H/13C"
          @chemical_shifts.push(c)
        end
      end
      @chemical_shift_submission = ChemicalShiftSubmission.find(session_chemical_shift_submission_id)
    else
      @atom_symbol.each do |atom|
        if spectrum_type == "1D-1H" or spectrum_type == "1D-1H-DEPT90" or spectrum_type == "2D-1H-1H-COSY" or spectrum_type == "2D-1H-1H-TOCSY" or spectrum_type == "2D-1H-1H-ROESY"
          if atom[1] == "C"
            next
          else
            c = ChemicalShift.new(atom_id: atom[0], atom_symbol: atom[1], chemical_shift_pred: atom[2], multiplet_pred: atom[3], jcoupling_pred: atom[4])
            @chemical_shifts.push(c)
          end
        elsif spectrum_type == "1D-13C" or spectrum_type == "1D-13C-DEPT90" or spectrum_type == "2D-13C-13C-COSY" or spectrum_type == "2D-13C-13C-INADEQUATE"
          if atom[1] == "H"
            next
          else
            c = ChemicalShift.new(atom_id: atom[0], atom_symbol: atom[1], chemical_shift_pred: atom[2], multiplet_pred: atom[3], jcoupling_pred: atom[4])
            @chemical_shifts.push(c)
          end
        elsif spectrum_type == "2D-1H-13C" or spectrum_type=="2D-1H/13C"
          c = ChemicalShift.new(atom_id: atom[0], atom_symbol: atom[1], chemical_shift_pred: atom[2], multiplet_pred: atom[3], jcoupling_pred: atom[4])
          @chemical_shifts.push(c)
        end

      end
      @chemical_shift_submission = ChemicalShiftSubmission.find(session_chemical_shift_submission_id)
    end
  end
############# Method to create assignment score plot ############

  def calc_assignment_score
    cm_score = 0
    cm_i     = 0
    @chemical_shifts.each do |cs|
      test_list = ['NA',"",nil]
      if test_list.include? cs.chemical_shift_true
        cs.chemical_shift_true = "NA"
      end
      if test_list.include? cs.multiplet_true
        cs.multiplet_true = "NA"
      end
      if test_list.include? cs.jcoupling_true
        cs.jcoupling_true = "NA"
      end
      # if test_list.include? cs.assigned_peaks
      #   cs.assigned_peaks = "NA"
      # end

      if [cs.chemical_shift_true,cs.multiplet_true,cs.jcoupling_true].all? { |e| e != 'NA' }
        cs.assignment_level = "Level-1"
        cs.assignment_score = 100.0.to_s
      elsif [cs.chemical_shift_true,cs.multiplet_true].all? { |e| e != 'NA' } and cs.jcoupling_true == "NA"
        if cs.multiplet_true == 's' or cs.multiplet_true == 'm'
          cs.assignment_level = "Level-1"
          cs.assignment_score = 100.0
        else
          cs.assignment_level = "Level-2"
          cs.assignment_score = 70.0
        end
      elsif cs.chemical_shift_true != 'NA' and cs.jcoupling_true == "NA" and cs.multiplet_true == "NA"
        cs.assignment_level = "Level-3"
        cs.assignment_score = 50.0
      # elsif cs.chemical_shift_true == 'NA'  and cs.assigned_peaks != "NA"
      #   cs.assignment_level = "level-4"
      #   cs.assignment_score = 25.0
      elsif cs.chemical_shift_true== 'NA'
        cs.assignment_level = "Level-4"
        cs.assignment_score = 0.0
      else
        cs.assignment_level = "NA"
        cs.assignment_score = 'NA'
      end
      cm_score = cm_score+cs.assignment_score.to_f
      cm_i     = cm_i+1

    end
    # pub_dir     = Rails.root.join("public")
    script_dir  = Rails.root.join("backend","magmet")
    python_path = PYTHON_ENV["#{Rails.env}"]['python_path']
    script      = Rails.root.join("backend","magmet/assignment_score_plot.py")

    session_name = session[:session_name]
    score_image_name = "#{session_name}_assignment_score.svg"
    score_image_path = Rails.root.join("public","downloads","#{session.id}",score_image_name)
    final_score = (cm_score/cm_i).round(2)
    assignment_plot = "#{python_path} #{script} -s #{final_score} -f #{score_image_path}"
    `#{assignment_plot}`
    session[:score_image_path] = score_image_path
  end

  def show_assignment_report
    @score_rules = score_rules=[[1,"Yes","Yes","Yes","Level-1","100%"],
                                [2,"Yes","Yes","No","Level-2","75%"],
                                [3,"Yes","No","No","Level-3","50%"],
                                [4,"No","No","No","Level-4","0%"]]

    load_chemical_shift_submission_object_by_user
    session_name = session[:session_name]
    session_id   = session.id
    assignment_report_file =  "#{session_name}_assignment_report.txt"
    @assignment_report_file_path = File.join("/downloads","#{session_id}",assignment_report_file)

    score_image_name = "#{session_name}_assignment_score.svg"
    @score_image_path = File.join("/downloads","#{session.id}",score_image_name)

    @chemical_shifts = @chemical_shift_submission.chemical_shifts

    @custom_atom_numbers = get_custom_atom_numbers @chemical_shift_submission

    render "shared/_assignment_report", locals: {file_path: download_assignment_report_chemical_shift_submission_path}

  end

  def download_assignment_report
      load_chemical_shift_submission_object_by_user
      session_name = session[:session_name]
      session_id   = session.id
      assignment_report_file =  "#{session_name}_assignment_report.txt"
      assignment_report_file_path = Rails.root.join("public","downloads","#{session_id}",assignment_report_file)
      # file_name = ChemicalShiftSubmission.Download_file(@chemical_shift_submission)
      send_file "#{assignment_report_file_path.to_s}", type: "application/csv", filename: "#{assignment_report_file}", disposition: 'attachment'
    end


############## Method used by the update  action starts #####################

  def chemical_shift_submission_from_session
    {
      "chemical_shift_submission_meta_data_attributes" => {
        "provenance" => session[:provenance],
        "genus" => session[:genus],
        "species" => session[:species],
        "physical_state_of_compound" => session[:physical_state_of_compound],
        "melting_point" => session[:melting_point],
        "boiling_point" => session[:boiling_point],
        "literature_reference" => session[:literature_reference],
        "literature_reference_type" => session[:literature_reference_type]
      }
    }
  end

  def score_rules
    @score_rules = score_rules=[[1,"Yes","Yes","Yes","Level-1","100%"],
                              [2,"Yes","Yes","No","Level-2","75%"],
                              [3,"Yes","No","No","Level-3","50%"],
                              [4,"No","No","No","Level-4","0%"]]
    render "shared/_assignment_report_popup"

  end

  def get_custom_atom_numbers(chemical_shift_submission)
    custom_atom_numbers = []
    if CustomAtomNumber.exists?(chemical_shift_submission_id: chemical_shift_submission.id)
      custom_atom_number_relations = CustomAtomNumber.where(chemical_shift_submission_id: chemical_shift_submission.id)
      custom_atom_number_relations.each do |c|
        custom_atom_numbers.push(c)
      end
    end
    custom_atom_numbers
  end

  def download_peak_list
    @chemical_shift_submission = ChemicalShiftSubmission.find(params[:id])
    location = Rails.root.join('public', 'downloads') 
    @chemical_shift_submission.download_user_assignment_table(location)
    filename = "#{@chemical_shift_submission.natural_product.np_mrd_id}_#{@chemical_shift_submission.id}_peak_list.csv"
    file_path = File.join(location, filename)

    if File.exist?(file_path)
      send_file file_path, type: 'text/csv', disposition: 'attachment', filename: filename
    else
      redirect_to @chemical_shift_submission, alert: 'No data available for download.'
    end
  end

end

