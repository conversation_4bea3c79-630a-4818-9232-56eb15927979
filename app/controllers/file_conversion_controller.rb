# Filename: file_conversion_controller.rb
# Description: Able to convert a series of files to a different form using python
#
# Public Classes:
#   - MixtureSubmissionController
#
# Author: <PERSON>   Creation Date: 2022/06/14
# Changes:
require('zip')

class FileConversionController < ApplicationController

    def new
    end

    def create

        file_name = "";
        Dir.mktmpdir do |dir|
            file = params[:upload_file]

            case params[:conversion_type]
            when '1D Spectrum (bruker) to NMRML'
                #unzip data to get at files (magmet will re-unzip later)
                #zip file should contain one top level folder with data inside
                `mv #{file.path} #{dir}`
                newpath = "#{dir}/#{File.basename(file.path)}"
                `unzip #{newpath} -d #{dir}`

                #get parameters
                magmetpath = Rails.root.join('backend', 'magmet').to_s + "/"
                sfrq =  `#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'magmet', 'find_spec_freq.py')} #{newpath}`
                solv =  `#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'magmet', 'find_solvent.py')}   #{newpath}`
                sptype = "1D"
                ref = "NA"
                sfrq = sfrq.strip
                solv = solv.strip
                sptype = sptype.strip
                ref = ref.strip

                #run magmet
                magmetcmd = "#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'magmet', 'processing_1d.py')} -m #{magmetpath} -i #{newpath} -f #{sfrq} -sol #{solv} -sptype #{sptype} -ref #{ref}"
                `#{magmetcmd}`

                #json output
                jsonout = "#{dir}/" + "#{sptype}_#{solv}_#{ref}_#{sfrq}.json"
                nuc = `#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'magmet', 'find_nuc_json.py')} #{jsonout}`

                #run nmrml
                nmrmlcmd = "#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'nmr_ml', 'nmrml_creator.py')} -freq #{sfrq} -solvent #{solv} -standard #{ref} -output_path #{dir}/output "

                if nuc.include? "13C"
                    nmrmlcmd += " -spec_type 1D-13C -json_13c_path #{jsonout}"
                    file_name = "1D-13C.nmrML"
                else
                    nmrmlcmd += " -spec_type 1D-1H -json_1h_path #{jsonout}"
                    file_name = "1D-1H.nmrML"
                end
                `#{nmrmlcmd}`

            when '2D Spectrum (processed, bruker/varian) to NMRML'

                #unzip data to get at files (magmet will re-unzip later)
                #zip file should contain one top level folder with data inside
                `mv #{file.path} #{dir}`
                newpath = "#{dir}/#{File.basename(file.path)}"
                `unzip #{newpath} -d #{dir}`

                #get parameters
                magmetpath = Rails.root.join('backend', 'magmet').to_s + "/"
                sfrq =  `#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'magmet', 'find_spec_freq.py')} #{newpath}`
                solv =  `#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'magmet', 'find_solvent.py')} #{newpath}`
                sptype = "2D"
                ref = "NA"
                sfrq = sfrq.strip
                solv = solv.strip
                sptype = sptype.strip
                ref = ref.strip

                #run magmet
                magmetcmd = "#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'magmet', 'processing_2d.py')} -m #{magmetpath} -i #{newpath} -f #{sfrq} -sol #{solv} -sptype #{sptype} -ref #{ref}"
                `#{magmetcmd}`

                #json output
                jsonout = "#{dir}/" + "#{sptype}_#{solv}_#{ref}_#{sfrq}.json"
                nuc = `#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'magmet', 'find_nuc_json.py')} #{jsonout}`
                if nuc.include? "13C"
                    nmrmlspectype = sptype + "-1H-13C-HSQC"
                elsif nuc.include? "15N"
                    nmrmlspectype = sptype + "-1H-15N-HSQC"
                else
                    nmrmlspectype = sptype + "-1H-1H-TOCSY"
                end

                #run nmrml
                nmrmlcmd = "#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'nmr_ml', 'nmrml_creator.py')} -freq #{sfrq} -solvent #{solv} -standard #{ref} -output_path #{dir}/output "
                nmrmlcmd += " -spec_type #{nmrmlspectype} -json_2d_path #{jsonout}"

                `#{nmrmlcmd}`
                file_name = "#{nmrmlspectype}.nmrML"

            when 'NMRML to JCAMP'
                file_name = "output_jcamp.jdx"
                `#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'jcampdx', 'createjcamp.py')} --fromnmrml #{file.path} > #{dir}/#{file_name}`

            when 'NMRML to NMREDATA'
                file_name = "output_nmredata.zip"
                cmd = "#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'nmredata', 'nmrml_to_nmredata.py')} --input #{file.path} --tmp #{dir} --output #{dir}/#{file_name}"
                `#{cmd}`

            when 'NMREDATA to NMRML'
                file_name = "output_nmrml.zip"
                cmd = "#{PYTHON_ENV[Rails.env.to_s]['python_path']} #{Rails.root.join('backend', 'nmredata', 'nmredata_zip_to_nmrml.py')} #{file.path} #{dir} #{dir} output"
                `#{cmd}`
                #zip up the nmrml files
                `zip #{dir}/#{file_name} #{dir}/*.nmrML`
            end
            data = File.read("#{dir}/#{file_name}")
            #send_data(data, :filename => file_name)
            send_data(data, filename: file_name, disposition: 'attachment', type: 'application/octet-stream')
        end
    end

    def update
    end

    # def show
    #     Rails.logger.debug(File.join(Rails.root, "public", "downloads", "example.nmrML"))
    #     send_file(File.join(Rails.root, "public", "downloads", "example.nmrML"))
    # end

    def example_file
        file_param = params[:file]
        puts params
        puts file_param
        file_path = case file_param
                    when 'nmrml'
                      Rails.root.join('public', 'downloads', 'example.nmrML')
                    when 'nmredata'
                      Rails.root.join('public', 'downloads', 'example_nmredata.zip')
                    when 'oned'
                      Rails.root.join('public', 'downloads', 'example_1d_bruker.zip')
                    end

        content_type = case File.extname(file_path)
                       when '.nmrML'
                         'application/nmrml'
                       when '.zip'
                         'application/zip'
                       else
                         'application/octet-stream'
                       end

        send_file file_path, type: content_type, disposition: 'attachment'
    end
end

