class ApplicationController < ActionController::Base
	include Wishart::SortParamsParser

	helper Unearth::ExtractableHelper
  helper Unearth::FilterableHelper
  helper Wishart::Engine.helpers
  helper Moldbi::StructureResourcesHelper
  helper Moldbi::Marvin<PERSON><PERSON>per
  helper Moldbi::MarvinJsHelper
  helper Specdb::RoutesHelper
  helper Specdb::MainAppHelper
  helper TmicBanner::Engine.helpers
  helper CiteThis::CitationHelper

  protect_from_forgery

  rescue_from ActiveRecord::RecordNotFound, with: :render_404

  # Render 404: Not Found
  def render_404(exception = nil)
    logger.info( clean_backtrace(exception, "Rendering 404 due to exception") )
    if /(jpe?g|png|gif)/i === request.path
      # Images should simply return the error as text
      return render(text: "404 Not Found", status: 404 )
    end
    render_error(404, "Resource Not Found")
  end

  # Authenticates the request. Retrieves the token from the 'Authorization' header,
  # decodes the JWT and finds the user with the user ID in the decoded JWT.
  # If the JWT is invalid or the user is not found, it renders a JSON response with an error message.
  def authenticate_request!
    token = request.headers['Authorization'].split(' ')[1]
    decoded_jwt, _header = decode_jwt(token)
    @current_user = User.find(decoded_jwt['user_id'])
  rescue JWT::DecodeError
    render json: { error: 'Invalid token' }, status: :unauthorized
  rescue ActiveRecord::RecordNotFound
    render json: { error: 'User not found' }, status: :not_found
  end

  def authenticate_admin_user!
    unless current_user && current_user.admin
      redirect_to(root_path, :notice => "You must be logged in as an admin user first")
    end
  end

  protect_from_forgery
  
  helper_method :current_user
  
  private

  # Decodes a provided JWT token. Decodes the token with the secret key and returns the decoded JWT and header.
  def decode_jwt(token)
    JWT.decode(token, Rails.application.secrets.jwt_secret_key, true, { algorithm: 'HS256' })
  end

  def current_user_session
    return @current_user_session if defined?(@current_user_session)
    @current_user_session = UserSession.find
  end
  
  def current_user
    return @current_user if defined?(@current_user)
    return nil if UserSession.try(:find).nil?
    @current_user = current_user_session && current_user_session.record
  end

  protected

  def render_error(code, message)
    respond_to do |format|
      format.html { render template: "/errors/#{code}", layout: 'application', status: code }
      format.json { render json: { error: message }, status: code }
      format.xml { render xml: { error: message }, status: code }
      format.any { render nothing: true, status: code }
    end
    true  # so we can do "render_xxx and return"
  end

  # Returns a "clean" backtrace that is suitable for humans
  def clean_backtrace(exception, preamble="Caught Exception")
    "\n\n#{preamble}:" + "\n\n#{exception.class} (#{exception.message}):\n    " +
            Rails.backtrace_cleaner.clean(exception.backtrace).join("\n    ") + "\n\n"
  end
  def authorize
    unless current_user
      session[:blocked_url] =  request.request_uri
      redirect_to(login_path, :notice => "You must be logged in as a verified user first")
    end
  end
 
  def redirect_back_or_default(uri)
    redirect_to(session[:blocked_url] || uri)
    session[:blocked_url] = nil
  end

  def redirect_away(*params)
    session[:original_url] = request.url
    redirect_to(*params)
  end

  def redirect_back(*params)
    url = session[:original_url]
    session[:original_url] = nil
    if url
      redirect_to url
    else
      redirect_to(*params)
    end
  end

end
