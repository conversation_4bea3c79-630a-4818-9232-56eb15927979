class SimpleController < ApplicationController

	def home
	end

	def about
	end

	def downloads
	end

	def statistics
	end

  def contact
  end

  def citing
  end

  def textquery
  end

  def utilities
  end

  def news
  end

  def data_protocols
  end

  def download_smiles
    send_file "#{Rails.root.join('public','system','downloads','current','smiles.csv.gz').to_s}", :type=>"application/zip", :filename => "smiles.csv.gz", :disposition => 'attachment'
  end

  def jspectraviewer
  end

  def jspec_download_library
    send_file "#{Rails.root.join('public', 'jspectra_viewer.min.zip').to_s}", :type=>"application/zip", :filename => "jspec_library.zip", :disposition => 'attachment'
  end

  def contributers
  end

  def download_ppt
    send_file "#{Rails.root.join('public','system','downloads','current','MANA_2023.zip').to_s}", :type=>"application/zip", :filename => "MANA_2023.zip", :disposition => 'attachment'
  end

end
