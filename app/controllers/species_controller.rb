class SpeciesController < ApplicationController
  # if you need more customizability, https://gorails.com/episodes/global-autocomplete-search seems to be a possible solution.
  autocomplete :species, :phylum, scopes: [:distinct_phylum]
  autocomplete :species, :kingdom, scopes: [:distinct_kingdom]
  autocomplete :species, :scientific_name, scopes: [:distinct_species]
  autocomplete :species, :order, scopes: [:distinct_order]
  autocomplete :species, :family, scopes: [:distinct_family]
  include Unearth::Filters::Filterable
  include Shared::CacheGenerator
  filter_by_groups *SpeciesSearcher.original_filters

  def index
    respond_to do |format|
      format.html {
        @search = SpeciesSearcher.search(setup_query, setup_search_options)
      }
    end
  end

  def show
    respond_to do |format|
      format.json {
        response = Rails.cache.fetch(species_cache_key(), expires_in: 7.days) do
          start = params[:start].to_i
          page_length = params[:length].to_i
          species = Species.includes(:natural_products).find(params[:id])
          order_column = case params["order"]["0"]["column"]
                        when "0"
                          "np_mrd_id"
                        when "2"
                          "name"
                        when "3"
                          "moldb_formula"
                        end
          order_direction = params[:order]["0"]["dir"]
          search = "%#{params[:search]["value"]}%"
          filtered_natural_products = species.natural_products
                                        .where("np_mrd_id LIKE ? OR name LIKE ? or moldb_formula like ?",
                                                search,
                                                search,
                                                search)

          natural_products_page = filtered_natural_products.order(order_column => order_direction)
                                                           .offset(start)
                                                           .limit(page_length).map do |np|
            structure_image = if np["thumb_url"].nil?
                                ActionController::Base.helpers.image_tag(moldbi.thumbnail_path(np, format: :svg))
                              else
                                ActionController::Base.helpers.image_tag(np["thumb_url"])
                              end
            {
              np_links: {
                np_mrd_id: np.np_mrd_id,
                cas: np.cas },
              structure_image: structure_image,
              name: np.name,
              formula: np.chemical_formula
            }
          end
          {
            recordsTotal: species.natural_products.count,
            recordsFiltered: filtered_natural_products.count,
            data: natural_products_page
          }
        end
        response[:draw] = params[:draw]
        render json: response
      }
    end
  end

  private

  def setup_search_options
    search_options =
      {
        page: params[:page],
        per_page: 10
        #sort: ['np_mrd_id']
      }
    prefilter = SpeciesSearcher.prefilter(params)
    filter_options = prefilter.merge(apply_searcher_filters(SpeciesSearcher))
    search_options.merge!(filter: filter_options)
  end

  def setup_query
    queries = []
    params[:q] ||= {}

    return '*' if params[:q].blank?

    params[:q].each do |field, value|
      next if value.blank?
      queries << "#{field}:#{value}"
    end

    if queries.present?
      queries.join(" AND ")
    else
      "*"
    end
  end
end