module Advanced
  class NaturalProductIndex
    include Unearth::Index::Indexable

    set_index_name 'advanced_natural_products'
    set_default_field 'name'

    # Define the index
    mapping do
      indexes :id, type: :keyword
      # Index the names we want to search with
      indexes :np_mrd_id, type: :text
      indexes :cas_number, as: 'cas', type: :keyword
      # indexes :name, boost: 200, type: :text
      indexes_with_exact :name, type: :text, boost: 100
      indexes :names, as: Proc.new { synonymified? ? [name].concat(synonymify["synonyms"].map { |synonym| synonym["name"] }) : [name] }, type: :text
      indexes :iupac, type: :text

      # Index the description for full text search
      indexes :description, type: :text

      # MolDB fields
      indexes_with_exact :chemical_formula, as: 'moldb_formula', type: :text
      indexes :mono_mass, as: 'moldb_mono_mass', type: :double
      indexes :average_mass, as: 'moldb_average_mass', type: :double
      indexes :predicted_logp, as: 'moldb_alogps_logp', type: :double
      indexes :predicted_logs, as: 'moldb_alogps_logs', type: :double
      indexes :predicted_solubility,
        as: Proc.new { moldb_alogps_solubility.to_f },
        type: :double
      indexes :electron_acceptor_count, as: 'moldb_acceptor_count', type: :integer
      indexes :electron_donor_count, as: 'moldb_donor_count', type: :integer
      indexes :refractivity, as: 'moldb_refractivity', type: :double
      indexes :polarizability, as: 'moldb_polarizability', type: :double
      indexes :formal_charge, as: 'moldb_formal_charge', type: :integer
      indexes :physiological_charge, as: 'moldb_physiological_charge', type: :integer
      indexes :pka_strongest_acidic, as: 'moldb_pka_strongest_acidic', type: :double
      indexes :pka_strongest_basic, as: 'moldb_pka_strongest_basic', type: :double
      indexes :smiles, as: 'moldb_smiles', type: :text
      indexes :inchi, as: 'moldb_inchi', type: :text
      indexes :inchi_key, as: 'moldb_inchikey', type: :text

      # Classyfire taxonomy
      indexes :classyfied, as: 'classyfired?', type: :boolean
      indexes :direct_parent,
              as: Proc.new { classyfirecation.try(:direct_parent_name) }, type: :text
      indexes :kingdom,
              as: Proc.new { classyfirecation.try(:kingdom_name) }, type: :text
      indexes :superklass,
              as: Proc.new { classyfirecation.try(:superklass_name) }, type: :text
      indexes :klass,
              as: Proc.new { classyfirecation.try(:klass_name) }, type: :text
      indexes :subklass,
              as: Proc.new { classyfirecation.try(:subklass_name) }, type: :text
      indexes :alternative_parents,
              as: Proc.new { classyfirecation.try(:alternative_parent_names) }, type: :text
      indexes :substituents,
              as: Proc.new { classyfirecation.try(:substituent_names) }, type: :text
      indexes :molecular_framework,
              as: Proc.new { classyfirecation.try(:molecular_framework_name) }, type: :text
      indexes :external_descriptors,
              as: Proc.new { classyfirecation.try(:external_descriptor_annotations) }, type: :text
    end    

    # Scopes that are used to build the index. This allows us to index >1
    # model. For example you can return [ Synonym.exported, Brand.exported ]
    # if for example you are indexing different types of names.
    def self.indexed_document_scopes
      # We don't do includes on proteins or ontology here, it get's too
      # memory intensive for the production server.
      [ NaturalProduct.exported ]
    end

    # TODO: Move this into customindex.
    # At least as something you can call to get a basic setup via super()
    def self.searchable_fields
      reject = [:id, :model_type]
      fields = []

      self.mapping.each do |field, options|
        next if field =~ /.+\_exact\z/
        next if reject.include? field
        fields << field
      end

      fields
    end
  end
end
