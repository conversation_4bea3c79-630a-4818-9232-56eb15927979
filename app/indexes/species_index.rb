class SpeciesIndex
  include Unearth::Index::Indexable

  set_index_name 'species'

  mapping do
    indexes :id, type: :text
    indexes_with_exact :scientific_name, type: :text, boost: 100

    # index only what we need from the natural products
    indexes :natural_products, as: proc {
                                                natural_products.exported.map do |np|
                                                  {
                                                    id: np.np_mrd_id,
                                                    name: np.name,
                                                    chemical_formula: np.chemical_formula,
                                                    cas: np.cas,
                                                    thumb_url: np.valid_thumb? ? np.thumb.url : nil
                                                  }.to_json
                                                end
                                              }, type: :text

    # taxonomic categories
    indexes :kingdom, as: 'map_kingdom', type: :text
    indexes :phylum, type: :text
    indexes :order, type: :text
    indexes :klass, as: 'class_name', type: :text
    indexes :family, type: :text

    indexes :tax_id, as: 'scientific_name_taxid', type: :text

  end

  def self.indexed_document_scopes
    [Species.exported]
  end
end
