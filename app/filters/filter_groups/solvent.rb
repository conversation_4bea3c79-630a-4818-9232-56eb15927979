module FilterGroups
  class Solvent < Unearth::Filters::FilterGroup
    FILTERS = {
      Water: 'H<sub>2</sub>O'.html_safe,
      D2O: 'D<sub>2</sub>O'.html_safe,
      CDCl3: 'CDCl<sub>3'.html_safe,
      CD3OD: 'CD<sub>3</sub>OD'.html_safe,
      C5D5N: 'C<sub>5</sub>D<sub>5</sub>N'.html_safe,
      DMSO: 'DMSO',
      acetone: 'C<sub>3</sub>H<sub>6</sub>O'.html_safe
    }.freeze

    def initialize
      super('solvent-filter', 'Filter by solvent', FILTERS)
    end

    def apply_to_relation(relation, params)
      applicable_filters = parse(params)
      if applicable_filters.empty?
        relation
      else
        SpectraFilter.filter_by_solvent(relation, applicable_filters)

      end
    end

    def apply_to_searcher(terms={}, params)
      applicable_filters = parse(params)
      terms[:solvent] = applicable_filters unless applicable_filters.empty?
      terms
    end

    def parse(params)
      filters.keys.select do |filter_param|
        params[filter_param] == '1'
      end
    end


  end
end
