module FilterGroups
  class Kingdom < Unearth::Filters::FilterGroup
    FILTERS = {
      fungi: 'Fungi',
      plantae: 'Plantae',
      animalia: 'Animalia',
      bacteria: 'Bacteria',
      archaea: 'Archaea',
      protozoa: 'Protozoa',
      chromista: 'Chromista'
    }.freeze

    def initialize
      super('kingdom-filter', 'Filter by kingdom', FILTERS)
    end

    def apply_to_relation(relation, params)
      applicable_filters = parse(params)
      if applicable_filters.empty?
        relation
      else
        temp_relation = NaturalProduct.exported.joins(:species)
        applicable_filters << :viridiplantae if applicable_filters.include? :plantae
        applicable_filters << :metazoa if applicable_filters.include? :animalia
        if applicable_filters.include? :bacteria
          applicable_filters = applicable_filters.map { |kingdom| kingdom == :bacteria ? :eubacteria : kingdom }
        end
        applicable_nps_from_species = temp_relation.where('kingdom IN (?)', applicable_filters)
                                                   .distinct.pluck(:np_mrd_id)
        regex_string = applicable_filters.join('|')
        applicable_nps_from_origin = temp_relation.where('origin REGEXP ?', "#{regex_string}|all")
                                                  .distinct.pluck(:np_mrd_id)
        applicable_nps = (applicable_nps_from_origin + applicable_nps_from_species).uniq
        relation.where(np_mrd_id: applicable_nps)
      end
    end

    def apply_to_searcher(terms={}, params)
      applicable_filters = parse(params)
      terms[:origin] = applicable_filters unless applicable_filters.empty?
      terms
    end

    def parse(params)
      filters.keys.select do |filter_param|
        params[filter_param] == '1'
      end
    end


  end
end
