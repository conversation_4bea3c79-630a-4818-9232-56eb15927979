module FilterGroups
  class SpectralType < Unearth::Filters::FilterGroup
    FILTERS = {
      experimental: 'Experimental NMR Spectra',
      simulated: 'Simulated NMR Spectra',
      predicted: 'Predicted NMR Spectra'
    }.freeze

    def initialize
      super('spectral-type-filter', 'Filter by spectral type', FILTERS)
    end

    def apply_to_relation(relation, params)
      applicable_filters = parse(params)
      if applicable_filters.empty?
        relation
      else
        SpectraFilter.filter_by_spectral_type(relation, applicable_filters)
      end
    end

    def apply_to_searcher(terms={}, params)
      applicable_filters = parse(params)
      terms[:spectral_type] = applicable_filters unless applicable_filters.empty?
      terms
    end

    def parse(params)
      filters.keys.select do |filter_param|
        params[filter_param] == '1'
      end
    end


  end
end
