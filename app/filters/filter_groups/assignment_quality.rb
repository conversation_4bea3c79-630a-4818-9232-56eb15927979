module FilterGroups
  class AssignmentQuality < Unearth::Filters::FilterGroup
    FILTERS = {
      level_four: '0-24%',
      level_three: '25-49%',
      level_two: '50-74%',
      level_one: '75-100%'
    }.freeze

    def initialize
      super('assignment-quality-filter', 'Filter by assignment quality', FILTERS)
    end

    def apply_to_relation(relation, params)
      applicable_filters = parse(params)
      if applicable_filters.empty?
        relation
      else
        SpectraFilter.filter_by_assignment_quality(relation, applicable_filters)
      end
    end

    def apply_to_searcher(terms={}, params)
      applicable_filters = parse(params)
      terms[:assignment_quality] = applicable_filters unless applicable_filters.empty?
      terms
    end

    def parse(params)
      filters.keys.select do |filter_param|
        params[filter_param] == '1'
      end
    end
  end
end

