module FilterGroups
  class Nuclei < Unearth::Filters::FilterGroup
    FILTERS = {
      one_d_proton: '<sup>1</sup>H'.html_safe,
      one_d_carbon: '<sup>13</sup>C'.html_safe,
      two_d_proton: '[<sup>1</sup>H, <sup>1</sup>H]'.html_safe,
      hetero_carbon_proton: '[<sup>1</sup>H, <sup>13</sup>C]'.html_safe
    }.freeze

    def initialize
      super('nuclei-filter', 'Filter by nucleus', FILTERS)
    end

    def apply_to_relation(relation, params)
      applicable_filters = parse(params)
      if applicable_filters.empty?
        relation
      else
        SpectraFilter.filter_by_nucleus(relation, applicable_filters)
      end
    end

    def apply_to_searcher(terms={}, params)
      applicable_filters = parse(params)
      terms[:nucleus] = applicable_filters unless applicable_filters.empty?
      terms
    end

    def parse(params)
      filters.keys.select do |filter_param|
        params[filter_param] == '1'
      end
    end


  end
end
