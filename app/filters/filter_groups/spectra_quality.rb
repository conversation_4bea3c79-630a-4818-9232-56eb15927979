module FilterGroups
  class SpectraQuality < Unearth::Filters::FilterGroup
    FILTERS = {
      not_usable: 'Not Usable',
      poor: 'Poor',
      very_low: 'Very Low',
      satisfactory: 'Satisfactory',
      acceptable: 'Acceptable',
      good: 'Good',
      very_good: 'Very Good',
      excellent: 'Excellent'
    }.freeze

    def initialize
      super('spectra-quality-filter', 'Filter by spectra quality', FILTERS)
    end

    def apply_to_relation(relation, params)
      applicable_filters = parse(params)
      if applicable_filters.empty?
        relation
      else
        SpectraFilter.filter_by_spectra_quality(relation, applicable_filters.map{ |filter| filter.to_s.humanize })
      end
    end

    def apply_to_searcher(terms={}, params)
      applicable_filters = parse(params)
      terms[:spectra_quality] = applicable_filters unless applicable_filters.empty?
      terms
    end

    def parse(params)
      filters.keys.select do |filter_param|
        params[filter_param] == '1'
      end
    end


  end
end
