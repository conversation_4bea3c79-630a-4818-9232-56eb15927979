class SpeciesSearcher
  include Unearth::Searcher::Searcher

  search_index :species
  search_with :fuzzy_like_this, :basic, fuzzy_fields: [:scientific_name, :kingdom, :phylum, :order, :family]


  def self.prefilter(params)
    {}
  end

  def self.highlights
    {}
  end

  def self.suggestions(query)
    candidates = [
      {
        field: "scientific_name",
        suggest_mode: "popular",
        min_word_len: 1
      },
      {
        field: "kingdom",
        suggest_mode: "popular",
        min_word_len: 1
      },
      {
        field: "phylum",
        suggest_mode: "popular",
        min_word_len: 1
      },
      {
        field: "order",
        suggest_mode: "popular",
        min_word_len: 1
      },
      {
        field: "family",
        suggest_mode: "popular",
        min_word_len: 1
      }
    ]
    self.index_class.suggestions(query, 'name', candidates)
  end
end