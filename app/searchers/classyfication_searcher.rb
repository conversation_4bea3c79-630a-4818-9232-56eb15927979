class ClassyficationSearcher
  include Unearth::Searcher::Searcher

  search_index "natural_products"
  search_with :name, :basic, :fuzzy_like_this,
              name_fields: [:name, :synonyms],
              fuzzy_fields: [:name, :synonyms]

  def self.prefilter(params)
    {}
  end

  def self.highlights
    NaturalProductSearcher.highlights
  end

  def self.suggestions(query)
    NaturalProductSearcher.suggestions(query)
  end
end