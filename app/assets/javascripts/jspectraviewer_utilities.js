$(document).ready(function() {
	const fileSelector = document.getElementById('file-selector');
	if (typeof fileSelector !== "undefined" && fileSelector !=null){		  
		fileSelector.addEventListener('change', (event) => {
		
		let spectrum_type = $( "#jspectradim" ).find(":selected").val();
		if  (spectrum_type == "2D") {
			var sv = new JSV.SpectraViewer2D("#viewer", {
				debug: false,
				width: $('#viewer-container').width(),
				height: "800",
				zoom_min: 0.1,
				cluster_navigation_id: '#cluster-navigation',
				simplify_tolerance: 0.01,
				highlight: {
					restriction_spectrum_id: 'Assignment',
					element_type: 'peak',
					display: { fill: 'rgba(238, 238, 255, 0.7)' },
					visible_only: false
				},
				assignment_table: {
                    container_id: '#assignments',
                    edit_mode: false
                }, 
                // select: {
                //     element_type: 'peak',
                //     allow_multiselect: true,
                //     allow_adjust: false,
                //     allow_peak_creation: false,
                //     show_indicators: true,
                //     display: { fill: 'rgba(208, 208, 255, 0.75)' }
                // },
                // structure: {
                //     container_id: '#structure',
                //     use_jsmol: true,
                //     show_atom_numbers: true,
                //     show_hydrogen: true,
                //     width: 400,
                //     height: 400
                // },
			});

			sv.flash('Loading...');
			const fileList = event.target.files;
			const fr = new FileReader();
			fr.onload=function(){
				nmrml = fr.result;
				console.log(nmrml);
				sv.add_nmrml_data(nmrml);
				};
			fr.readAsText(fileList[0]);

			}

		else if (spectrum_type == "1D") {
			var SVr = new JSV.SpectraViewer("#viewer", {
				width: $("#viewer-container").width(),
				height: 400,
				drag_drop_load: false,
				// zoom_max: 500,
				axis_y_show: true,
				axis_y_lock: 0.04,
				axis_x_title: 'ppm',
				axis_y_title: 'Intensity',
				// axis_y_gutter: 120,
				// axis_x_gutter: 80,
				// x_tick_count: 15,
				cluster_navigation_id: '#cluster-navigation',
				simplify_tolerance: 0.01,
				min_boundaries: {x: [-1, 10], y: [0, 1.2]},
				highlight: {
					restriction_spectrum_id: 'Assignment',
					element_type: 'peak',
					display: { fill: 'rgba(238, 238, 255, 0.7)' },
					visible_only: false
				},
				assignment_table: {
                    container_id: '#assignments',
                    edit_mode: false
                }, 
                select: {
                    element_type: 'peak',
                    allow_multiselect: true,
                    allow_adjust: false,
                    allow_peak_creation: false,
                    show_indicators: true,
                    display: { fill: 'rgba(208, 208, 255, 0.75)' }
                },
                structure: {
                    container_id: '#structure',
                    use_jsmol: true,
                    show_atom_numbers: true,
                    show_hydrogen: true,
                    width: 400,
                    height: 400
                },
			})

				SVr.flash('Loading...');
				const fileList = event.target.files;
				const fr = new FileReader();
				fr.onload=function(){
					nmrml = fr.result;
					console.log("adding file data", nmrml);
					SVr.add_nmrml_data(nmrml);
					SVr.reset_boundaries();
					// SVr.zoombox.set_zoom_area();
					};
				fr.readAsText(fileList[0]);
			
			} 
		
		
		}


		)}});

