# Load any reference tooltips
$ ->
  $(".reference-icon-link").tooltip()

# Load any tip tooltips
$ ->
  $(".tips").tooltip()

$ ->
  $('.datatable').dataTable()

  # Customize datatables to change the look
  $('.dataTables_filter label').each ->
    searcher = $(this).find('input').clone(true)
    $(this).html('')
    searcher.appendTo($(this))

  $(".dataTables_filter input").attr('placeholder', 'Search')

$ ->
    $("table").tooltip()

# reset modals loaded from remote when hidden
# allows us to re-use modals
$ ->
  $(document).on 'hidden.bs.modal', (e) ->
    $(e.target).removeData('bs.modal')

$ ->
  $('#load-screen').show()
  # Resize header links font size on window resize
  # The quickfit tolerance param make the text look too small locally but bigger
  # in production, I don't know why...
  $(window).resize () ->
    if $('.link-bar.one .link-text').length
        $('.link-bar.one .link-text').quickfit({max: "60", tolerance: 0.46})
        $('.link-bar.two .link-text').css('font-size', $('.link-bar.one .link-text').css('font-size'))
        $('.link-bar.three .link-text').css('font-size', $('.link-bar.one .link-text').css('font-size'))

  $(window).load () ->
    if $('.link-bar.one .link-text').length
      $('.link-bar.one .link-text').quickfit({max: "60", tolerance: 0.46})
      $('.link-bar.two .link-text').css('font-size', $('.link-bar.one .link-text').css('font-size'))
      $('.link-bar.three .link-text').css('font-size', $('.link-bar.one .link-text').css('font-size'))

    $('.link-bar.one').animate({
      right: '0%'
    }, 2000)

    $('.link-bar.two').animate({
      right: '0%'
    }, 2000)

    $('.link-bar.three').animate({
      right: '0%'
    }, 2000)

    $('#load-screen').hide()

  # Add show loader class to links for MetaboCards using a regex on the url
  # Regex is specific to avoid links to .xml files, etc.
  $("a").filter( ->
    @href.match /natural_products$/
  ).addClass('show-loader')

  $("a").filter( ->
    @href.match /natural_products\/NP\d{7}$/
  ).addClass('show-loader')

  # $("a").filter( ->
  #   @href.match /submissions$/
  # ).addClass('show-loader')

  # Show loading gif when clicking on specific links, only if we are NOT holding down a key
  # (i.e. command-click opening a new window)
  $('.show-loader').click (e) ->
    if !e.metaKey
      $('#load-screen').show()

  # Make sure we turn off the loading gif when you use back links
  window.onpageshow = (event) ->
    if event.persisted
      $('#load-screen').hide()

# Truncating text and tables on NP-Card
# Source: https://github.com/jedfoster/Readmore.js/blob/master/README.md
$ ->
  $('.met-desc').readmore
    moreLink: '<a href="#" style="text-align:right;padding-right:10px;font-weight:bold;text-decoration:none;">Read more...</a>'
    lessLink: '<a href="#" style="text-align:right;padding-right:10px;font-weight:bold;text-decoration:none;">Close</a>'
    speed: 1000
    collapsedHeight: 150
    heightMargin: 150

  $('.data-table-container').readmore
    moreLink: '<a href="#" style="text-align:center;font-weight:bold;text-decoration:none;">Show more...</a>'
    lessLink: '<a href="#" style="text-align:center;font-weight:bold;text-decoration:none;">Close</a>'
    speed: 1000
    collapsedHeight: 200
    heightMargin: 200

  $('.acc-num').readmore
    moreLink: '<a href="#" style="padding-left:10px;font-weight:bold;text-decoration:none;">Show more accession numbers</a>'
    lessLink: '<a href="#" style="padding-left:10px;font-weight:bold;text-decoration:none;">Close</a>'
    speed: 1000
    collapsedHeight: 100

$ ->
  $('.comment-icon').tooltip()


# Collapse NMR Spectra list
$ ->
  # Select nmr spectra table
  $(".natural-products > tbody > tr > td:last-child > table").readmore
    moreLink: '<a href="#" style="text-align:left;font-weight:bold;text-decoration:none;">Show more...</a>'
    lessLink: '<a href="#" style="text-align:left;font-weight:bold;text-decoration:none;">Close</a>'
    speed: 1000
    collapsedHeight: 100

# Truncating species list
window.showMore = (more_link, less_link, list_id) ->
  $("#" + more_link).hide()
  $("#" + less_link).show()
  $("." + list_id).css "display", "table-row"
  false

window.showLess = (more_link, less_link, list_id) ->
  $("#" + more_link).show()
  $("#" + less_link).hide()
  $("." + list_id).css "display", "none"
  false

$ ->
  $('#chemical_shift_genus').hide()
  $('#chemical_shift_species').hide()
  $('#chemical_shift_provenance').change (e) ->
    provenance_val = $('#chemical_shift_provenance').val()
    if !(provenance_val == "Other" || provenance_val == "Biotransformation" || provenance_val == "Chemical synthesis")
      $('#chemical_shift_genus').show()
      $('#chemical_shift_species').show()
    else
      $('#chemical_shift_genus').hide()
      $('#chemical_shift_species').hide()

$ ->
  console.log("Starting")
  if !($('#chemical_shift_provenance_verification').val() == "Other" || $('#chemical_shift_provenance_verification').val() == "Biotransformation" || $('#chemical_shift_provenance_verification').val == "Chemical synthesis")
    console.log('INSIDE Verifciaton')
    $('#chemical_shift_genus_verification').show()
  else
    console.log('INSIDE Verifciaton hide')
    $('#chemical_shift_genus_verification').hide()
  $('#chemical_shift_provenance_verification').change (e) ->
    provenance_val_verification = $('#chemical_shift_provenance_verification').val()
    if !(provenance_val_verification == "Other" || provenance_val_verification == "Biotransformation" || provenance_val_verification == "Chemical synthesis")
      $('#chemical_shift_genus_verification').show()
    else
      $('#chemical_shift_genus_verification').hide()

$ ->
  console.log("Starting")

  if !($('#chemical_shift_provenance_edit').val() == "Other" || $('#chemical_shift_provenance_edit').val() == "Biotransformation" || $('#chemical_shift_provenance_edit').val() == "Chemical synthesis")
    $('#chemical_shift_genus_edit').show()
    $('#chemical_shift_species_edit').show()
  else
    $('#chemical_shift_genus_edit').hide()
    $('#chemical_shift_species_edit').hide()
  $('#chemical_shift_provenance_edit').change (e) ->
    provenance_val_edit = $('#chemical_shift_provenance_edit').val()
    if !(provenance_val_edit == "Other" || provenance_val_edit == "Biotransformation" || provenance_val_edit == "Chemical synthesis")
      $('#chemical_shift_genus_edit').show()
      $('#chemical_shift_species_edit').show()
    else
      $('#chemical_shift_genus_edit').hide()
      $('#chemical_shift_species_edit').hide()




$ ->
  $('#submission_genus').hide()
  $('#submission_species').hide()
  $('#submission_provenance').change (e) ->
    provenance_val = $('#submission_provenance').val()
    if !(provenance_val == "Other" || provenance_val == "Biotransformation" || provenance_val == "Chemical synthesis")
      $('#submission_genus').show()
      $('#submission_species').show()
    else
      $('#submission_genus').hide()
      $('#submission_species').hide()


$ ->
  console.log("Starting")
  if !($('#submission_provenance_verification').val() == "Other" || $('#submission_provenance_verification').val() == "Biotransformation" || $('#submission_provenance_verification').val == "Chemical synthesis")
    console.log('INSIDE Verifciaton')
    $('#submission_genus_verification').show()
  else
    console.log('INSIDE Verifciaton hide')
    $('#submission_genus_verification').hide()
  $('#submission_provenance_verification').change (e) ->
    provenance_val_verification = $('#submission_provenance_verification').val()
    if !(provenance_val_verification == "Other" || provenance_val_verification == "Biotransformation" || provenance_val_verification == "Chemical synthesis")
      $('#submission_genus_verification').show()
    else
      $('#submission_genus_verification').hide()


$ ->
  console.log("Starting")

  if !($('#submission_provenance_edit').val() == "Other" || $('#submission_provenance_edit').val() == "Biotransformation" || $('#submission_provenance_edit').val() == "Chemical synthesis")
    $('#submission_genus_edit').show()
    $('#submission_species_edit').show()
  else
    $('#submission_genus_edit').hide()
    $('#submission_species_edit').hide()
  $('#submission_provenance_edit').change (e) ->
    provenance_val_edit = $('#submission_provenance_edit').val()
    if !(provenance_val_edit == "Other" || provenance_val_edit == "Biotransformation" || provenance_val_edit == "Chemical synthesis")
      $('#submission_genus_edit').show()
      $('#submission_species_edit').show()
    else
      $('#submission_genus_edit').hide()
      $('#submission_species_edit').hide()

#$ ->
# $('#load-screen').show()
# $.ajax({
#  complete: (e) ->
#    $('#load-screen').hide()
# })

$ ->
  $('body').on 'click', 'a[data-popup]', (e) ->
    window.open $(this).attr('href'), 'Popup', 'height=600, width=1000'
    e.preventDefault()
    return
$ ->
  $('body').on 'click', 'button[close-window]', (e) ->
    window.close()
  return

$ ->
  if($('.lit-text').length == 0)
    return

  $('.lit-text').get(0).oninput = (event) ->
    event.target.setCustomValidity('')
  $('.lit-type').change (e) ->
    lit_type = $('.lit-type').val()
    invalid_message = ""
    if(lit_type == 'DOI')
      $('.lit-text').get(0).pattern = '10.\\d{4}.+'
      pattern = $('.lit-text').get(0).pattern
      invalid_message = "Please provide a valid DOI"
    else if (lit_type == 'PMID')
      $('.lit-text').get(0).pattern = "\\d{7,8}"
      invalid_message = "Please provide a valid PMID"
      pattern = $('.lit-text').get(0).pattern
    else if (lit_type == 'Unpublished / Under Review')
      $('.lit-text').get(0).value = 'Unpublished / Under Review'
      $('.lit-text').prop('disabled', true);
      return
    else
      $('.lit-text').pattern = '.*'

    if $('.lit-text').get(0).value == 'Unpublished / Under Review' ||
        $('.lit-text').get(0).value == 'Unpublished/Under Review'
      $('.lit-text').get(0).value = ""
    $('.lit-text').prop('disabled', false);
    $('.lit-text').get(0).title = invalid_message
    $('.lit-text').get(0).oninvalid = (event) ->
      event.target.setCustomValidity(invalid_message)

$ ->
  if($('#3d-btn').length != 0 )
    $('.structure-links > .btn-group a:last-child:not("#3d-btn"):not("#fs-jsmol")').addClass('no-radius')

$ ->
  $('[data-toggle="tooltip"]').tooltip()
  return

$ ->
  $('.species-table').each ->
    species_id = $(this).data('species_id')
    $(this).dataTable(
      processing: true,
      serverSide: true,
      ajax: {
        url: "/species/#{species_id}.json"
      },
      columns: [
        {
          data: "np_links",
          className: "natural-product-links"
          render: (data) ->
            return "<a class=\"btn-card show-loader\" href=\"/natural_products/#{data['np_mrd_id']}\">#{data['np_mrd_id']}</a><div class=\"cas\">#{data["cas"]}</div>"
        },
        {data: "structure_image",
        className: "natural-product-structure"},
        {data: "name",
        className: "natural-product-name"},
        {data: "formula",
        className: "natural-product-formula"}
      ],
      columnDefs: {
        targets: 1,
        orderable: false
      },
      pageLength: 5,
      lengthMenu: [5, 10, 25, 50, 100])

$ ->
  if($("button[name='filter']").length != 0)
    $("button[name='filter']").click ->
      $('#load-screen').show()

$ ->
  $(document).ready ->
    $('#load-screen').hide()

$ ->
  $(document).ready ->
    $('body').on 'click.scroll-adjust', '[href^="#"]', (event) ->
      # make sure navigation hasn't already been prevented
      if event and event.isDefaultPrevented()
        return
      # get a reference to the offending navbar
      $nav = $('nav.navbar')
      $jump = $('div#jumper.natural_product')

      if $jump.length != 0
        console.log("Jump")
        # check if the navbar is fixed
        if $jump.css('position') != 'fixed'
          $(window).one 'scroll', ->
            # scroll the window up by the height of the navbar
            window.scrollBy 0, -($jump.outerHeight() + 2 * $nav.height())
          return
        # listen for when the browser performs the scroll
        $(window).one 'scroll', ->
          # scroll the window up by the height of the navbar
          window.scrollBy 0, -$jump.outerHeight()
          return
      else
        # check if the navbar is fixed
        if $nav.css('position') != 'fixed'
          return
        # listen for when the browser performs the scroll
        $(window).one 'scroll', ->
          # scroll the window up by the height of the navbar
          window.scrollBy 0, -$nav.height()
          return
      return
      