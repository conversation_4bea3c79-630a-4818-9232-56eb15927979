function isPicked() {
    removeStyle()
    index = arguments[2] + 1
    selectAtom(index);
}

function removeStyle(){
    var cell = document.getElementById('atom_table').rows.length;
    for (i = 1; i < cell; i++) {
        let s = `select atomno = ${i}; halos OFF;`
        Jmol.script(jmolApplet1, s);

        var cell_1 = document.getElementById('atom_table').rows[i].cells[0];
        var cell_2 = document.getElementById('atom_table').rows[i].cells[1];
        cell_1.classList.remove("custom-selection");
        cell_2.classList.remove("custom-selection");

        cell_1.classList.remove("custom-selection-hydrogen");
        cell_2.classList.remove("custom-selection-hydrogen");

        cell_1.classList.remove("custom-selection-parent");
        cell_2.classList.remove("custom-selection-parent");
      }
}

function appendHydrogens(bondInfo, atomBonds, atomNo, type){
    let hydrogens = []
    bondInfo.forEach(bond => {
        let hydrogenNo;
        if(bond["atom1"]["atomno"] === atomNo && bond["atom2"]["sym"] === "H"){
            hydrogens.push(bond["atom2"]["atomno"])
        } else if(bond["atom2"]["atomno"] === atomNo && bond["atom1"]["sym"] === "H"){
            hydrogens.push(bond["atom1"]["atomno"])
        }
    }) ;
    hydrogens = hydrogens.reverse();
    atomBonds.push({"type": type, "atomNo": atomNo ,"hydrogens": hydrogens})
}

/**
 * @returns {*[]} Array containing information for all hydrogen bonds in the molecule currently loaded is JSmol
 */
function atomOrdering(){
    const bondInfo = Jmol.getPropertyAsArray(jmolApplet0, "bondInfo");
    let atomRows = Array.prototype.slice.call(document.getElementById('atom_table').rows);
    atomRows.shift();
    let atoms = atomRows.filter(atomRow => atomRow.cells[0].innerText !== 'H');
    let atomBonds = [];
    let hydrogenIndex = 1;

    atoms.forEach(atom => {
        const atomNo = parseInt(atom.cells[1].innerText);
        const type = atom.cells[0].innerText;
        appendHydrogens(bondInfo, atomBonds, atomNo, type);
    });
    atomBonds.sort((a,b) => (a["atomNo"] > b["atomNo"] ) ? 1 : ((b["atomNo"]  > a["atomNo"] ) ? -1 : 0))

    return atomBonds;
}

/**
 * Highlight atom in JSmol and it's corresponding row
 * @param index mol file index of the atom
 */
function selectAtom(index) {
    removeStyle()
    let row = document.getElementById(`atom-row-${index}`);
    let cell_1 = row.cells[1];
    let cell_2 = row.cells[0];
    let cell_3 = row.cells[2].children[0]; // get input element inside cell

    cell_1.classList.add("custom-selection");
    cell_2.classList.add("custom-selection");
    cell_3.focus();

    let s = `select atomno = ${index}; halos ON;`

    if (cell_2.innerText === 'H'){
        s += ' color halos blue;';

        let parentAtom = atomOrdering().filter(atom => atom.hydrogens.includes(index))[0];
        row = document.getElementById(`atom-row-${parentAtom.atomNo}`);
        row.cells[1].classList.add("custom-selection-parent");
        row.cells[0].classList.add("custom-selection-parent");
        s += ` select atomno = ${parentAtom.atomNo}; halos ON; color halos green;`
        parentAtom.hydrogens.forEach(hydrogen => {
            if (hydrogen !== index){
                row = document.getElementById(`atom-row-${hydrogen}`);
                row.cells[1].classList.add("custom-selection-hydrogen");
                row.cells[0].classList.add("custom-selection-hydrogen");

                s += ` select atomno = ${hydrogen}; halos ON; color halos red;`
            }
        });
    } else {
        s += ' color halos blue;'
        let selectedAtom = atomOrdering().filter(atom => atom.atomNo === index)[0];
        selectedAtom.hydrogens.forEach(hydrogen => {
            row = document.getElementById(`atom-row-${hydrogen}`);
            row.cells[1].classList.add("custom-selection-hydrogen");
            row.cells[0].classList.add("custom-selection-hydrogen");

            s += ` select atomno = ${hydrogen}; halos ON; color halos red;`
        });
    }
    Jmol.script(jmolApplet1, s);
}

/**
 * Displays custom label on atoms in JSmol
 * Also colours labelled carbons if inside the custom numbering modal
 * @param {String} index index of the atom to label
 * @param {String} label label to apply to the atom
 *
 */
function updateLabel(index, label, in_custom_numbering = false) {
    let s = `select atomno = ${index}; set label "${label}";`;
    if(in_custom_numbering){
        if(index !== label) {
            let atomSymbol = Jmol.getPropertyAsArray(jmolApplet0, "atomInfo")[parseInt(index)-1].sym;
            if (atomSymbol === "C") {
                s += 'color atoms [x90ee90];'
            }
        } else {
            let atomSymbol = Jmol.getPropertyAsArray(jmolApplet0, "atomInfo")[parseInt(index)-1].sym;
            if (atomSymbol === "C") {
                s += 'color atoms CPK;'
            }
        }
        Jmol.script(jmolApplet1, s);
    } else{
        Jmol.script(jmolApplet0, s);
    }
}

/**
 * Clears the numbering in both the atom table and in JSmol (to default)
 */
function resetNumbering() {
    if(confirm("This will clear all fields inside the Custom Atom Numbering Table." +
        "\nDo you wish to continue?")) {
        let cell = document.getElementById('atom_table').rows.length
        for (let i = 1; i < cell; i++) {
            updateLabel(i.toString(), i.toString(), true);
            document.getElementById('atom_table').rows[i].cells[2].children[0].value = ''
        }
    }
}

/**
 * Does a simple mapping for Hydrogens based on the custom numbering
 */
function mapHydrogens() {
    if(confirm("This will auto-populate hydrogens with unused numbers in ascending order." +
        "\n Do you wish to continue?")) {
        let hydrogenIndex = 1;
        let atomBonds = atomOrdering();

        let table = document.getElementById('atom_table');
        let rows = table.rows.length;
        let numberings = new Set();
        for (let i = 1; i < rows; i++) {
            let val = parseInt(table.rows[i].cells[2].children[0].value);
            if (!isNaN(val)) numberings.add(val);
        }

        atomBonds.forEach(atom => {
            atom.hydrogens.reverse().forEach(hydrogen => {
                let target = document.getElementById(`atom-row-${hydrogen}`).cells[2].children[0];
                while (numberings.has(hydrogenIndex)) hydrogenIndex++;
                if (!target.value) {
                    updateLabel(hydrogen, hydrogenIndex.toString(), true);
                    target.value = hydrogenIndex++;
                }
            })
        })
    }
}

function cbOn(c,f) {
    var s='';
    var cb = 'loadStructCallback pickCallback hoverCallback animFrameCallback'.split(' ');
    for (var i=0; i<cb.length; i++) {
        s += 'set ' + cb[i] + ' "' + (c == cb[i] ? f : '') + '"; ';
    }
    Jmol.script(jmolApplet0, s); // for all pages
    Jmol.script(jmolApplet1, s) // for the custom numbering modal
}

function toggleLabels() {
    // Toggle method to show and hide labels in JSmol
    let btn = $('#toggle-labels');

    if (btn.hasClass("btn-danger")) {
        let btnWidth = btn.width()
        if (typeof jmolApplet0 == "object") {
            Jmol.script(jmolApplet0, "select all; labels off");
        } else {
            Jmol.script(jmolApplet1, "select all; labels off");
        }
        btn.removeClass("btn-danger")
        btn.addClass("btn-primary")
        btn.text("Show labels")
        // Keep size between label changes
        btn.width(btnWidth+'px')
    } else {
        if (typeof jmolApplet0 == "object") {
            Jmol.script(jmolApplet0, "select all; labels %e");
        } else {
            Jmol.script(jmolApplet1, "select all; labels on");
        }
        btn.removeClass("btn-primary")
        btn.addClass("btn-danger")
        btn.text("Hide labels")
    }
}

function toggleHydrogens() {
    // Toggle method to show and hide Hydrogens in JSmol
    let btn = $('#toggle-H')

    if(btn.hasClass('btn-danger')) {
        let btnWidth = btn.width()
        Jmol.script(jmolApplet0, "select all; set showHydrogens FALSE");
        btn.removeClass("btn-danger")
        btn.addClass("btn-primary")
        btn.text("Show Hydrogens")
        // Keep size between labels changes
        btn.width(btnWidth+'px')
    } else {
        Jmol.script(jmolApplet0, "select all; set showHydrogens TRUE");
        btn.removeClass("btn-primary")
        btn.addClass("btn-danger")
        btn.text("Hide Hydrogens")
    }
}


function toggleNMR() {
    // Toggle method to show and hide Hydrogens in JSmol
    let btn = $('#toggle-NMR')

    if(btn.hasClass('btn-danger')) {
        let btnWidth = btn.width()
        Jmol.script(jmolApplet0, "select all; labels %e; color label black");
        $('#toggle-labels').attr('disabled', false).removeClass('disabled');
        btn.removeClass("btn-danger")
        btn.addClass("btn-primary")
        btn.text("Show NMR Assignment")
        // Keep size between labels changes
        btn.width(btnWidth+'px')
    } else {
        if($('#toggle-labels').hasClass('btn-primary')) {
            toggleLabels()
        }
        $('#toggle-labels').attr('disabled',true).addClass('disabled');
        labelCS()
        btn.removeClass("btn-primary")
        btn.addClass("btn-danger")
        btn.text("Hide NMR Assignment")
    }
}

function labelCS() {
    let assignmentLink = $('#view_3d').data('assignments')
    fetch(assignmentLink)
        .then(response => response.text())
        .then(data => {
            // Do something with your data
            let lines = data.split('\n')
            console.log(lines)
            for(let i = 0; i , lines.length - 1; i++) {
                let shiftData = lines[i].split(',')
                if (shiftData[3] === "") {
                    continue
                }
                let s = `select atomno = ${shiftData[1]}; set label "%e ${shiftData[2]}";  color label red`;
                Jmol.script(jmolApplet0, s);
            }
            console.log(data);
        });
}

function recenter() {
    Jmol.script(jmolApplet0, 'rotate best');
}

function zoomFit() {
    Jmol.script(jmolApplet0, 'zoom 0');
}

function downloadJsmol() {
    Jmol.script(jmolApplet0, 'write image png "Structure.png"')
}

