bayesil = {}
window.bayesil = bayesil

$ ->

  # X-editable
  # $.fn.editable.defaults.mode = 'inline'

  # Closing of flash message boxes
  # $('a.close-flash').on 'click', () ->
  #   $(this).parent().slideUp()
  #   false

  # $('#index-table').DataTable({
  #   # dataTables 1.10 settings
  #   # "order": [ 2, "desc" ],
  #   "paging":  true
  #   "destroy": true
  # })

  # ------
  # Custom Defined Mixture Code

  filter_table = $('.filter-list').DataTable({
    # dataTables 1.10 settings
    "scrollCollapse": true,
    "scrollY": "300px",
    "paging": false
    "destroy": true
    "columnDefs": [ {
       "targets": 0,
       "orderable": false
     } ]
  })

  # Fix the width of the header columns after show the dialog
  # Also activate tool-tips
  $('#custom-filter-list').on 'shown.bs.modal', ->
    filter_table.columns.adjust().draw()

  # Tool tips settings
  $('.tool-tip').tooltip()

  $('.btn-disabled').on 'click', ->
    false;

  # Cancel
  $('.custom-filter-cancel').on 'click', ->
    $('#custom-filter-list').modal('hide')


  # Save
  $('#custom-filter-save').on 'click', ->
    $('#custom-filter-list input:checkbox').each ->
      $(this).data('checked-state', this.checked)
    $('#custom-filter-list').modal('hide')

  # Dialog Closed
  # Set check boxes to checked-state
  $('#custom-filter-list').on 'hide.bs.modal', ->
    $('#custom-filter-list input:checkbox').each ->
      $(this).prop('checked', $(this).data('checked-state'))
    check_custom_list_count()

  # Edit
  $('#custom-filter-edit').on 'click', ->
    $('#custom-filter-list').modal('show')
    return false

  # Select All
  $('#filter-button-all').on 'click', ->
    $('#custom-filter-list input:checkbox').prop('checked', true).change()
    return false

  # Select None
  $('#filter-button-none').on 'click', ->
    $('#custom-filter-list input:checkbox').prop('checked', false).change()
    return false

  # Select Other (eg. csf, serum ...)
  $('.filter-button-select').on 'click', ->
    ids = $(this).data('hmdb-ids').split(',')
    $('#custom-filter-list input:checkbox').prop('checked', false).change()
    $.each ids, (index, value) ->
      $('#custom-filter-list input:checkbox[value="' + value + '"]').prop('checked', true).change()
    return false

  # Update list count as checkboxes are selected
  $('#custom-filter-list input:checkbox').on 'change', ->
    update_custom_list_count()


  # Show/hide Spectra viewer instructions
#  $('#spectra-viewer-more-info').on 'click', () ->
#    if $(this).html() == 'Less info...'
#      $(this).html('More info...')
#      $('#spectra-viewer-instructions').slideUp('fast')
#    else
#      $(this).html('Less info...')
#      $('#spectra-viewer-instructions').slideDown('fast')
#    return false

  # Shoe/hide advanced settings
  $('#advanced-link').on 'click', () ->
    $('#advanced-section').slideToggle()
    false

# Since the SpectraViewer can take a while to load,
# use $(window).load rather than $(document).ready, so images are loaded first
$(window).load ->
  is_submission = $('.panel#spectrum .panel-body').length != 0
  main_width = if is_submission then $('.panel#spectrum .panel-body').width() else $('#spectra-container').width()
  $('#spectra-viewer').each ->
    # Initialize Spectra Viewer
    sv = new JSV.SpectraViewer('#spectra-viewer', {

      width: main_width + 50,
      height: 400,
      # debug: true,
      drag_drop_load: false,
      zoom_max: 500,
      axis_y_show: true,
      axis_y_lock: 0.04,
      axis_x_title: 'ppm',
      axis_y_title: 'Intensity',
      axis_y_gutter: 120,
      axis_x_gutter: 80,
      x_tick_count: 15,
      cluster_navigation_id: '#cluster-navigation',
      simplify_tolerance: 0.01,
      zoom_max: 1000,
      min_boundaries: {x: [0, 1], y: [-1, 1.19]},
      highlight: {
        restriction_spectrum_id: 'Assignment',
        element_type: 'peak',
        display: { fill: 'rgba(238, 238, 255, 0.7)' },
        visible_only: false
      },
      select: {
        element_type: 'peak',
        allow_multiselect: true,
        allow_adjust: true,
        allow_width_adjust: true,
        allow_peak_creation: true,
        show_indicators: true,
        display: { fill: 'rgba(208, 208, 255, 0.75)' }
      },
      structure: {
        container_id: '#structure',
        show_hydrogen: true,
        show_atom_numbers: true,
        use_jsmol: true,
        width: 400,
        height: 400
      },
      assignment_table: {
        container_id: '#assignments',
        edit_mode: false
      }
    })
    window.sv = sv
    spectra_path = $(this).data('spectra_path')
    load_spectrum = (path) ->
      if sv.spectra().empty() then sv.clear()
      sv.flash('Loading...')
      $.ajax
          dataType: 'json',
          url: path,
          success: (data) ->
            if data
              sv.add_bayesil_data(data)
              sv.draw()
          #   else
          #     spectrum_error()
          # error: () ->
          #   spectrum_error()

    # spectrum_error = () ->
    #   sv.clear()
    #   sv.remove_all_spectra()
    #   sv.flash('Spectrum is not available')

    # if $(this).parents('#dragndrop-viewer').length
    #   sv.flash('Drag Bayesil JSON file here...')
    #   sv.drag_drop_load = true
    #   sv.on 'drop', () ->
    #     if (sv.json_file)
    #       $('#spectra-name').html(': ' + sv.json_file.name)
    #   setup_selections(sv)
    # else
    #   load_spectrum($(this).data('spectra_path'))

    # data_nmrml_path = $(this).data('nmrml_path')
    load_nmrml = (nmrml_path) ->
      if sv.spectra().empty() then sv.clear()
      source = d3.select('#spectra-viewer').attr('data-source')
      sv.flash('Loading...')
      $('#jsv-phase').removeClass('btn-on')
      $('#phase-controls').slideUp 'fast'
      $.ajax
        dataType: 'text',
        url: nmrml_path,
        success: (nmrml) ->
          if nmrml
            saved_domains = [sv.scale.x.domain(), sv.scale.y.domain()]
            viewer_was_not_empty = sv.spectra().length > 0
            sv.remove_all_spectra()
            sv.add_nmrml_data(nmrml, source)
            sv.annotation.visible = $('#jsv-show-labels').hasClass('btn-on')
            $('.jsv-cluster-navigator td:first').html('Multiplets')
            # if (viewer_was_not_empty)
            #   sv.set_domain('x', saved_domains[0])
            #   sv.set_domain('y', saved_domains[1])
            $window.trigger('resize')
            sv.reset_boundaries()
            sv.draw()
            # if sv.clusters().length <= 0
            #   $('#jsv-assignments-edit').trigger 'click'
    #       else
    #         spectrum_error()
    #     error: () ->
    #       spectrum_error()
    # load_nmrml(data_nmrml_path)


    ss = spectra_path.search('.json')
    if ss>0
      load_spectrum(spectra_path)
    else
      load_nmrml(spectra_path)
    # Make Spectra Viewer Stick to window top when scrolling
    $window = $(window)
    sv_container = $('#spectra-container')
    sv_anchor = $('#spectra-anchor')  # Viewer offset replacement with viewer becomes fixed
    top_margin = $('#main-nav').height()
    viewer_top = sv_container.offset().top
    min_window_height = sv_container.height() + top_margin + 30

    main_width = if is_submission then $('.panel#spectrum .panel-body').width() - 1 else $('#spectra-container').width()
    structure_width = $('#structure').width()
    cluster_section_width = main_width - structure_width - 4
    $('#cluster-navigation').width(cluster_section_width - 2)
    $('#assignments').width(cluster_section_width)

    # Window Resize
    $window.resize () ->
      main_width = if is_submission then $('.panel#spectrum .panel-body').width() - 1 else $('#spectra-container').width()
      structure_width = $('#structure').width()
      sv.resize(main_width, null, true)
      $('#spectra-header').width(main_width)
      $('#spectra-controls').width($('main').width())
      $('#spectrum-message-section').width($('main').width())
      # $('#phase-controls').width($('main').width())
      cluster_section_width = main_width - structure_width - 4
      $('#assignments').width(cluster_section_width)
      $('#cluster-navigation').width(cluster_section_width - 2)
      if $('#assignment-help').offset().left < 50
        $('#assignment-help').width(main_width)
      else
        $('#assignment-help').width(cluster_section_width)
      if $window.height() <= min_window_height
        $('#lock-viewer').addClass('disabled')
        $('#lock-viewer').html( $('#lock-viewer').data('off-name'))
        $('#lock-viewer').removeClass('btn-on')
        $window.scroll() # trigger scroll event to update stickiness
      else
        $('#lock-viewer').removeClass('disabled')

    sv.structure.on 'structure-resize', () ->
      main_width = main_width = if is_submission then $('.panel#spectrum .panel-body').width() else $('#spectra-container').width()
      structure_width = $('#structure').width()
      cluster_section_width = main_width - structure_width - 4
      $('#assignments').width(cluster_section_width)
      $('#cluster-navigation').width(cluster_section_width - 2)
      if $('#assignment-help').offset().left < 50
        $('#assignment-help').width(main_width)
      else
        $('#assignment-help').width(cluster_section_width)










    # display_nmr_frequency = () ->
    #   if JSV.isNumeric(nmr_frequency()) then nmr_frequency() + ' MHz' else 'NA'

    # nmr_frequency = () ->
    #   if sv && sv.spectra(1) && sv.spectra(1).meta && sv.spectra(1).meta && sv.spectra(1).meta.frequency
    #     d3.round(sv.spectra(1).meta.frequency, 1)
    #   else
    #     $('.nmr-frequency').html()

    # spectrum_points = () ->
    #   if sv && sv.spectra(1) then sv.spectra(1).xy_data.length()

    # display_spectrum_points = () ->
    #   if JSV.isNumeric(spectrum_points()) then spectrum_points() + ' pts' else 'NA'

    # spectrum_sweep_width = (hz=false) ->
    #   if sv && sv.spectra(1)
    #     meta = sv.spectra(1).meta
    #     if hz
    #       if meta && meta.sweep_width
    #         d3.round(meta.sweep_width, 1)
    #       else if JSV.isNumeric(nmr_frequency())
    #         d3.round(spectrum_sweep_width() * nmr_frequency(), 1)
    #     else
    #       extent = d3.extent(sv.spectra(1).xy_data.x)
    #       d3.round(Math.abs(extent[0]-extent[1]), 2)


    # spectrum_resolution = () ->
    #   if JSV.isNumeric(spectrum_points()) && JSV.isNumeric(spectrum_sweep_width(true))
    #     spectrum_sweep_width(true) * 2 / spectrum_points()

    # display_spectrum_resolution = () ->
    #   if JSV.isNumeric(spectrum_resolution()) then d3.round(spectrum_resolution(), 3) + ' hz/pt' else 'NA'




  # hmdb_link = (id) ->
  #   '<a target="_blank" class="wishart-link-out" href="http://www.hmdb.ca/metabolites/' + id + '">' + id + ' <span class="glyphicon glyphicon-new-window"> </span></a>'


setup_selections = (sv) ->
  sv.on 'highlight-start', () ->
    element = sv.highlight.highlighted_element
    element.fill = element.color
  sv.on 'selection-add', () ->
    compounds = sv.selection.compounds()
    c.fill = c.color for c in compounds
  sv.on 'selection-remove', () ->
    compounds = sv.selection.compounds()
    c.fill = undefined for c in compounds
  sv.on 'selection-clear', () ->
    compounds = sv.selection.compounds()
    c.fill = undefined for c in compounds


$.fn.mySlideToggle = (show) ->
  if(show)
    $(this).slideDown()
  else
    $(this).slideUp()

$(window).load ->
  $('#spectra-viewer-2d').each ->
    self = $(this)
    spectra_data = self.data('spectra_data')
    sv = new JSV.SpectraViewer2D '#spectra-viewer-2d', {
        debug: false,
        width: self.parent().width(),
        height: "800"
      }
    sv.flash('Loading...')

    $.ajax
      dataType: 'text',
      url: spectra_data,
      success: (nmrml) ->
        if nmrml
          sv.add_nmrml_data(nmrml)

    $window = $(window)
    $window.resize () ->
      main_width = self.parent().width() - 1
      structure_width = $('#structure').width()
      cluster_section_width = main_width - structure_width - 4
      sv.resize(main_width, null, true)
#      $('#assignments').width(cluster_section_width)
#      $('#cluster-navigation').width(cluster_section_width - 2)
      sv.draw()