//////////////////////////////////////////////////////////////////////////////
// JSpectraViewer Setup
//////////////////////////////////////////////////////////////////////////////
window.JSpectraViewer = {};

JSpectraViewer.version = '0.1'

if (window.JSV === undefined) window.JSV = JSpectraViewer;
if (window.d3v7 === undefined) window.d3v7 = d3;

(function(JSV) {

  JSV.inherits = function(child, parent) {
    child.prototype = Object.create(parent.prototype);
  }

  JSV.initialize = function(){
    if (!d3v7.version.match(/^7\./g)) throw 'JSpectraViewer requires D3 v7'
    JSV._viewers = new JSV.SVSet();

    JSV.viewers = function(term) {
      return JSV._viewers.get(term);
    }

  }


})(JSpectraViewer);//////////////////////////////////////////////////////////////////////////////
// SpectraViewer
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * The SpectraViewer object controls how the viewer looks and behaves and
   * is the main object in JSV. Spectra are added to the SpectraViewer
   * using the [add_spectrum](#add_spectrum) method. A large number of options
   * can be set when creating a new viewer:
   *
   *  Option                | Default     | Description
   *  ----------------------|-------------------------------------------------
   *  title                 | _undefined_ | Title will appear above viewer if present
   *  id                    | jsv-1       | ID to select viewer
   *  width                 | 800         | Width of viewer in pixels
   *  height                | 400         | Height of viewer in pixels
   *  axis_x_title          | 'ppm'       | Label of x-axis
   *  axis_x_show           | true        | Show x-axis
   *  axis_x_grid           | true        | Show grid along x-axis
   *  axis_x_reverse        | true        | Reverse the direction of the x-axis
   *  axis_y_title          | 'Intensity' | Label of y-axis
   *  axis_y_show           | false       | Show y-axis
   *  axis_y_grid           | false       | Show grid along y-axis
   *  axis_y_lock           | false       | If true, locks axis to minimum y value. If a number locks the y-axis a percentage below 0 (e.g. 0.1 sets the y-axis minimum to 10% of the viewable y-axis scale below 0)
   *  min_boundaries        | _undefined_ | The minimum/maximum values for the x/y-axis. e.g. {x: [-1, 10], y: [0, 1]}. _Note_: _min_boundaries_ must be set, if only providing peak data.
   *  zoom_max              | 100         | The maximum zoom level
   *  zoombox_show          | true        | Show the zoombox
   *  zoombox_size          | 15          | Zoombox size as a percent of the viewer
   *  legend_show           | true        | Show the spectra legend
   *  drag_drop_load        | true        | If true, then bayesil JSON data can be dragged on to the viewer to load
   *  select                | {}          | See options for [SVSelection](SVSelection.js.html)
   *  highlight             | {}          | See options for [SVHighlighter](SVHighlighter.js.html)
   *  structure             | {}          | See options for [SVStructure](SVStructure.js.html)
   *  assignment_table      | {}          | See options for [SVAssignments](SVAssignments.js.html)
   *  cluster_navigation_id | _undefined_ | ID of div to place cluster navigation viewer (e.g. '#clust-nav')
   *
   *
   * @param {String} container_id The id of the element to contain the viewer.
   *   The contents of this element will be replaced with the viewer.
   * @param {Object} options Options to set up the viewer. Described below.
   * @return {SpectraViewer}
   */
  const SpectraViewer = function(container, options) {
    const sv = this;
    window.sv = this;
    this.container = d3v7.select(container);
    // Get options
    options = options || {};
    this.title          = options.title;
    this.width          = JSV.default_for(options.width, 800);
    this.height         = JSV.default_for(options.height, 400);
    this.tick_count     = JSV.default_for(options.tick_count, 10);
    this.x_tick_count     = JSV.default_for(options.x_tick_count, 10);
    this.tick_length    = JSV.default_for(options.tick_length, 10);
    this.tick_precision = JSV.default_for(options.tick_precision, 3);
    this.axis_x_tick_format = d3v7.format(JSV.default_for(options.axis_x_tick_format, '~g'));
    this.axis_y_tick_format = d3v7.format(JSV.default_for(options.axis_y_tick_format, '~g'));
    this.axis_x_title   = JSV.default_for(options.axis_x_title, 'ppm');
    this.axis_y_title   = JSV.default_for(options.axis_y_title, 'Intensity');
    this.axis_y_show    = JSV.default_for(options.axis_y_show, false);
    this.axis_x_show    = JSV.default_for(options.axis_x_show, true);
    this.axis_y_grid    = JSV.default_for(options.axis_y_grid, false);
    this.axis_x_grid    = JSV.default_for(options.axis_x_grid, false);
    // this.axis_y_reverse = JSV.default_for(options.axis_y_reverse, false);
    this.axis_x_reverse = JSV.default_for(options.axis_x_reverse, true);
    // If logical true, locks y axis to minimum y value
    // If a number locks the y axis to a percentage below 0 (e.g. 0.1 sets the y-axis min to 10% of the
    // viewable y axis scale below 0).
    this.axis_y_lock    = JSV.default_for(options.axis_y_lock, false);
    // min_boundaries must be set, if only providing peak data
    this.min_boundaries = options.min_boundaries; // Array example: {x: [-1, 10], y: [0, 1]}]
    this.zoom_max       = JSV.default_for(options.zoom_max, 100);
    this.zoombox_show   = JSV.default_for(options.zoombox_show, true);
    this.zoombox_size   = JSV.default_for(options.zoombox_size, 15); // percentage of viewer
    this.legend_show    = JSV.default_for(options.legend_show, true);
    // Controls the number of pixels saved for plots while zooming/draging
    // Increasing this number will descrease the number of pixels and thus speed up animations.
    // If no tolerance is provided, it will be calculated automatically from the spectra noise.
    this.simplify_tolerance = options.simplify_tolerance;
    // An array of debug keys to display or true to show all.
    this.debug = JSV.default_for(options.debug, false);
    this.debug_data = { time: {}, data: {}, drag: {}, zoom: {}, zbox: {} };
    // Set viewer ID
    const current_ids = JSV.viewers().map(function (viewer) {
      return viewer.id;
    });
    this.id = JSV.default_for(options.id, JSV.unique_id('jsv-', 1, current_ids));

    // Space required for axes
    this.axis_y_gutter = this.axis_y_show ? JSV.default_for(options.axis_y_gutter, 60) : 0;
    this.axis_x_gutter = this.axis_x_show ? JSV.default_for(options.axis_x_gutter, 50) : 0;

    // Delete contents of container and add title
    const header = this.title ? '<h3>' + this.title + '</h3>' : '';
    this.container.html(header)
        .style('width', this.width + 2 + 'px');

    this.sv_wrapper = this.container.append('div')
        .attr('class', 'sv-wrapper')
        .style('position', 'relative');

    // Add div to store the current key press
    this.container.append('div')
        .attr('class', 'sv-key-down')
        .style('display', 'none');

    // Create the viewer canvas
    // NOTE: anything drawn to the canvas must take the pixel ratio into account
    //       and should use the pixel() method.
    this.canvas = this.sv_wrapper.append("canvas")
        .attr("id", this.container.attr('id'))
        .attr("class", 'spectra-viewer ' + this.class)
        .style('border', '1px solid #DDD')
        .attr("width", this.width)
        .attr("height", this.height).node();

    // Check for canvas support
    if (!this.canvas.getContext) {
      this.container.html('<h3>Spectra Viewer requires Canvas, which is not supported by this browser.</h3>');
      throw('Canvas not supported');
    }

    // Get pixel ratio and upscale canvas depending on screen resolution
    // http://www.html5rocks.com/en/tutorials/canvas/hidpi/
    JSV.pixel_ratio = JSV.get_pixel_ratio(this.canvas);
    JSV.scale_resolution(this.canvas, JSV.pixel_ratio);

    // This would need to be adjusted in width setter
    this.font = this.adjust_font();
    this.axis_title_font = this.adjust_font(1, undefined, 'bold');
    // Set viewer context
    this.context = this.canvas.getContext('2d');

    // Add a placeholder function for browsers that don't have setLineDash()
    if (!this.context.setLineDash) {
      this.context.setLineDash = function () {}
    }

    // Set up scales for plot area
    this.scaled_height = JSV.pixel(this.height - this.axis_x_gutter);
    this.scaled_width  = JSV.pixel(this.width - this.axis_y_gutter);

    // Scale hold the current x/y scales
    // NOTE: to reverse axis, reverse the ranges
    this.scale = new JSV.SVScale();
    this.scale.y.range([this.scaled_height, 0]);
    // this.axis_x_reverse = false
    this.scale.x.range(this.x_range());

    // boundary hold the domain extents
    this.boundary = new JSV.SVScale();
    this.boundary.x.range(this.x_range());
    this.boundary.y.range([this.scaled_height, 0]);
    this.boundary.clamp(true);
    // Set minimun boundaries
    if (this.min_boundaries) {
      console.log("UPDATING min_boundaries", this.min_boundaries)
      this.boundary.update(this.min_boundaries);
      this.scale.update(this.min_boundaries);
    }

    // Initialize containers
    this._spectra = new JSV.SVSet();


    // Create SVG overlay
    this.svg = this.sv_wrapper.append('svg')
        .attr('width', this.width)
        .attr('height', this.height)
        .attr('class', 'svg-viewer')
        .style('position', 'absolute')
        .style('top', 0)
        .style('left', 0);

    // Set cursor for svg
    this.svg.style('cursor', 'all-scroll');

    //TODO: change drag drop options to: none, replace, add
    // This option must be set after the svg is set up
    this.drag_drop_load = JSV.default_for(options.drag_drop_load, true);

    this.initialize_zooming();
    this.initialize_dragging();
    sv.legend = new JSV.SVLegend(sv);

    // Add keyboard callback
    // d3v7.select(window)
    //   .on("keydown", function() { if (sv.contains_mouse) sv.keydown() })
    //   .on("keyup",   function() { sv.keyup() });
    d3v7.select(window)
        .on("keydown", sv.keydown)
        .on("keyup",   sv.keyup);

    this.svg.on('mousemove', function(event) {
      const pos = d3v7.pointer(event);
      sv.mx = sv.scale.x.invert(JSV.pixel(pos[0]))
      sv.my = sv.scale.y.invert(JSV.pixel(pos[1]))
      // console.log([sv.mx, sv.my]);
      sv.highlight.hover();
      sv.annotation.check_hover();
    });

    this.svg.on('mouseover', function() {
      sv.contains_mouse = true;
      // Remove focus from other elements (e.g. NMRLib editor), so that
      // pressing keys like 'a' or 's' do not continue typing in other elements.
      document.activeElement.blur();
    });

    this.svg.on('mouseout', function() {
      sv.contains_mouse = false;
    });

    // Setup SVEvents
    this.handlers = new JSV.SVEvents();
    this.on = this.handlers.on
    this.off = this.handlers.off
    this.trigger = this.handlers.trigger

    if (this.drag_drop_load) { this.initialize_drag_drop_load(); }

    this.selection = new JSV.SVSelection(this, JSV.default_for(options.select, {}));

    // Initialize Labels
    this.annotation = new JSV.SVAnnotation(sv);

    // Initialize Zoom Box
    this.zoombox = new JSV.ZoomBox(sv);
    this.zoombox.visible = this.zoombox_show;

    // This option must be set after sv_wrapper and zoombox
    this.highlight = new JSV.SVHighlighter(this, JSV.default_for(options.highlight, {}));

    // Initialize Menu
    this.menu = new JSV.SVMenu(sv);
    this.menu.open();

    // Initialize Help
    this.help = new JSV.SVHelp(sv);


    this.initial_settings();

    this.cluster_navigation_id = options.cluster_navigation_id;

    // Initialize Structure Viewer
    if (options.structure) {
      this.structure = new JSV.SVStructure(options.structure);
    }

    // Initialize Assignment Table
    if (options.assignment_table) {
      this.assignment_table = new JSV.SVAssignments(sv, options.assignment_table);
    }


    JSV._viewers.push(this);

    // Draw viewer
    this.draw();
  }

  SpectraViewer.prototype.toString = function() { return 'SpectraViewer' }

  /////////////////////////////////////////////////////////////////////////////
  // SpectraViewer Properties (setters/getters)
  /////////////////////////////////////////////////////////////////////////////
  Object.defineProperties(SpectraViewer.prototype, {
    'drag_drop_load': {
      get: function() { return this._drag_drop_load; },
      set: function(val) {
        this._drag_drop_load = val;
        if (this._drag_drop_load) {
          this.initialize_drag_drop_load();
        } else {
          this.svg.on('.dragndrop', null);
        }
      }
    },
    'cluster_navigation_id': {
      get: function() { return this._cluster_navigation_id; },
      set: function(val) {
        this._cluster_navigation_id = val;
        if (this._cluster_navigation_id) {
          this._cluster_navigation = new JSV.SVClusterNavigator(this, val);
        } else {
          if (this._cluster_navigation) {
            this._cluster_navigation.detach();
            this._cluster_navigation = undefined;
          }
        }
      }
    },
    'structure_viewer_id': {
      get: function() { return this._structure_viewer_id; },
      set: function(val) {
        this._structure_viewer_id = val;
        if (this._structure_viewer_id) {
          this._structure_viewer = new JSV.SVStructure(this, val);
        } else {
          if (this._structure_viewer) {
            this._structure_viewer.detach();
            this._structure_viewer = undefined;
          }
        }
      }
    }
    // 'zoombox_show': {
    //   get: function() { return this.zoombox.visible; },
    //   set: function(val) {
    //     this.zoombox.visible = val
    //   }
    // }
  });

  SpectraViewer.prototype.x_range = function(width) {
    width = width || this.width;
    const scaled_width = JSV.pixel(width - this.axis_y_gutter);
    return this.axis_x_reverse ?
        [scaled_width, 0] :
        [JSV.pixel(this.axis_y_gutter), JSV.pixel(width)];
  }

  /**
   * Resizes the SpectraViewer
   *
   * @param {Number} width New width
   * @param {Number} height New height
   * @param {Boolean} fast After resize, should the spectra be draw redrawn fast.
   */
  SpectraViewer.prototype.resize = function(width, height, fast) {
    this.width = width || this.width;
    this.height = height || this.height;

    this.container
        .style('width', this.width + 'px');
    d3v7.select(this.canvas)
        .attr('width', this.width)
        .attr('height', this.height);
    this.font = this.adjust_font();
    this.axis_title_font = this.adjust_font(1, undefined, 'bold');
    this.scaled_height = JSV.pixel(this.height - this.axis_x_gutter);
    this.scaled_width  = JSV.pixel(this.width - this.axis_y_gutter);
    this.scale.y.range([this.scaled_height, 0]);
    this.boundary.y.range([this.scaled_height, 0]);
    this.scale.x.range(this.x_range());
    this.boundary.x.range(this.x_range());

    this.svg.attr('width', this.width).attr('height', this.height);
    JSV.scale_resolution(this.canvas, JSV.pixel_ratio);
    this.zoombox.update();
    this.legend.update();
    this.draw(fast);
  }

  SpectraViewer.prototype.keyval = function() {
    return d3v7.select('.sv-key-down').html();
  }


  d3v7.selection.prototype.moveToFront = function() {
    return this.each(function() {
      this.parentNode.appendChild(this);
    });
  };

  SpectraViewer.prototype.keydown = function(event) {
    // This has to be done globally on all viewers
    if (event[this.zoom_brush_key]) {
      d3v7.selectAll('.sv-wrapper .svg-viewer').style('cursor', 'crosshair');
      // d3v7.selectAll('.zoom-brush').moveToFront();
    }
    d3v7.selectAll('.sv-key-down').html(event.keyCode);
    // console.log(d3v7.event);
  }

  SpectraViewer.prototype.keyup = function(event) {
    // 16 is shift
    if (event.keyCode == 16) {
      d3v7.selectAll('.sv-wrapper .svg-viewer').style('cursor', 'all-scroll');
      // d3v7.selectAll('.select-brush').moveToFront();
    }
    // Test external custom cursor
    // d3v7.selectAll('.sv-wrapper .svg-viewer').style('cursor', "url('http://www.javascriptkit.com/dhtmltutors/cursor-hand.gif'), 'pointer'");
    d3v7.selectAll('.sv-key-down').html('');
  }


  SpectraViewer.prototype.pixels_to_units_of_axis = function(axis, number_of_pixels) {
    const sv = this;
    return sv.scale[axis].invert(JSV.pixel(number_of_pixels)) - sv.scale[axis].invert(0);
  }


  // Change the zoom axis and zoom scale to the current zoom level if the axis is changing
  SpectraViewer.prototype.set_zoom_axis = function(zoom_y_key_down, event) {
    if (zoom_y_key_down) {
      if (this.zoom_axis != 'y') {
        this.zoom_axis = 'y';
        //this.zoom_behavior.scale(this.zoom_y);
        this.set_zoom_cursor(zoom_y_key_down);
      }
    } else {
      if (this.zoom_axis != 'x') {
        this.zoom_axis = 'x';
        //this.zoom_behavior.scale(this.zoom_x);
        this.set_zoom_cursor(zoom_y_key_down);
      }
    }
  }

  SpectraViewer.prototype.set_zoom_cursor = function(zoom_y_key_down) {
    if (zoom_y_key_down) {
      this.svg.style('cursor', 'ns-resize');
    } else {
      this.svg.style('cursor', 'ew-resize');
    }
  }

  // Zoom the spectra for the supplied axis based on the current zoom level.
  // - axis: either 'x' or 'y'
  // - zoom: zoom level for the axis
  SpectraViewer.prototype.scale_axis = function(axis, zoom_level, event) {
    // Calculate the difference between min and max values on axis after zooming
    const axis_diff = Math.abs(this.boundary[axis].domain()[0] - this.boundary[axis].domain()[1]) / zoom_level;
    // Value of axis at the mouse position
    const mouse_index = axis === 'x' ? 0 : 1;
    let value = this.scale[axis].invert(this.mouse(event, this.canvas)[mouse_index]);
    // Calculate the ratio the mouse position is along the axis
    let axis_ratio = (value - this.scale[axis].domain()[0]) / Math.abs(this.scale[axis].domain()[0] - this.scale[axis].domain()[1]);

    // Constrain y zooming so that y domain minimum stays constant
    if (axis == 'y') {
      axis_ratio = 0;
      value = this.scale.y.domain()[0];
    }

    // Initially set the zoomed domain using the axis ratio
    const domain = [value - (axis_diff * axis_ratio), value + (axis_diff * (1 - axis_ratio))];
    this.scale[axis].domain(domain);

    // Update the domain making sure they are within the boundaries
    this.set_domain(axis, domain);

    // Save zoom level
    // this['zoom_' + axis] = zoom_level

    // DEBUG INFO
    if (this.debug) {
      axis = axis.toUpperCase();
      this.debug_data.zoom['d' + axis]  = JSV.round(axis_diff);
      this.debug_data.zoom['v' + axis]  = JSV.round(value);
      this.debug_data.zoom['r' + axis]  = JSV.round(axis_ratio);
    }
  }

  // Get mouse position in the 'container' taking into account the pixel ratio
  SpectraViewer.prototype.mouse = function(event, container) {
    return d3v7.pointer(event, container).map(function(p) { return JSV.pixel(p); });
  }

  // Translates the spectra for the supplied axis.
  // - axis: either 'x' or 'y'
  // - translation: number of pixels to move the specta  on the axis
  SpectraViewer.prototype.translate_axis = function(axis, translation) {
    // Calculate new domain based on translation
    const domain = this.scale[axis].range().map(function (r) {
      return r - JSV.pixel(translation);
    }).map(this.scale[axis].invert);
    this.set_domain(axis, domain);
  }

  // http://bl.ocks.org/mbostock/5731979
  /**
   * Move from the current x,y domain to the supplied x/y domain using a transition.
   *
   * @param {Array} domains 2D array containing the min/max for the new X/Y domains:
   *   [ [Xmin, Xmax], [Ymin, Ymax] ]
   * @param {Number} duration Time of move transition in milliseconds
   */
  SpectraViewer.prototype.move_to = function(domains, duration) {
    const sv = this;
    duration = duration || 500;
    // Change Ymin to boundaries Ymin if the y axis is locked
    if (this.axis_y_lock !== false) {
      domains[1][0] = this.axis_y_lock_value(domains[1]);
    }
    // Flatten 2D domain arrays so they have the format: [Xmin, Xmax, Ymin, Ymax]
    let end_domains = [];
    let start_domains = [];
    end_domains = end_domains.concat.apply(end_domains, domains);
    start_domains = start_domains.concat
        .apply(start_domains, [sv.scale.x.domain(), sv.scale.y.domain()]);

    d3v7.select(this.canvas).transition()
        .duration(duration)
        .tween('move', function() {
          const interm_domains = d3v7.interpolateArray(start_domains, end_domains);
          return function(t) {
            sv.set_domain('x', [interm_domains(t)[0], interm_domains(t)[1]]);
            sv.set_domain('y', [interm_domains(t)[2], interm_domains(t)[3]]);
            sv.fast_draw();
          }
        }).on('end', function() { sv.full_draw(); });
    // Reset zoom axis
    sv.zoom_axis = '';
  }

  // Sets the domain (mininum and maximum value) for the supplied axis
  // after adjusting the domain to make sure it is within the boundaries of
  // the plot. Also adjusts the domain based on max_zoom.
  // - axis: either 'x' or 'y'
  // - domain: array with 2 elements, the min and max value for the domain
  SpectraViewer.prototype.set_domain = function(axis, domain) {
    const scale = this.scale;
    const boundary = this.boundary;


    // Determine boundary domain (difference between min and max values on the axis)
    const boundary_diff = Math.abs(boundary[axis].domain()[1] - boundary[axis].domain()[0]);
    // Determine visible domain (difference between min and max values on the axis)
    let domain_diff = Math.abs(domain[1] - domain[0]);

    // Optionally lock minimum domain value to minimum boundary value
    if ( (axis == 'y') && (this['axis_y_lock'] !== false) ) {
      domain[0] = this.axis_y_lock_value(domain);
      domain[1] = domain[0] + domain_diff;
    }

    // Changing the domain may change the zoom level
    // Check that the zoom is not above the max
    let new_zoom = boundary_diff / domain_diff;
    if (new_zoom > this.zoom_max) {
      new_zoom = this.zoom_max;
      const old_domain_diff = domain_diff;
      domain_diff = boundary_diff / new_zoom;
      // Center the domain
      const center = (domain[0] + domain[1]) / 2;
      domain[0] = center - (domain_diff / 2);
      domain[1] = domain[0] + domain_diff;
      if (this.debug) {
        this.debug_data.zoom['center-' + axis] = JSV.round(center);
        this.debug_data.zoom['domain-diff-' + axis] = JSV.round(domain_diff);
      }
    }
    // Check that the zoom is not below 1
    if (new_zoom < 1) {
      new_zoom = 1;
      domain_diff = boundary_diff / new_zoom;
      domain[0] = boundary[axis].min();
      domain[1] = boundary[axis].max();
    }
    if (this['zoom_' + axis] != new_zoom) {
      this['zoom_' + axis] = new_zoom;
      this.trigger('zoom');
    }

    // Check that domain is within the plot boundaries
    if (domain[0] < boundary[axis].domain()[0]) {
      domain[0] = boundary[axis].domain()[0];
      domain[1] = domain[0] + domain_diff;
    } else  if (domain[1] > boundary[axis].domain()[1]) {
      domain[1] = boundary[axis].domain()[1];
      domain[0] = domain[1] - domain_diff;
    }

    // Set domain
    this.scale[axis].domain(domain);
    this.trigger('domain-change');
  }

  SpectraViewer.prototype.axis_y_lock_value = function(domain) {
    if (this.axis_y_lock === true) {
      return this.boundary.y.domain()[0];
    } else {
      const domain_diff = Math.abs(domain[1] - domain[0]);
      return 0 - domain_diff * this.axis_y_lock;
    }
  }

  /**
   * Zoom the Viewer out completely
   */
  SpectraViewer.prototype.zoom_out_completely = function() {
    this.move_to([this.boundary.x.domain(), this.boundary.y.domain()])
    this.scale.x.domain(this.boundary.x.domain());
    this.scale.y.domain(this.boundary.y.domain());
    this.zoom_x = 1;
    this.zoom_y = 1;
  }


  /**
   * Adds a new spectrum to the viewer using the data provided. The _data_
   * can be a previously generated [Spectrum](Spectrum.js.html) or the data that will be
   * passed on to create a new [Spectrum](Spectrum.js.html).
   * Example Data:
   * ```javascript
   * data = {
   *   // ID to identify the spectrum. Defaults to spectrum_1, spectrum2, etc.
   *   id: 'fit',
   *   // Name displayed in legend. Defaults to the id.
   *   name: 'My Spectrum',
   *   // One of the following must be provided to create a Spectrum.
   *   // If more than one is provided, only one will be used and they are prioritized in
   *   // order shown. Details on each data type can be found below.
   *   compounds: compound_data,
   *   peak_list: peak_data,
   *   xy_line: xy_data,
   *   // Optional display options
   *   display: {color: 'blue', lineWidth: 3},
   *   // Optional meta data. Accessible through the spectrum's meta property.
   *   meta: {extra_info: 'This spectrum is really good!'},
   *   // Optional annotation data. Accessible through the spectrum's labels property
   *   // See below for details
   *   labels: labels
   * }
   *
   * // Describes one or more compounds
   * compound_data = [
   *   {
   *     id: 'HMDB01659',
   *     name: 'Acetone',
   *     concentration: '50',
   *     clusters: [
   *       {
   *         peaks: [
   *           { center: 2.22, amplitude: 0.6, width: 0.005 }
   *         ]
   *       }
   *     ]
   *   }
   * ]
   *
   * // A simple peak list
   * peak_data = [
   *   { center: 3.2, amplitude: 1.1, width: 0.05 },
   *   { center: 5.1, amplitude: 1.3, width: 0.05 },
   *   { center: 4.5, amplitude: 0.6, width: 0.04 }
   * ]
   *
   * // xy_data can be one of two formats:
   * // An object with x and y arrays [Better]
   * xy_data = {
   *   x: [1, 2, 3],
   *   y: [0.5, 0.4, 0.3]
   * }
   *
   * // To reduce JSON download size, previously loaded spectrum data can be used for the x array.
   * // In this case a spectrum with the id 'my_spectrum' should already be loaded in the viewer.
   * xy_data = {
   *   x: 'my_spectrum',
   *   y: [0.5, 0.4, 0.3]
   * }
   *
   *
   * // An array of x and y points [Deprecated]
   * xy_data = [
   *   {x: 1, y: 0.5}, {x: 2, y: 0.4}, {x: 3, y: 0.3}
   * ]
   *
   * // Annotation labels: array of label objects
   * labels = [
   *   { x: 1,
   *     y: 1,
   *     text: 'my_label',
   *     display: { vertical: true, font_size: 12 }, // optional
   *     meta: { something_useful: 'value' } // optional
   *   }
   * ]
   * ```
   * @param {Object} data Data required to create a new [Spectrum](Spectrum.js.html).
   *   Data can also be a Spectrum object, in which case, _display_ and _meta_ are ignored.
   * @param {Object} display Display options as described in [SVPath](SVPath.js.html)
   * @param {Object} meta User-defined properties
   */
  SpectraViewer.prototype.add_spectrum = function(data, display, meta) {
    const start_time = new Date().getTime();

    //FIXME: Should be done elsewhere and in a better way
    // Determine max number of points from current spectra
    // This is used to draw spectra from peak data
    let max_points = d3v7.max(this.spectra().map(function (spectrum) {
      return spectrum.xy_data.length()
    }));
    if (!max_points) max_points = 40000;
    this.points_per_ppm = max_points / (this.boundary.x.domain()[1] - this.boundary.x.domain()[0]);

    data.tolerance = JSV.default_for(data.tolerance, this.simplify_tolerance);
    data.number_of_points = JSV.default_for(data.number_of_points, max_points);
    data.min_x = JSV.default_for(data.min_x, this.boundary.x.domain()[0]);
    data.max_x = JSV.default_for(data.max_x, this.boundary.x.domain()[1]);
    const current_ids = this.all_spectra().map(function (spectrum) {
      return spectrum.name;
    });
    data.id = JSV.default_for(data.id, JSV.unique_id('Spectrum_', 1, current_ids));

    // If a spectrum ID is provided for x data, use that spectrums x data
    if ( data.xy_line && (typeof data.xy_line.x == 'string') ) {
      const x_id = data.xy_line.x;
      if ( this.spectra(x_id) ) {
        data.xy_line.x = this.spectra(x_id).xy_data.x;
      } else {
        throw new Error("There is no spectrum with the id provided by '" + data.id + "' xy_line.x:" + x_id);
      }
    }

    const spectrum = (data.toString() == 'Spectrum') ? data : new JSV.Spectrum(data, display, meta);
    spectrum._sv = this;

    this._spectra.push(spectrum);
    this.boundary.update(spectrum.xy_data);
    this.scale.update(spectrum.xy_data);
    this.zoombox.update();
    this.legend.update();
    if (this.debug) {
      this.debug_data.time[data.id + '~add'] = JSV.elapsed_time(start_time);
      this.debug_data.data[data.id + '~noise'] = spectrum.noise;
      this.debug_data.data[data.id + '~points-all'] = spectrum.xy_data.length();
      this.debug_data.data[data.id + '~points-simple'] = spectrum.simple_xy_data.length();
    }
    this.draw();
  }

  /**
   * Add the processed spectra and the compound fit generated by Bayesil
   * @param {Object} data Bayesil results.json data
   */
  SpectraViewer.prototype.add_bayesil_data = function(data) {
    this.add_spectrum( { id: 'Spectrum', xy_line: data.spectrum_xy }, undefined, data.meta);
    this.add_spectrum( JSV.convert_bayesil_data(data) );
  }

  /**
   * Remove all the spectra before adding the Bayesil _data_.
   *
   * @param {Object} data Bayesil results.json data
   * @param {Boolean} keep_domains If true, keep the Viewer at the same position on the x/y axes
   */
  SpectraViewer.prototype.replace_bayesil_data = function(data, keep_domains) {
    const sv = this;
    const saved_domains = [sv.scale.x.domain(), sv.scale.y.domain()];
    const viewer_was_not_empty = sv.spectra().length > 0;
    sv.remove_all_spectra();
    sv.add_bayesil_data(data);
    // if (json_parsed.fit_xy) {
    //   sv.add_spectrum({ id: 'FIT', xy_line: json_parsed.fit_xy}, {color: 'orange'});
    // }
    if (viewer_was_not_empty && keep_domains)  {
      sv.set_domain('x', saved_domains[0]);
      sv.set_domain('y', saved_domains[1]);
    }
    sv.draw();
  }

  /**
   * Read and add the data from an nmrML file. JSV will read the structure, assignments and spectrum xy data.
   * This method is not meant to read nmrML-Quant
   *
   * @param {Object} nmrml nmrML data in text format
   */
  SpectraViewer.prototype.add_nmrml_data = function(nmrml, source="experimental") {
    const xy_data = {x: [], y: []};
    if(!["experimental", "predicted", "simulated"].includes(source) ){
      console.error("Spectra source not recognized")
    } else {
      const sv = this;
      if (!window.X2JS) {
        console.log("X2JS needs to be installed to read nmrML: https://github.com/abdmob/x2js");
        return
      }


      // Add Spectrum XY plot
      const data_array_match = nmrml.match(/<spectrumDataArray.*compressed="(true|false)"[^]*>(.*)<\/spectrumDataArray>/);
      if (data_array_match) {
        const compressed = (data_array_match[1] == 'true');
        const encoded_data = data_array_match[2];
        const xy_array = JSV.convert_base64_to_float64(encoded_data, compressed);
        let maxY = xy_array[1];
        for (let i = 0; i < xy_array.length; i += 2) {
          xy_data.x.push(xy_array[i]);
          xy_data.y.push(xy_array[i + 1]);

          maxY = (xy_array[i + 1] > maxY) ? xy_array[i + 1] : maxY;
        }
        const scale_factor = 1 / maxY;
        xy_data.y = xy_data.y.map(function (x) {
          return x * scale_factor;
        });
        sv.add_spectrum({id: "Experimental", name: 'Experimental', display: {color: 'black', visible: true}, xy_line: xy_data});
      }
      else {
        console.log("No Spectrum Data Array found")
      }
      // Extract part of nmrML of interest
      const atom_assignment_match = nmrml.match(/<atomAssignment[^]*<\/atomAssignment>/);
      let atom_assignment;
      if (atom_assignment_match) {
        atom_assignment = atom_assignment_match[0];
      } else {
        console.log("Could not read nmrML (1). No <atomAssignment> tag found.");
        return
      }
      // Create structure
      const structure_match = atom_assignment.match(/<structure[^]*<\/structure>/);
      if (sv.structure && structure_match) {
        const name_match = atom_assignment.match(/<identifier.*name="(.*?)"/);
        if (name_match) {
          sv.structure.title = name_match[1];
        }
        sv.structure.read_nmrml(structure_match[0]);
      }
      // Determine NMR type (e.g. 13C or 1H)
      const nucleus_match = nmrml.match(/acquisitionNucleus.*name=\"(.*?)\"/);
      if (sv.assignment_table && nucleus_match) {
        const nucleus = nucleus_match[1];
        if (nucleus.match(/hydrogen/i) || nucleus.match(/1H/i)) {
          sv.assignment_table.nmr_nucleus = '1H';
        } else if (nucleus.match(/carbon/i) || nucleus.match(/13C/i)) {
          sv.assignment_table.nmr_nucleus = '13C';
        }
      }
      // Create Compound
      const x2js = new X2JS();
      const json = x2js.xml_str2json(atom_assignment);
      let name;
      try {
        name = json.atomAssignment.chemicalCompound.identifierList.identifier._name;
      } catch(err){
        console.error("nmrML contains no compound identifier")
        name = "Compound";
      }
      const compound = {name: name};
      const clusters = [];
      let multiplet_list = json.atomAssignment.atomAssignmentList.multiplet;
      if (multiplet_list) {
        multiplet_list = Array.isArray(multiplet_list) ? multiplet_list : [multiplet_list];

        // create a the scale for simulated peaks
        let all_amps = multiplet_list.map((multiplet) => {
          let peak_list = multiplet.peakList.peak
          peak_list = Array.isArray(peak_list) ? peak_list : [peak_list];
          return peak_list.map((p) =>  parseFloat(p._amplitude))
        });
        all_amps = all_amps.flat()
        let y_data_min = xy_data.y[0]
        let y_data_max = xy_data.y[0]
        xy_data.y.forEach(function(val){
          y_data_min = Math.min(val, y_data_min);
          y_data_max = Math.max(val, y_data_max);
        });
        let peaks_scaler = d3v7.scaleLinear()
            .domain([Math.min(0, Math.min(...all_amps)), Math.max(...all_amps)])
            .range([y_data_min, y_data_max])
        multiplet_list.forEach(function (multiplet, index) {
          const peaks = [];
          let peak_list = multiplet.peakList.peak;
          peak_list = Array.isArray(peak_list) ? peak_list : [peak_list];
          peak_list.forEach((peak) => {
            let amplitude = peaks_scaler(parseFloat(peak._amplitude))
            peak = {
              center: parseFloat(peak._center),
              amplitude: amplitude
            };
            peak.width = parseFloat(peak._width) || 0.005;
            peaks.push(peak);
          });
          // nmrml_cluster_index is used to find the right cluster when adding atoms below
          clusters.push({peaks: peaks, meta: {nmrml_cluster_index: index}});
        });
      }
      compound.clusters = clusters;
      let label = "Simulated";
      let visible = false;

      if(source === "predicted"){
        visible = true;
        label = "Predicted/Simulated"
      } else if (source === "simulated"){
        visible = true
      }

      sv.add_spectrum({id: "Simulated", name: label, display: {color: 'blue', visible: visible}, compounds: [compound]});
      // Read assignments
      if (sv.structure && multiplet_list) {
        const compound_clusters = sv.compounds(sv.compounds().length).clusters();
        multiplet_list.forEach( (multiplet, index) => {
          const compound_cluster = compound_clusters.find( (c) => {
            return c.meta.nmrml_cluster_index === index;
          });
          if (multiplet.atoms && compound_cluster) {
            let atom_ids = [];
            const atom_refs = multiplet.atoms._atomRefs;
            if (atom_refs && atom_refs.match(/\w/)) {
              atom_ids = atom_refs.trim().split(/\s+/);
            }
            atom_ids.forEach((atom_id, index) => atom_ids[index] = atom_id.replace(/\D/g,''))
            compound_cluster.atom_refs = atom_ids;
            compound_cluster._atoms.merge(sv.structure.atoms(atom_ids));

            switch (multiplet.multiplicity._name.toLowerCase()) {
              case 'singlet feature':
                compound_cluster.multiplet_type = 's';
                break;
              case 'doublet feature':
                compound_cluster.multiplet_type = 'd';
                break;
              case 'triplet feature':
                compound_cluster.multiplet_type = 't';
                break;
              case 'quartet feature':
                compound_cluster.multiplet_type = 'q';
                break;
              case 'doublet of doublets feature':
                compound_cluster.multiplet_type = 'dd';
                break;
              case 'doublet of doublet of doublets feature':
                compound_cluster.multiplet_type = 'ddd';
                break;
              case 'doublet of doublet of doublets of doublets feature':
                compound_cluster.multiplet_type = 'dddd';
                break;
              case 'triplet of doublets feature':
                compound_cluster.multiplet_type = 'td';
                break;
              case 'doublet of triplets feature':
                compound_cluster.multiplet_type = 'dt';
                break;
              case 'triplet of triplets feature':
                compound_cluster.multiplet_type = 'tt';
                break;
              case 'doublet of quartets feature':
                compound_cluster.multiplet_type = 'dq';
                break;
              case 'doublet of doublet of triplets feature':
                compound_cluster.multiplet_type = 'ddt';
                break;
              case 'doublet of triplet of doublets feature':
                compound_cluster.multiplet_type = 'dtd';
                break;
              case 'triplet of doublets of doublets feature':
                compound_cluster.multiplet_type = 'tdd';
                break;
              case 'quintet feature':
                compound_cluster.multiplet_type = 'quint';
                break;
              case 'multiplet feature':
                compound_cluster.multiplet_type = 'm';
                break;
            }

            // look elsewhere for the multiplicity
            if (!compound_cluster.hasOwnProperty('multiplet_type')){
              switch (multiplet.multiplicity._accession) {
                case 'NMR:1000194':
                  compound_cluster.multiplet_type = 's';
                  break;
                case 'NMR:1000184':
                  compound_cluster.multiplet_type = 'd';
                  break;
                case 'NMR:1000185':
                  compound_cluster.multiplet_type = 't';
                  break;
                case 'NMR:1000186':
                  compound_cluster.multiplet_type = 'q';
                  break;
                case 'NMR:1000192':
                  compound_cluster.multiplet_type = 'dd';
                  break;
                case 'NMR:1000195':
                  compound_cluster.multiplet_type = 'qunit';
                  break;
                case 'NMR:1000196':
                  compound_cluster.multiplet_type = 'dt';
                  break;
                case 'NMR:1000197':
                  compound_cluster.multiplet_type = 'td';
                  break;
                case 'NMR:1400305':
                  compound_cluster.multiplet_type = 'm';
                  break;
              }
            }
          }
        });
      }

      if(source !== 'experimental'){
        sv.remove_spectra("Experimental")
      }
      sv.spectra("Simulated").labels.update_peaks();
      sv.trigger('adjust-end') // updates Assignment table (NEED BETTER WAY)
    }
  }

  SpectraViewer.prototype.add_spin_matrix_data = function(spin_matrix_json){
    const sv = this;
    const json = JSON.parse(spin_matrix_json);

    const experimental_xy_data = json.spectrum.spectrum_xy;
    const fit_xy_data = json.fit.spectrum_xy;

    sv.add_spectrum({
      id: "Experimental",
      name: 'Experimental',
      display: {color: 'black', visible: true},
      xy_line: experimental_xy_data
    })

    sv.add_spectrum({
      id: "Fit",
      name: 'Fit',
      display: {color: 'blue', visible: true},
      xy_line: fit_xy_data
    })
  }

  SpectraViewer.prototype.add_ir_data = function(ir_xml){
    const sv = this;
    const x2js = new X2JS();
    const json = x2js.xml_str2json(ir_xml);

    if (json.hasOwnProperty("ms-ir")) {
      const data = json["ms-ir"];

      if (data.hasOwnProperty('x-units')) sv.axis_x_title = data["x-units"];
      if (data.hasOwnProperty('y-units')) sv.axis_y_title = data["y-units"];

      const spectrum_data = data["ms-ir-spectrum"].split('\n');
      const xy_data = {x: [], y: []};
      for(let coords of spectrum_data){
        coords =  coords.split(' ');
        xy_data["x"].push(parseFloat(coords[0]));
        xy_data["y"].push(parseFloat(coords[1]));
      }

      sv.add_spectrum({
        // max_x: 1900,
        // min_x: 580,
        id: "Experimental",
        name: 'Experimental',
        display: {color: 'black', visible: true},
        xy_line: xy_data
      });

      sv.full_draw();
    } else{
      console.error("Could not find IR data")
    }
  }

  /**
   * Remove all the spectrum with the specified _id_ from the viewer.
   *
   * @param {Number} id ID of spectrum to remove
   */
  SpectraViewer.prototype.remove_spectra = function(id) {
    this._spectra = new JSV.SVSet( this._spectra.filter(function(spectrum) { return spectrum.id != id; }) );
    this.zoombox.remove_spectra(id);
    this.legend.update();
  }

  /**
   * Remove all the spectra from the viewer
   */
  SpectraViewer.prototype.remove_all_spectra = function() {
    const sv = this;
    sv.selection.clear();
    this.all_spectra().forEach(function(spectrum) { sv.remove_spectra(spectrum.id) });
    sv.reset_boundaries();
  }

  SpectraViewer.prototype.reset_boundaries = function() {
    const sv = this;
    sv.selection.clear();
    sv.boundary.initialized = false
    sv.scale.initialized = false
    if (sv.min_boundaries) {
      console.log("Resetting boundaries to min_boundaries", sv.min_boundaries)
      sv.boundary.update(sv.min_boundaries);
      sv.scale.update(sv.min_boundaries);
    } else {
      sv.boundary.update({ x: [0, 1], y: [0, 1] })
      sv.scale.update({ x: [0, 1], y: [0, 1] })
    }
  }

  /**
   * Returns an [SVSet](SVSet.js.html) of Spectra or a single Spectrum from the viewer.
   * If no term is given, only **active** spectra are returned.
   * Otherwise all spectra will be search with the _term_.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Spectrum}
   */
  SpectraViewer.prototype.spectra = function(term) {
    if (term) return this._spectra.get(term);
    const active_spectra = new JSV.SVSet(this._spectra.filter(function (s) {
      return s.active;
    }));
    return active_spectra.get(term);
  }

  /**
   * Returns an [SVSet](SVSet.js.html) of Spectra or a single Spectrum from the viewer.
   * This metod is the same as [SpectraViewer.spectra](SpectraViewer.js.html#spectra), except that if no
   * _term_ is given all spectra are returned, including inactive ones.
   *
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Spectrum}
   */
  SpectraViewer.prototype.all_spectra = function(term) {
    return this._spectra.get(term);
  }

  /**
   * Returns an [SVSet](SVSet.js.html) of Compounds or a single Compound from all the Spectra in the viewer.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Compound}
   */
  SpectraViewer.prototype.compounds = function(term) {
    const compounds = new JSV.SVSet();
    let i = 0;
    const len = this._spectra.length;
    for (; i < len; i++) {
      compounds.merge(this._spectra[i].compounds());
    }
    return compounds.get(term);
  }

  /**
   * Returns an [SVSet](SVSet.js.html) of Clusters or a single Cluster from all the Spectra in the viewer.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Cluster}
   */
  SpectraViewer.prototype.clusters = function(term) {
    const clusters = new JSV.SVSet();
    let i = 0;
    const len = this._spectra.length;
    for (; i < len; i++) {
      console.log("Merging Clusters", this._spectra[i], typeof this._spectra[i]);
      console.log("Prototype", Object.getPrototypeOf(this._spectra[i]));
      clusters.merge(this._spectra[i].clusters());
    }
    return clusters.get(term);
  }

  /**
   * Returns an [SVSet](SVSet.js.html) of Peaks or a single Peak from all the Spectra in the viewer.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Peak}
   */
  SpectraViewer.prototype.peaks = function(term) {
    const peaks = new JSV.SVSet();
    let i = 0;
    const len = this._spectra.length;
    for (; i < len; i++) {
      peaks.merge(this._spectra[i].peaks());
    }
    return peaks.get(term);
  }

  /**
   * Clear the viewer canvas
   */
  SpectraViewer.prototype.clear = function() {
    this.context.clearRect(0, 0, JSV.pixel(this.width), JSV.pixel(this.height));
  }

  /**
   * Draws the contents of the viewer. This method should rarely be called directly.
   * Instead use, the convenience methods that call it:
   *
   *  - [SpectraViewer.full_draw](SpectraViewer.js.html#full_draw)
   *  - [SpectraViewer.calc_draw](SpectraViewer.js.html#calc_draw)
   *
   * @param {Boolean} fast If true, use simplified XY data if available
   * @param {Boolean} caculated If true, calculate XY data instead of using precomputed XY data.
   * @param {Number} pixel_skip If true, when calculating XY data skip this number of pixels between X values
   */
  SpectraViewer.prototype.draw = function(fast, calculated, pixel_skip) {
    const start_time = new Date().getTime();
    const sv = this;
    fast = fast || false
    const context = this.context;
    const scale = this.scale;
    this.clear();
    // Draw Grid lines
    if (this.axis_y_grid) this.draw_grid_lines('y');
    if (this.axis_x_grid) this.draw_grid_lines('x');
    this.spectra().forEach(function(spectrum) {
      // Draw compounds
      spectrum.compounds().draw(context, scale, fast, calculated, pixel_skip);
      // Draw clusters
      spectrum.clusters().draw(context, scale, fast, calculated, pixel_skip);
      // Draw peaks
      spectrum.peaks().draw(context, scale, fast, true, pixel_skip);
      // Draw spectra
      spectrum.draw(context, scale, fast, calculated, pixel_skip);
    });
    // Draw selection
    this.selection.draw(context, scale, fast, calculated, pixel_skip);
    this.zoombox.set_zoom_area(this.scale);
    // Draw labels
    this.annotation.draw();
    if (this.legend_show) this.legend.draw();
    this.draw_axes();
    this.selection.draw_bounds(context, scale);
    this.selection.draw_indicators(context, scale);
    if (this.debug) {
      this.debug_data.time['draw'] = JSV.elapsed_time(start_time);
      this.draw_debug(this.legend.bottom());
    }
  }

  /**
   * Draw using the full data (not simplified).
   * If zoomed in enough so that the number of points in the spectra
   * is less than the number of pixels in the viewer then then lines
   * will be calculated if possible.
   */
  SpectraViewer.prototype.full_draw = function() {
    const ppm_range = this.scale.x.domain()[1] - this.scale.x.domain()[0];
    const point_range = this.points_per_ppm * ppm_range;
    const pixel_range = Math.abs(this.scale.x.range()[1] - this.scale.x.range()[0]);
    ( (pixel_range / point_range) > 3 ) ? this.draw(false, true, 1) : this.draw(false, false);
  }

  /**
   * Draw by calculating the line shapes, using every visible pixel.
   */
  SpectraViewer.prototype.calc_draw = function() {
    this.draw(false, true, 1);
  }

  /**
   * Draw by calculating the line shapes, skipping several pixels
   */
  SpectraViewer.prototype.fast_calc_draw = function() {
    // this.draw(true, true, 2);
    this.draw(false, true, 2);
  }

  /**
   * Draw using simplified data if available. When zoomed in enough
   * the full data will be used.
   */
  SpectraViewer.prototype.fast_draw = function() {
    // Turn off fast draw when zoomed in 10x or more
    // TODO: determine at what level fast draw should be turned off based on the spectra length
    //       - this would be best if done at the spectrum level
    this.zoom_x < 10 ? this.draw(true) : this.draw();
    // this.draw(true);
  }

  /**
   * Flash a message on the center of the viewer.
   */
  SpectraViewer.prototype.flash = function(msg) {
    this.context.font = this.adjust_font(1.5);
    this.context.textAlign = 'center';
    this.context.textBaseline = 'center';
    const x = JSV.pixel(this.width) / 2;
    const y = JSV.pixel(this.height) / 2;
    this.context.fillText(msg, x, y);
  }

  // Draws any information in 'data' onto the left side of the viewer
  SpectraViewer.prototype.draw_debug = function(y) {
    if (!this.debug) return;
    const context = this.context;
    const data = this.debug_data;

    context.font = this.adjust_font(1, 'monospace');
    context.fillStyle = 'black';
    const line_height = JSV.pixel(18);
    y =  y || 0;
    let x;
    if (this.axis_x_reverse) {
      x = JSV.pixel(10);
      context.textAlign = 'left';
    } else {
      x = JSV.pixel(this.width - 10);
      context.textAlign = 'right';
    }
    const section_keys = this.debug === true ? Object.keys(data) : this.debug;
    let i = 0;
    section_keys.forEach(function(section_key) {
      const data_keys = Object.keys(data[section_key]);
      data_keys.forEach(function(data_key) {
        context.fillText((section_key + '|' + data_key + ': ' + data[section_key][data_key]), x, y + (line_height * i));
        i += 1;
      });
    })
  }

  // Returns a canvas font string adjusted to the size of the canvas
  SpectraViewer.prototype.adjust_font = function(font_factor, font_family, font_style) {
    font_factor = font_factor || 1;
    font_family = font_family || 'Sans-Serif';
    font_style  = font_style  || '';
    // ratio of default font size over default canvas width
    let ratio = 9 / 1400 * font_factor;
    let fontsize = ratio * JSV.pixel(this.width) + JSV.pixel(5);
    return font_style + ' ' + fontsize + 'pt ' + font_family;
  }

  // returns the font size used for a given font factor
  SpectraViewer.prototype.get_font_size = function(font_factor) {
    let ratio = 9 / 1400 * font_factor;
    return ratio * JSV.pixel(this.width) + JSV.pixel(5);
  }

  SpectraViewer.prototype.draw_axes = function() {
    const scale = this.scale;
    this.context.strokeStyle = 'black'; // axes color
    this.context.lineWidth = 1; // axes color
    this.context.setLineDash([1,0]);
    // Clear plot graphics from the X axis area
    const y_gutter = JSV.pixel(this.axis_y_gutter);
    const x_gutter = JSV.pixel(this.axis_x_gutter);
    // Clear plot graphics from the X axis area
    this.context.clearRect(scale.x.range_min(), scale.y.range_max(), scale.x.range_max() + y_gutter, x_gutter);
    // Clear plot graphics from the Y axis area
    const x = this.axis_x_reverse ? scale.x.range_max() : 0;
    this.context.clearRect(x, scale.y.range_min(), y_gutter, scale.y.range_max() + x_gutter);
    // Draw
    this.context.fillStyle = 'black';
    if (this.axis_y_show) this.draw_y_axis();
    if (this.axis_x_show) this.draw_x_axis();
  }

  SpectraViewer.prototype.draw_x_axis = function() {
    // Create a variable so we can access the chart in the tick drawing function
    const self = this;
    const tick_length = JSV.pixel(this.tick_length);
    const context = this.context;
    const scale = this.scale;

    // Draw axis line
    context.beginPath();
    context.moveTo(scale.x.range()[1], scale.y.range()[0]);
    context.lineTo(scale.x.range()[0], scale.y.range()[0]);
    context.stroke();
    // Set up text
    context.textAlign = 'center';
    context.textBaseline = 'top';
    context.font = this.font;
    // Draw ticks and text
    scale.x.ticks(this.x_tick_count).forEach(function(tick_x) {
      context.beginPath();
      context.moveTo(scale.x(tick_x), scale.y.range()[0]);
      context.lineTo(scale.x(tick_x), scale.y.range()[0] + tick_length);
      context.stroke();
      const rounded_tick_x = parseFloat(tick_x.toFixed(self.tick_precision));
      context.fillText(rounded_tick_x, scale.x(tick_x), scale.y.range()[0] + tick_length);
    });
    // Draw x label
    if (this.axis_x_title) {
      context.font = this.axis_title_font;
      const label_x = scale.x.range_diff() / 2;
      const label_y = scale.y.range()[0] + (3 * tick_length);
      context.fillText(this.axis_x_title, label_x, label_y)
    }
  }

  SpectraViewer.prototype.draw_y_axis = function() {
    const sv = this;
    const context = this.context;
    const scale = this.scale;
    const padding = 5;
    const direction = this.axis_x_reverse ? 1 : -1;
    const tick_length = direction * JSV.pixel(this.tick_length);
    const text_x = scale.x.range()[0] + (direction * JSV.pixel(this.tick_length + padding));
    const y_axis_elevation = 10;
    // Draw axis line
    context.beginPath();
    context.moveTo(scale.x.range()[0], scale.y.range()[1] - y_axis_elevation);
    context.lineTo(scale.x.range()[0], scale.y.range()[0] - y_axis_elevation);
    context.stroke();
    // Set up text
    context.textAlign = this.axis_x_reverse ? 'left' : 'right';
    context.textBaseline = 'middle';
    context.font = this.font;
    let max_label_width = 0;

    // Draw ticks and text
    scale.y.ticks(this.tick_count).forEach(function(tick_y) {
      context.beginPath();
      context.moveTo(scale.x.range()[0], scale.y(tick_y) - y_axis_elevation);
      context.lineTo(scale.x.range()[0] + tick_length, scale.y(tick_y) - y_axis_elevation);
      context.stroke();
      // context.fillText(tick_y.toFixed(decimal_places), text_x, scale.y(tick_y));
      context.fillText(sv.axis_y_tick_format(tick_y), text_x, scale.y(tick_y) - y_axis_elevation);
      if (sv.axis_y_title) {
        const label_width = Number(context.measureText(tick_y).width);
        if (label_width > max_label_width) max_label_width = label_width;
      }
    });
    // Draw y label
    if (this.axis_y_title) {
      const margin = JSV.pixel(4);
      const gutter = JSV.pixel(this.axis_y_gutter);
      const font_height = /(\d+\.?\d*)pt/.exec(this.axis_title_font)[1];
      // Width of text and ticks filling up the gutter
      const draw_width = max_label_width + Math.abs(tick_length) + Number(font_height) + margin + padding;
      if ( draw_width < gutter) {
        context.save();
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.font = this.axis_title_font;
        // Determine center point of label
        const label_x = scale.x.range()[0] + ((gutter - margin - Number(font_height) / 2) * direction);
        const label_y = scale.y.range()[0] / 2;
        // Move to center
        context.translate(label_x, label_y);
        // Rotate Label
        context.rotate(direction * Math.PI / 2);
        // Move back to origin
        context.translate(-label_x, -label_y);
        // Draw label
        context.fillText(this.axis_y_title, label_x, label_y);
        context.restore();
      }
    }
  }

  SpectraViewer.prototype.draw_grid_lines = function(axis) {
    const sv = this;
    const context = this.context;
    const orig_context = context;
    const scale = this.scale;
    scale[axis].ticks(this.tick_count).forEach(function(tick) {
      context.beginPath();
      if (axis == 'y') {
        context.moveTo(scale.x.range()[0], scale.y(tick));
        context.lineTo(scale.x.range()[1], scale.y(tick));
      } else if (axis == 'x') {
        context.moveTo(scale.x(tick), scale.y.range()[0]);
        context.lineTo(scale.x(tick), scale.y.range()[1]);
      }
      context.strokeStyle = 'rgb(210,210,210)'; // axes color
      context.lineWidth = JSV.pixel(0.5);
      // context.setLineDash([2,2]);
      context.stroke();
    });
    this.context = orig_context;
  }

  SpectraViewer.prototype.draw_y_axis2 = function() {
    const sv = this;
    const context = this.context;
    const scale = this.scale;
    const padding = 5;

    const min_x = d3v7.min(scale.x.range());
    const max_x = d3v7.max(scale.x.range());
    const min_y = d3v7.min(scale.y.range());
    const max_y = d3v7.max(scale.y.range());
    let line_x, text_x, tick_length;
    if (this.axis_y_left) {
      line_x = min_x;
      text_x = line_x - JSV.pixel(this.tick_length + padding);
      context.textAlign = 'right';
      tick_length = -JSV.pixel(this.tick_length);
    } else {
      line_x = max_x;
      text_x = line_x + JSV.pixel(this.tick_length + padding);
      context.textAlign = 'left';
      tick_length = JSV.pixel(this.tick_length);
    }

    // Draw axis line
    context.beginPath();
    context.moveTo(line_x, min_y);
    context.lineTo(line_x, max_y);
    context.stroke();
    // Set up text
    context.textBaseline = 'middle';
    context.font = this.font;
    let max_label_width = 0;
    // Draw ticks and text
    scale.y.ticks(this.tick_count).forEach(function(tick_y) {
      context.beginPath();
      context.moveTo(line_x, scale.y(tick_y));
      context.lineTo(line_x + tick_length, scale.y(tick_y));
      context.stroke();
      context.fillText(tick_y, text_x, scale.y(tick_y));
      if (sv.axis_y_title) {
        const label_width = Number(context.measureText(tick_y).width);
        if (label_width > max_label_width) max_label_width = label_width;
      }
    });
    // Draw y label
    if (this.axis_y_title) {
      const margin = JSV.pixel(4);
      const gutter = JSV.pixel(this.axis_y_gutter);
      const font_height = /(\d+)pt/.exec(this.axis_title_font)[1];
      // Width of text and ticks filling up the gutter
      const draw_width = max_label_width + tick_length + Number(font_height) + margin;
      if ( draw_width < gutter) {
        context.save();
        context.textAlign = 'center';
        context.textBaseline = 'hanging';
        context.font = this.axis_title_font;
        context.rotate(Math.PI / 2);
        const label_y = -scale.x.range()[0] - gutter + margin;
        const label_x = scale.y.range()[0] / 2;
        context.fillText(this.axis_y_title, label_x, label_y);
        context.restore();
      }
    }
  }

  // Returns a 2D array of the x and y domains [[Xmin, Xmax], [Ymin, Ymax]]
  // peaks: an array of peak objects
  // exact: -true: the exact boundaries of the peaks
  //               good for drawing a selection line
  //        -false: add a little to dimensions.
  //                Best for domains to zoom in on.
  SpectraViewer.prototype.get_peak_domains = function(peaks, exact) {
    const margin_percentage = 0.1;
    let min_peak = peaks[0];
    let max_peak = peaks[0];
    let min, max;
    for (let i = 1; i < peaks.length; i++) {
      const peak = peaks[i];
      if (peak.center < min_peak.center) min_peak = peak;
      if (peak.center > max_peak.center) max_peak = peak;
    }
    // Calculate sum of peaks to determine top
    const xy_for_peaks = JSV.xy_from_peaks(peaks, 1000, min_peak.center, max_peak.center);
    let top = d3v7.max(xy_for_peaks.y);
    if (exact) {
      min = min_peak.center - (min_peak.width / 2);
      max = max_peak.center + (max_peak.width / 2);
    } else {
      min = min_peak.center - (min_peak.width);
      max = max_peak.center + (max_peak.width);
      const margin = (max - min) * margin_percentage;
      min -= margin;
      max += margin;
      top += (top * margin_percentage);
    }
    return [[min, max], [0, top]];
  }

  SpectraViewer.prototype.move_to_compound = function(spectrum_id, compound_id, duration) {
    const spectrum = this.spectra(spectrum_id);
    const compound = spectrum && spectrum.compounds(compound_id);
    this.move_to_peaks(compound.peaks(), duration);
  }

  SpectraViewer.prototype.move_to_peaks = function(peaks, duration, scale) {
    scale = scale || 1;
    const domains = this.get_peak_domains(peaks);
    domains[1][1] = domains[1][1] * scale;
    const domain_diff = Math.abs(domains[0][1] - domains[0][0]);
    domains[0][0] = domains[0][0] - (domain_diff - domain_diff / scale);
    domains[0][1] = domains[0][1] + (domain_diff - domain_diff / scale);
    this.move_to(domains, duration);
  }

  // Extent is a 2D array [[x0, y0], [x1, y1]]
  // - x0 and y0 are the lower bounds of the extent
  // - x1 and y1 are the upper bounds of the extent
  SpectraViewer.prototype.set_domain_to_brush_extent = function(extent, use_pixel_ratio) {
    const domains = this.domains_from_brush_extent(extent, use_pixel_ratio);
    this.set_domain('x', domains[0]);
    this.set_domain('y', domains[1]);
  }

  SpectraViewer.prototype.domains_from_brush_extent = function(extent, use_pixel_ratio) {
    const x_domain = [extent[0][0], extent[1][0]];
    const y_domain = [extent[0][1], extent[1][1]];
    return [x_domain, y_domain];
  }

  SpectraViewer.prototype.elements = function(element_type, parent, possible_elements, visible_only) {
    const sv = this;
    let elements = possible_elements;
    if (!possible_elements) {
      parent = JSV.default_for(parent, sv);
      if (typeof parent == 'string') {
        parent = sv.spectra(parent);
      }
      if (!parent) {
        elements = new JSV.SVSet();
      } else if (element_type == 'peak') {
        elements = parent.peaks();
      } else if (element_type == 'cluster') {
        elements = parent.clusters();
      } else if (element_type == 'compound') {
        elements = parent.compounds();
      } else if (element_type == 'spectrum') {
        elements = parent.spectra();
      } else {
        elements = new JSV.SVSet();
      }
    }
    if (visible_only) {
      elements = new JSV.SVSet(elements.filter(function(e) { return e.visible; }));
    }
    return elements;
  }

  SpectraViewer.prototype.find_element_mouse_over = function(element_type, spectrum, possible_elements, visible_only) {
    const sv = this;
    let found_element;
    elements = sv.elements(element_type, spectrum, possible_elements, visible_only);
    // Calculate y value for each element at the mouse x position
    elements.forEach(function(element) {
      element.y_for_mouse_x = JSV.sum_of_peaks(sv.mx, element.peaks());
    });
    // Sort elements by y value
    elements.order_by('y_for_mouse_x');
    // Find first element
    for (let i = 0; i < elements.length; i++) {
      if (sv.my <= elements[i].y_for_mouse_x && sv.my >= 0) {
        found_element = elements[i];
        break;
      }
    }
    return found_element;
  }

  SpectraViewer.prototype.max_y_in_range = function(x_min, x_max) {


  }


  SpectraViewer.prototype.image = function(width, height) {
    width = width || this.width;
    height = height || this.height;

    // Save current settings
    const orig_context = this.context;
    const zoombox_visible = this.zoombox.visible;

    // Generate new context and scales
    const temp_canvas = d3v7.select('body').append('canvas')
        .attr('width', width)
        .attr('height', height)
        .node();

    JSV.scale_resolution(temp_canvas, JSV.pixel_ratio);
    this.context = temp_canvas.getContext('2d');

    const scaled_height = JSV.pixel(height - this.axis_x_gutter);
    this.scale.x.range(this.x_range(width));
    this.scale.y.range([scaled_height, 0]);

    this.zoombox.visible = false;

    // Generate image
    this.full_draw();
    image = temp_canvas.toDataURL();

    // Restore original settings
    this.scale.x.range(this.x_range());
    this.scale.y.range([this.scaled_height, 0]);
    this.context = orig_context;
    this.zoombox.visible = zoombox_visible;

    // Delete temp canvas
    d3v7.select(temp_canvas).remove();

    return image;
  }

  SpectraViewer.prototype.initial_settings = function() {
    const self = this;
    this.settings = new JSV.SVSettings(this, {
      title: 'JSV Settings',
      width: '250',
      height: '210',
      settings: [ {
        label: 'Grid lines [y-axis]',
        property: 'axis_y_grid',
        type: 'boolean'
      }, {
        label: 'Grid lines [x-axis]',
        property: 'axis_x_grid',
        type: 'boolean'
      }, {
        label: 'Show Zoombox',
        property: function(value) {
          if (arguments.length == 0) return self.zoombox.visible;
          self.zoombox.visible = value;
        },
        type: 'boolean'
      }, {
        label: 'Show Legend',
        property: 'legend_show',
        type: 'boolean'
      }
      ]
    });
  }


  JSV.SpectraViewer = SpectraViewer;

})(JSpectraViewer);//////////////////////////////////////////////////////////////////////////////
// SpectraViewer2D
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

    /**
     * The SpectraViewer2D object controls how the viewer looks and behaves and
     * is the main object in JSV. Spectra are added to the SpectraViewer2D
     * using the [add_spectrum](#add_spectrum) method. A large number of options
     * can be set when creating a new viewer:
     *
     *  Option                | Default     | Description
     *  ----------------------|-------------------------------------------------
     *  title                 | _undefined_ | Title will appear above viewer if present
     *  id                    | jsv-1       | ID to select viewer
     *  width                 | 800         | Width of viewer in pixels
     *  height                | 400         | Height of viewer in pixels
     *  axis_x_title          | 'ppm'       | Label of x-axis
     *  axis_x_show           | true        | Show x-axis
     *  axis_x_grid           | true        | Show grid along x-axis
     *  axis_x_reverse        | true        | Reverse the direction of the x-axis
     *  axis_y_title          | 'Intensity' | Label of y-axis
     *  axis_y_show           | false       | Show y-axis
     *  axis_y_grid           | false       | Show grid along y-axis
     *  axis_y_lock           | false       | If true, locks axis to minimum y value. If a number locks the y-axis a percentage below 0 (e.g. 0.1 sets the y-axis minimum to 10% of the viewable y-axis scale below 0)
     *  min_boundaries        | _undefined_ | The minimum/maximum values for the x/y-axis. e.g. {x: [-1, 10], y: [0, 1]}. _Note_: _min_boundaries_ must be set, if only providing peak data.
     *  zoom_max              | 25          | The maximum zoom-out level (units in standard deviation for peak detection)
     *  zoom_min              | 0.25        | The maximum zoom-in level (units in standard deviations for peak detection)
     *  select                | {}          | See options for [SVSelection](SVSelection.js.html)
     *  structure             | {}          | See options for [SVStructure](SVStructure.js.html)
     *  assignment_table      | {}          | See options for [SVAssignments](SVAssignments.js.html)
     *  cluster_navigation_id | _undefined_ | ID of div to place cluster navigation viewer (e.g. '#clust-nav')
     *
     *
     * @param {String} container_id The id of the element to contain the viewer.
     *   The contents of this element will be replaced with the viewer.
     * @param {Object} options Options to set up the viewer. Described below.
     * @return {SpectraViewer2D}
     */
    const SpectraViewer2D = function (container, options) {
        let sv = this;
        this.container = d3v7.select(container);
        // Get options
        options = options || {};
        this.title = options.title;
        this.width = JSV.default_for(options.width, 800);
        this.height = JSV.default_for(options.height, 400);
        this.tick_count = JSV.default_for(options.tick_count, 10);
        this.x_tick_count = JSV.default_for(options.x_tick_count, 10);
        this.tick_length = JSV.default_for(options.tick_length, 10);
        this.tick_precision = JSV.default_for(options.tick_precision, 3);
        this.axis_x_tick_format = d3v7.format(JSV.default_for(options.axis_x_tick_format, '~g'));
        this.axis_y_tick_format = d3v7.format(JSV.default_for(options.axis_y_tick_format, '~g'));
        this.axis_x_title = JSV.default_for(options.axis_x_title, 'ppm');
        this.axis_y_title = JSV.default_for(options.axis_y_title, 'ppm');
        this.axis_y_show = JSV.default_for(options.axis_y_show, 'ppm');
        this.axis_x_show = JSV.default_for(options.axis_x_show, true);
        this.axis_y_grid = JSV.default_for(options.axis_y_grid, false);
        this.axis_x_grid = JSV.default_for(options.axis_x_grid, false);
        this.axis_y_reverse = JSV.default_for(options.axis_y_reverse, false);
        this.axis_x_reverse = JSV.default_for(options.axis_x_reverse, true);
        // If logical true, locks y axis to minimum y value
        // If a number locks the y axis to a percentage below 0 (e.g. 0.1 sets the y-axis min to 10% of the
        // viewable y axis scale below 0).
        this.axis_y_lock = JSV.default_for(options.axis_y_lock, false);
        // min_boundaries must be set, if only providing peak data
        this.min_boundaries = options.min_boundaries; // Array example: {x: [-1, 10], y: [0, 1]}]
        this.zoom_max = JSV.default_for(options.zoom_max, 25);
        this.zoom_min = JSV.default_for(options.zoom_min, 0.25);
        // Controls the number of pixels saved for plots while zooming/draging
        // Increasing this number will descrease the number of pixels and thus speed up animations.
        // If no tolerance is provided, it will be calculated automatically from the spectra noise.
        this.simplify_tolerance = options.simplify_tolerance;
        // An array of debug keys to display or true to show all.
        this.debug = JSV.default_for(options.debug, false);
        this.debug_data = {time: {}, data: {}, drag: {}, zoom: {}, zbox: {}};
        // Set viewer ID
        const current_ids = JSV.viewers().map(function (viewer) {
            return viewer.id;
        });
        this.id = JSV.default_for(options.id, JSV.unique_id('jsv-', 1, current_ids));

        // Space required for axes
        this.axis_y_gutter = this.axis_y_show ? JSV.default_for(options.axis_y_gutter, 0.14 * this.width) : 0;
        this.axis_x_gutter = this.axis_x_show ? JSV.default_for(options.axis_x_gutter, 150) : 0;

        // Set initial threshold for peak detection
        this.threshold = 1;

        // Delete contents of container and add title
        const header = this.title ? '<h3>' + this.title + '</h3>' : '';
        this.container.html(header)
            .style('width', this.width + 2 + 'px');

        this.sv_wrapper = this.container.append('div')
            .attr('class', 'sv-wrapper')
            .style('position', 'relative');

        // Add div to store the current key press
        this.container.append('div')
            .attr('class', 'sv-key-down')
            .style('display', 'none');

        // Create the viewer canvas
        // NOTE: anything drawn to the canvas must take the pixel ratio into account
        //       and should use the pixel() method.
        this.canvas = this.sv_wrapper.append("canvas")
            .attr("id", this.container.attr('id'))
            .attr("class", 'spectra-viewer ' + this.class)
            .style('border', '1px solid #DDD')
            .attr("width", this.width)
            .attr("height", this.height).node();

        // Check for canvas support
        if (!this.canvas.getContext) {
            this.container.html('<h3>Spectra Viewer requires Canvas, which is not supported by this browser.</h3>');
            throw('Canvas not supported');
        }

        // Get pixel ratio and upscale canvas depending on screen resolution
        // http://www.html5rocks.com/en/tutorials/canvas/hidpi/
        JSV.pixel_ratio = JSV.get_pixel_ratio(this.canvas);
        JSV.scale_resolution(this.canvas, JSV.pixel_ratio);

        // This would need to be adjusted in width setter
        this.font = this.adjust_font();
        this.axis_title_font = this.adjust_font(1, undefined, 'bold');
        // Set viewer context
        this.context = this.canvas.getContext('2d');

        // Add a placeholder function for browsers that don't have setLineDash()
        if (!this.context.setLineDash) {
            this.context.setLineDash = function () {
            }
        }

        // Set up scales for plot area
        this.scaled_height = JSV.pixel(this.height - this.axis_x_gutter);
        this.scaled_width = JSV.pixel(this.width - this.axis_y_gutter);

        // Scale hold the current x/y scales
        // NOTE: to reverse axis, reverse the ranges
        this.scale = new JSV.SVScale();
        this.scale.y.range([0, this.scaled_height]);
        // this.axis_x_reverse = false
        this.scale.x.range(this.x_range());

        // boundary hold the domain extents
        this.boundary = new JSV.SVScale();
        this.boundary.x.range(this.x_range());
        this.boundary.y.range([this.scaled_height, 0]);
        this.boundary.clamp(true);

        // Set minimun boundaries
        if (this.min_boundaries) {
            this.boundary.update(this.min_boundaries);
            this.scale.update(this.min_boundaries);
        }

        // Initialize containers
        this._spectra = new JSV.SVSet();

        // make room for 1D nmr on top and left sides\
        this.shift_plot()


        // Create SVG overlay
        this.svg = this.sv_wrapper.append('svg')
            .attr('width', this.width)
            .attr('height', this.height)
            .attr('class', 'svg-viewer')
            .style('position', 'absolute')
            .style('top', 0)
            .style('left', 0);

        // Set cursor for svg
        this.svg.style('cursor', 'crosshair');

        // draw crosshairs
        this.svg.append("text").attr("id", "crosshairs_label").attr("x", "0.35em").attr("y", "1em")
        this.svg.append("line").attr("id", "crosshair_x")
            .style("stroke", "blue")
            .style("stroke-width", 1)
        this.svg.append("line").attr("id", "crosshair_y")
            .style("stroke", "blue")
            .style("stroke-width", 1)
        this.svg.on("mousemove", function (event) {
            const pos = d3v7.pointer(event, this);
            sv.mx = sv.scale.x.invert(JSV.pixel(pos[0]))
            sv.my = sv.scale.y.invert(JSV.pixel(pos[1]))

            sv.annotation.check_hover();

            d3v7.select('#crosshair_x')
                .attr("x1", 0)
                .attr("y1", pos[1])
                .attr("x2", sv.width)
                .attr("y2", pos[1]);
            d3v7.select('#crosshair_y')
                .attr("x1", pos[0])
                .attr("y1", 0)
                .attr("x2", pos[0])
                .attr("y2", sv.height);

            if (sv.debug) d3v7.select("#crosshairs_label").text("x: " + sv.mx + " y: " + sv.my);
        })

        this.zoom_rect = {
            'r': null,
            'origin': null,
            'translated_origin': null,
        }

        sv.svg.call(d3v7.drag()
            .on('start', this.drag_zoom().start)
            .on('drag', this.drag_zoom().drag)
            .on('end', this.drag_zoom().end)
        );

        this.initialize_zooming();

        d3v7.select(window)
            .on("keydown", sv.keydown)
            .on("keyup", sv.keyup);

        this.svg.on('mouseover', function () {
            sv.contains_mouse = true;
            // Remove focus from other elements (e.g. NMRLib editor), so that
            // pressing keys like 'a' or 's' do not continue typing in other elements.
            document.activeElement.blur();

        });

        this.svg.on('mouseout', function () {
            sv.contains_mouse = false;
        });

        // Setup SVEvents
        this.handlers = new JSV.SVEvents();
        this.on = this.handlers.on
        this.off = this.handlers.off
        this.trigger = this.handlers.trigger


        this.selection = new JSV.SVSelection(this, JSV.default_for(options.select, {}));

        // Initialize Labels
        // this.labels = new JSV.SVLabels(sv);
        this.annotation = new JSV.SVAnnotation(sv);

        // Initialize Menu
        this.menu = new JSV.SVMenu(sv);

        // Initialize Help
        this.help = new JSV.SVHelp(sv);

        this.cluster_navigation_id = options.cluster_navigation_id;

        // Initialize Structure Viewer
        if (options.structure) {
            this.structure = new JSV.SVStructure(options.structure);
        }

        // Initialize Assignment Table
        if (options.assignment_table) {
            console.log("Assignment Table 2D set to", this.assignment_table);
            this.assignment_table = new JSV.SVAssignments2D(sv, options.assignment_table);
        }


        JSV._viewers.push(this);

        // Draw viewer
        this.draw();
    };

    SpectraViewer2D.prototype.toString = function() { return 'SpectraViewer2D' }

    /////////////////////////////////////////////////////////////////////////////
    // SpectraViewer2D Properties (setters/getters)
    /////////////////////////////////////////////////////////////////////////////
    Object.defineProperties(SpectraViewer2D.prototype, {
        'cluster_navigation_id': {
            get: function() { return this._cluster_navigation_id; },
            set: function(val) {
                console.log("Setting cluster_navigation_id", val);
                this._cluster_navigation_id = val;
                if (this._cluster_navigation_id) {
                    this._cluster_navigation = new JSV.SVClusterNavigator(this, val);
                } else {
                    if (this._cluster_navigation) {
                        this._cluster_navigation.detach();
                        this._cluster_navigation = undefined;
                    }
                }
            }
        },
        'structure_viewer_id': {
            get: function() { return this._structure_viewer_id; },
            set: function(val) {
                this._structure_viewer_id = val;
                if (this._structure_viewer_id) {
                    this._structure_viewer = new JSV.SVStructure(this, val);
                } else {
                    if (this._structure_viewer) {
                        this._structure_viewer.detach();
                        this._structure_viewer = undefined;
                    }
                }
            }
        }
    });


    SpectraViewer2D.prototype.x_range = function(width) {
        width = width || this.width;
        const scaled_width = JSV.pixel(width - this.axis_y_gutter);
        return this.axis_x_reverse ?
            [scaled_width, 0] :
            [JSV.pixel(this.axis_y_gutter), JSV.pixel(width)];
    }

    /**
     * Resizes the SpectraViewer2D
     *
     * @param {Number} width New width
     * @param {Number} height New height
     * @param {Boolean} fast After resize, should the spectra be draw redrawn fast.
     */
    SpectraViewer2D.prototype.resize = function(width, height, fast) {
        this.shift_plot(true);
        this.width = width || this.width;
        this.height = height || this.height;

        this.container
            .style('width', this.width + 'px');
        d3v7.select(this.canvas)
            .attr('width', this.width)
            .attr('height', this.height);
        this.font = this.adjust_font();
        this.axis_title_font = this.adjust_font(1, undefined, 'bold');
        this.scaled_height = JSV.pixel(this.height - this.axis_x_gutter);
        this.scaled_width  = JSV.pixel(this.width - this.axis_y_gutter);
        this.scale.y.range([0, this.scaled_height]);
        this.boundary.y.range([this.scaled_height, 0]);
        this.scale.x.range(this.x_range());
        this.boundary.x.range(this.x_range());

        this.svg.attr('width', this.width).attr('height', this.height);
        JSV.scale_resolution(this.canvas, JSV.pixel_ratio);
        this.shift_plot();
        this.draw();
    }

    SpectraViewer2D.prototype.keyval = function() {
        return d3v7.select('.sv-key-down').html();
    }


    d3v7.selection.prototype.moveToFront = function() {
        return this.each(function() {
            this.parentNode.appendChild(this);
        });
    };

    SpectraViewer2D.prototype.keydown = function(event) {
        // This has to be done globally on all viewers
        if (event[this.zoom_brush_key]) {
            d3v7.selectAll('.sv-wrapper .svg-viewer').style('cursor', 'crosshair');
            // d3v7.selectAll('.zoom-brush').moveToFront();
        }
        d3v7.selectAll('.sv-key-down').html(event.keyCode);
        // console.log(d3v7.event);
    }

    SpectraViewer2D.prototype.keyup = function(event) {
        // 16 is shift
        if (event.keyCode == 16) {
            d3v7.selectAll('.sv-wrapper .svg-viewer').style('cursor', 'all-scroll');
            // d3v7.selectAll('.select-brush').moveToFront();
        }
        // Test external custom cursor
        // d3v7.selectAll('.sv-wrapper .svg-viewer').style('cursor', "url('http://www.javascriptkit.com/dhtmltutors/cursor-hand.gif'), 'pointer'");
        d3v7.selectAll('.sv-key-down').html('');
    }


    SpectraViewer2D.prototype.pixels_to_units_of_axis = function(axis, number_of_pixels) {
        const sv = this;
        return sv.scale[axis].invert(JSV.pixel(number_of_pixels)) - sv.scale[axis].invert(0);
    }


    // Change the zoom axis and zoom scale to the current zoom level if the axis is changing
    SpectraViewer2D.prototype.set_zoom_axis = function(zoom_y_key_down) {
        if (zoom_y_key_down) {
            if (this.zoom_axis != 'y') {
                this.zoom_axis = 'y';
                this.zoom_behavior.scale(this.zoom_y);
                this.set_zoom_cursor(zoom_y_key_down);
            }
        } else {
            if (this.zoom_axis != 'x') {
                this.zoom_axis = 'x';
                this.zoom_behavior.scale(this.zoom_x);
                this.set_zoom_cursor(zoom_y_key_down);
            }
        }
    }

    SpectraViewer2D.prototype.set_zoom_cursor = function(zoom_y_key_down) {
        if (zoom_y_key_down) {
            this.svg.style('cursor', 'ns-resize');
        } else {
            this.svg.style('cursor', 'ew-resize');
        }
    }

    // Zoom the spectra for the supplied axis based on the current zoom level.
    // - axis: either 'x' or 'y'
    // - zoom: zoom level for the axis
    SpectraViewer2D.prototype.scale_axis = function(axis, zoom_level) {
        // Calculate the difference between min and max values on axis after zooming
        const axis_diff = Math.abs(this.boundary[axis].domain()[0] - this.boundary[axis].domain()[1]) / zoom_level;
        // Value of axis at the mouse position
        const mouse_index = axis === 'x' ? 0 : 1;
        let value = this.scale[axis].invert(this.mouse(this.canvas)[mouse_index]);
        // Calculate the ratio the mouse position is along the axis
        let axis_ratio = (value - this.scale[axis].domain()[0]) / Math.abs(this.scale[axis].domain()[0] - this.scale[axis].domain()[1]);

        // Constrain y zooming so that y domain minimum stays constant
        if (axis == 'y') {
            axis_ratio = 0;
            value = this.scale.y.domain()[0];
        }

        // Initially set the zoomed domain using the axis ratio
        const domain = [value - (axis_diff * axis_ratio), value + (axis_diff * (1 - axis_ratio))];
        this.scale[axis].domain(domain);

        // Update the domain making sure they are within the boundaries
        this.set_domain(axis, domain);

        // Save zoom level
        // this['zoom_' + axis] = zoom_level

        // DEBUG INFO
        if (this.debug) {
            axis = axis.toUpperCase();
            this.debug_data.zoom['d' + axis]  = JSV.round(axis_diff);
            this.debug_data.zoom['v' + axis]  = JSV.round(value);
            this.debug_data.zoom['r' + axis]  = JSV.round(axis_ratio);
        }
    }

    // Get mouse position in the 'container' taking into account the pixel ratio
    SpectraViewer2D.prototype.mouse = function(container) {
        return d3v7.mouse(container).map(function(p) { return JSV.pixel(p); });
    }

    // Translates the spectra for the supplied axis.
    // - axis: either 'x' or 'y'
    // - translation: number of pixels to move the specta  on the axis
    SpectraViewer2D.prototype.translate_axis = function(axis, translation) {
        // Calculate new domain based on translation
        const domain = this.scale[axis].range().map(function (r) {
            return r - JSV.pixel(translation);
        }).map(this.scale[axis].invert);
        this.set_domain(axis, domain);
    }

    // Shifts canvas plot to make room for 1D NMR on sides
    SpectraViewer2D.prototype.shift_plot = function(reverse=false) {
        if(reverse){
            this.context.translate(-this.x_range()[0] * 0.08, -this.scaled_height * 0.12)
        } else {
            this.context.translate(this.x_range()[0] * 0.08, this.scaled_height * 0.12)
        }
    }

    SpectraViewer2D.prototype.absolute_to_translated_context = function(x, y) {
        let x_dif = this.x_range()[0] * 0.08;
        let y_dif = this.scaled_height * 0.12;

        let x_abs_min = this.scale.x.range()[1];
        let x_abs_max = this.scale.x.range()[0];
        let y_abs_min = this.scale.y.range()[0];
        let y_abs_max = this.scale.y.range()[1];

        x = x - x_dif;
        y = y - y_dif;

        x = Math.max(x_abs_min, Math.min(x_abs_max, x));
        y = Math.max(y_abs_min, Math.min(y_abs_max, y));
        return [this.scale.x.invert(x),this.scale.y.invert(y)];
    }

    // http://bl.ocks.org/mbostock/5731979
    /**
     * Move from the current x,y domain to the supplied x/y domain using a transition.
     *
     * @param {Array} domains 2D array containing the min/max for the new X/Y domains:
     *   [ [Xmin, Xmax], [Ymin, Ymax] ]
     * @param {Number} duration Time of move transition in milliseconds
     */
    SpectraViewer2D.prototype.move_to = function(domains, duration) {
        const sv = this;
        duration = duration || 500;
        // Change Ymin to boundaries Ymin if the y axis is locked
        if (this.axis_y_lock !== false) {
            domains[1][0] = this.axis_y_lock_value(domains[1]);
        }
        // Flatten 2D domain arrays so they have the format: [Xmin, Xmax, Ymin, Ymax]
        let end_domains = [];
        let start_domains = [];
        end_domains = end_domains.concat.apply(end_domains, domains);
        start_domains = start_domains.concat
            .apply(start_domains, [sv.scale.x.domain(), sv.scale.y.domain()]);

        d3v7.select(this.canvas).transition()
            .duration(duration)
            .tween('move', function() {
                const interm_domains = d3v7.interpolateArray(start_domains, end_domains);
                return function(t) {
                    sv.set_domain('x', [interm_domains(t)[0], interm_domains(t)[1]]);
                    sv.set_domain('y', [interm_domains(t)[2], interm_domains(t)[3]]);
                    sv.fast_draw();
                }
            }).on('end', function() { sv.full_draw(); });
        // Reset zoom axis
        sv.zoom_axis = '';
    }

    // Sets the domain (mininum and maximum value) for the supplied axis
    // after adjusting the domain to make sure it is within the boundaries of
    // the plot. Also adjusts the domain based on max_zoom.
    // - axis: either 'x' or 'y'
    // - domain: array with 2 elements, the min and max value for the domain
    SpectraViewer2D.prototype.set_domain = function(axis, domain) {
        const scale = this.scale;
        const boundary = this.boundary;


        // Determine boundary domain (difference between min and max values on the axis)
        const boundary_diff = Math.abs(boundary[axis].domain()[1] - boundary[axis].domain()[0]);
        // Determine visible domain (difference between min and max values on the axis)
        let domain_diff = Math.abs(domain[1] - domain[0]);

        // Optionally lock minimum domain value to minimum boundary value
        if ( (axis == 'y') && (this['axis_y_lock'] !== false) ) {
            domain[0] = this.axis_y_lock_value(domain);
            domain[1] = domain[0] + domain_diff;
        }

        // Changing the domain may change the zoom level
        // Check that the zoom is not above the max
        let new_zoom = boundary_diff / domain_diff;
        if (new_zoom > this.zoom_max) {
            new_zoom = this.zoom_max;
            const old_domain_diff = domain_diff;
            domain_diff = boundary_diff / new_zoom;
            // Center the domain
            const center = (domain[0] + domain[1]) / 2;
            domain[0] = center - (domain_diff / 2);
            domain[1] = domain[0] + domain_diff;
            if (this.debug) {
                this.debug_data.zoom['center-' + axis] = JSV.round(center);
                this.debug_data.zoom['domain-diff-' + axis] = JSV.round(domain_diff);
            }
        }
        // Check that the zoom is not below 1
        if (new_zoom < 1) {
            new_zoom = 1;
            domain_diff = boundary_diff / new_zoom;
            domain[0] = boundary[axis].min();
            domain[1] = boundary[axis].max();
        }
        if (this['zoom_' + axis] != new_zoom) {
            this['zoom_' + axis] = new_zoom;
            this.trigger('zoom');
        }

        // Check that domain is within the plot boundaries
        if (domain[0] < boundary[axis].domain()[0]) {
            domain[0] = boundary[axis].domain()[0];
            domain[1] = domain[0] + domain_diff;
        } else  if (domain[1] > boundary[axis].domain()[1]) {
            domain[1] = boundary[axis].domain()[1];
            domain[0] = domain[1] - domain_diff;
        }

        // Set domain
        this.scale[axis].domain(domain);
        this.trigger('domain-change');
    }

    SpectraViewer2D.prototype.axis_y_lock_value = function(domain) {
        if (this.axis_y_lock === true) {
            return this.boundary.y.domain()[0];
        } else {
            const domain_diff = Math.abs(domain[1] - domain[0]);
            return 0 - domain_diff * this.axis_y_lock;
        }
    }

    /**
     * Zoom the Viewer out completely
     */
    SpectraViewer2D.prototype.zoom_out_completely = function() {
        this.move_to([this.boundary.x.domain(), this.boundary.y.domain()])
        this.scale.x.domain(this.boundary.x.domain());
        this.scale.y.domain(this.boundary.y.domain());
        this.zoom_x = 1;
        this.zoom_y = 1;
    }

    SpectraViewer2D.prototype.add_spectrum_2d = function(data, display, meta) {
        //data.tolerance = JSV.default_for(data.tolerance, this.simplify_tolerance);
        //data.number_of_points = JSV.default_for(data.number_of_points, max_points);


        // calculate max and min values to update scaling
        let adjusted_y = data.json_data.y.map(v => data.json_data.y[0] - v)

        let x_min = Math.floor(Math.min(...data.json_data.x))
        let x_max = Math.ceil(Math.max(...data.json_data.x))
        let y_min = Math.floor(Math.min(...adjusted_y))
        let y_max = Math.ceil(Math.max(...adjusted_y))

        this.min_boundaries = {x: [x_min, x_max], y: [y_min, y_max]}

        this.boundary.update(this.min_boundaries);
        this.scale.update(this.min_boundaries);

        data.min_x = JSV.default_for(data.min_x, this.boundary.x.domain()[0]);
        data.max_x = JSV.default_for(data.max_x, this.boundary.x.domain()[1]);
        const current_ids = this.all_spectra().map(function (spectrum) {
            return spectrum.name;
        });
        data.id = JSV.default_for(data.id, JSV.unique_id('Spectrum2D_', 1, current_ids));

        const spectrum2D = (data.toString() == 'Spectrum2D') ? data : new JSV.Spectrum2D(data, display, meta);
        spectrum2D._sv = this;

        this._spectra.push(spectrum2D);
        this.boundary.update(spectrum2D.xyz_data);
        this.scale.update(spectrum2D.xyz_data);
        this.draw();
    }

    SpectraViewer2D.prototype.add_nmrml_data = function(nmrml){
        let sv = this;
            if (!window.X2JS) {
                console.error("X2JS needs to be installed to read nmrML: https://github.com/abdmob/x2js");
                return;
            }

            // Add Spectrum XYZ plot
            const coords_regex = {
                x: /<spectrumDataArray2dDirectDimensionF2.*compressed="(true|false)"[^]*>([^]+)<\/spectrumDataArray2dDirectDimensionF2>/,
                y: /<spectrumDataArray2dIndirectDimensionF1.*compressed="(true|false)"[^]*>([^]+)<\/spectrumDataArray2dIndirectDimensionF1>/,
                z: /<spectrumDataArray2dIntensities.*compressed="(true|false)"[^]*>([^]+)<\/spectrumDataArray2dIntensities>/
            };
            let xyz_data = {};

            Object.entries(coords_regex).forEach(function ([key, re]) {
                // find encoded data
                const data_array_match = nmrml.match(re);
                if (data_array_match) {
                    let compressed = (data_array_match[1] === 'true');
                    let encoded_data = data_array_match[2];
                    xyz_data[key] = Array.from(JSV.convert_base64_to_float64(encoded_data, compressed));
                } else {
                    console.error(`${key} data array not found`);
                }
            })


            if (Object.keys(coords_regex).every(key => key in xyz_data)) {
                sv.add_spectrum_2d({json_data: xyz_data});

                // Extract part of nmrML of interest
                const atom_assignment_match = nmrml.match(/<atomAssignment[^]*<\/atomAssignment>/);
                let atom_assignment;
                if (atom_assignment_match) {
                    atom_assignment = atom_assignment_match[0];
                } else {
                    console.log("Could not read nmrML (2). No <atomAssignment> tag found.");
                    return
                }
                // Create structure
                const structure_match = atom_assignment.match(/<structure[^]*<\/structure>/);
                if (sv.structure && structure_match) {
                    const name_match = atom_assignment.match(/<identifier.*name="(.*?)"/);
                    if (name_match) {
                        sv.structure.title = name_match[1];
                    }
                    sv.structure.read_nmrml(structure_match[0]);
                }
                //sv.assignment_table.nmr_nucleus = 'HSQC'; // only supported format for now
                // Create Compound
                const x2js = new X2JS();
                const json = x2js.xml_str2json(atom_assignment);
                const compound = {name: json.atomAssignment.chemicalCompound.identifierList.identifier._name};
                const clusters = [];
                let multiplet_list = json.atomAssignment.atomAssignmentList.multiplet;
                if (multiplet_list) {
                     multiplet_list = Array.isArray(multiplet_list) ? multiplet_list : [multiplet_list];
                     multiplet_list.forEach(function(multiplet, index) {
                        const peak = [multiplet._dir_dim_center, multiplet._indir_dim_center];
                        clusters.push( {peaks: peak,
                                        meta: {nmrml_cluster_index: index}});
                    });
                    compound.clusters = clusters;
                    // TODO: Add simulated 2D Spectrum to viewer
                    if (sv.structure && multiplet_list) {
                        const compound_clusters = sv.compounds(sv.compounds().length).clusters();
                        multiplet_list.forEach((multiplet, index) => {
                            const compound_cluster = compound_clusters.find((c) => {
                                return c.meta.nmrml_cluster_index === index;
                            });
                            if (multiplet.atoms && compound_cluster) {
                                let dir_dim_atom_ids = [];
                                let indir_dim_atom_ids = [];
                                const dir_dim_atom_refs = multiplet.atoms._dir_dim_atomRefs;
                                const indir_dim_atom_refs = multiplet.atoms._indir_dim_atomRefs;

                                if (dir_dim_atom_refs && dir_dim_atom_refs.match(/\w/) &&
                                    indir_dim_atom_refs && indir_dim_atom_refs.match(/\w/)) {
                                    dir_dim_atom_ids = dir_dim_atom_refs.trim().split(/\s+/);
                                    indir_dim_atom_ids = indir_dim_atom_refs.trim().split(/\s+/);
                                }

                                dir_dim_atom_ids.forEach((atom_id, index) => {
                                    dir_dim_atom_ids[index] = atom_id.replace(/\D/g, '');
                                });
                                indir_dim_atom_ids.forEach((atom_id, index) => {
                                    indir_dim_atom_ids[index] = atom_id.replace(/\D/g, '');
                                });

                                compound_cluster.dir_dim_atom_refs = dir_dim_atom_ids;
                                compound_cluster.indir_dim_atom_refs = indir_dim_atom_refs;
                                const atom_ids = dir_dim_atom_ids
                                    .concat(indir_dim_atom_ids.filter((id) => dir_dim_atom_ids.indexOf(id) < 0));
                                compound_cluster._atoms.merge(sv.structure.atoms(atom_ids));

                                switch (multiplet.multiplicity._name.toLowerCase()) {
                                    case 'singlet feature':
                                        compound_cluster.multiplet_type = 's';
                                        break;
                                    case 'doublet feature':
                                        compound_cluster.multiplet_type = 'd';
                                        break;
                                    case 'triplet feature':
                                        compound_cluster.multiplet_type = 't';
                                        break;
                                    case 'quartet feature':
                                        compound_cluster.multiplet_type = 'q';
                                        break;
                                    case 'doublet of doublets feature':
                                        compound_cluster.multiplet_type = 'dd';
                                        break;
                                    case 'doublet of doublet of doublets feature':
                                        compound_cluster.multiplet_type = 'ddd';
                                        break;
                                    case 'doublet of doublet of doublets of doublets feature':
                                        compound_cluster.multiplet_type = 'dddd';
                                        break;
                                    case 'triplet of doublets feature':
                                        compound_cluster.multiplet_type = 'td';
                                        break;
                                    case 'doublet of triplets feature':
                                        compound_cluster.multiplet_type = 'dt';
                                        break;
                                    case 'triplet of triplets feature':
                                        compound_cluster.multiplet_type = 'tt';
                                        break;
                                    case 'doublet of quartets feature':
                                        compound_cluster.multiplet_type = 'dq';
                                        break;
                                    case 'doublet of doublet of triplets feature':
                                        compound_cluster.multiplet_type = 'ddt';
                                        break;
                                    case 'doublet of triplet of doublets feature':
                                        compound_cluster.multiplet_type = 'dtd';
                                        break;
                                    case 'triplet of doublets of doublets feature':
                                        compound_cluster.multiplet_type = 'tdd';
                                        break;
                                    case 'quintet feature':
                                        compound_cluster.multiplet_type = 'quint';
                                        break;
                                    case 'multiplet feature':
                                        compound_cluster.multiplet_type = 'm';
                                        break;
                                }

                                // look elsewhere for the multiplicity
                                if (!compound_cluster.hasOwnProperty('multiplet_type')) {
                                    switch (multiplet.multiplicity._accession) {
                                        case 'NMR:1000194':
                                            compound_cluster.multiplet_type = 's';
                                            break;
                                        case 'NMR:1000184':
                                            compound_cluster.multiplet_type = 'd';
                                            break;
                                        case 'NMR:1000185':
                                            compound_cluster.multiplet_type = 't';
                                            break;
                                        case 'NMR:1000186':
                                            compound_cluster.multiplet_type = 'q';
                                            break;
                                        case 'NMR:1000192':
                                            compound_cluster.multiplet_type = 'dd';
                                            break;
                                        case 'NMR:1000195':
                                            compound_cluster.multiplet_type = 'qunit';
                                            break;
                                        case 'NMR:1000196':
                                            compound_cluster.multiplet_type = 'dt';
                                            break;
                                        case 'NMR:1000197':
                                            compound_cluster.multiplet_type = 'td';
                                            break;
                                        case 'NMR:1400305':
                                            compound_cluster.multiplet_type = 'm';
                                            break;
                                    }
                                }
                            }
                        });
                    }
                }
                sv.trigger('adjust-end'); // updates Assignment table (NEED BETTER WAY)
            } else {
                console.error("Could not read in Spectrum Data");
            }

    }

    /**
     * Remove all the spectrum with the specified _id_ from the viewer.
     *
     * @param {Number} id ID of spectrum to remove
     */
    SpectraViewer2D.prototype.remove_spectra = function(id) {
        this._spectra = new JSV.SVSet( this._spectra.filter(function(spectrum) { return spectrum.id != id; }) );
    }

    /**
     * Remove all the spectra from the viewer
     */
    SpectraViewer2D.prototype.remove_all_spectra = function() {
        const sv = this;
        sv.selection.clear();
        this.all_spectra().forEach(function(spectrum) { sv.remove_spectra(spectrum.id) });
        sv.reset_boundaries();
    }

    SpectraViewer2D.prototype.reset_boundaries = function() {
        const sv = this;
        sv.selection.clear();
        sv.boundary.initialized = false
        sv.scale.initialized = false
        if (sv.min_boundaries) {
            sv.boundary.update(sv.min_boundaries);
            sv.scale.update(sv.min_boundaries);
        } else {
            sv.boundary.update({ x: [0, 1], y: [0, 1] })
            sv.scale.update({ x: [0, 1], y: [0, 1] })
        }
    }

    /**
     * Returns an [SVSet](SVSet.js.html) of Spectra or a single Spectrum from the viewer.
     * If no term is given, only **active** spectra are returned.
     * Otherwise all spectra will be search with the _term_.
     * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
     * @return {SVSet|or|Spectrum}
     */
    SpectraViewer2D.prototype.spectra = function(term) {
        if (term) return this._spectra.get(term);
        const active_spectra = new JSV.SVSet(this._spectra.filter(function (s) {
            return s.active;
        }));
        return active_spectra.get(term);
    }

    /**
     * Returns an [SVSet](SVSet.js.html) of Spectra or a single Spectrum from the viewer.
     * This metod is the same as [SpectraViewer2D.spectra](SpectraViewer2D.js.html#spectra), except that if no
     * _term_ is given all spectra are returned, including inactive ones.
     *
     * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
     * @return {SVSet|or|Spectrum}
     */
    SpectraViewer2D.prototype.all_spectra = function(term) {
        return this._spectra.get(term);
    }

    /**
     * Returns an [SVSet](SVSet.js.html) of Compounds or a single Compound from all the Spectra in the viewer.
     * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
     * @return {SVSet|or|Compound}
     */
    SpectraViewer2D.prototype.compounds = function(term) {
        const compounds = new JSV.SVSet();
        let i = 0;
        const len = this._spectra.length;
        for (; i < len; i++) {
            compounds.merge(this._spectra[i].compounds());
        }
        return compounds.get(term);
    }

    /**
     * Returns an [SVSet](SVSet.js.html) of Clusters or a single Cluster from all the Spectra in the viewer.
     * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
     * @return {SVSet|or|Cluster}
     */
    SpectraViewer2D.prototype.clusters = function(term) {
        const clusters = new JSV.SVSet();
        let i = 0;
        const len = this._spectra.length;
        for (; i < len; i++) {
            console.log("Merging Clusters", this._spectra[i], typeof this._spectra[i]);
            console.log("Prototype", Object.getPrototypeOf(this._spectra[i]));
            clusters.merge(this._spectra[i].clusters());
        }
        return clusters.get(term);
    }

    /**
     * Clear the viewer canvas
     */
    SpectraViewer2D.prototype.clear = function(fast = false) {
        if(fast){
            this.context.clearRect(0, -1, JSV.pixel(this.width), JSV.pixel(this.height)+1);
            this.context.clearRect(-this.x_range()[0] * 0.08,
                JSV.pixel(this.height) - JSV.pixel(this.axis_x_gutter),
                JSV.pixel(this.width), JSV.pixel(this.height));
        } else {
            this.shift_plot(true)
            this.context.clearRect(0, 0, JSV.pixel(this.width), JSV.pixel(this.height));
            this.shift_plot()
        }
    }

    /**
     * Draws the contents of the viewer. This method should rarely be called directly.
     * Instead use, the convenience methods that call it:
     *
     *  - [SpectraViewer2D.full_draw](SpectraViewer2D.js.html#full_draw)
     *  - [SpectraViewer2D.calc_draw](SpectraViewer2D.js.html#calc_draw)
     *
     * @param {Boolean} fast If true, use simplified XY data if available
     * @param {Boolean} caculated If true, calculate XY data instead of using precomputed XY data.
     * @param {Number} pixel_skip If true, when calculating XY data skip this number of pixels between X values
     */
    SpectraViewer2D.prototype.draw = function(fast, calculated, pixel_skip) {
        const start_time = new Date().getTime();
        const sv = this;
        fast = fast || false
        const context = this.context;
        const scale = this.scale;
        this.clear(fast);
        // Draw Grid lines
        if (this.axis_y_grid) this.draw_grid_lines('y');
        if (this.axis_x_grid) this.draw_grid_lines('x');
        this.spectra().forEach(function(spectrum) {
                spectrum.draw(context, scale, fast, sv.scaled_height, sv.x_range(), sv.threshold);
        });
        this.draw_axes();

        if (this.debug) {
            this.debug_data.time['draw'] = JSV.elapsed_time(start_time);
        }
    }

    /**
     * Draw using the full data (not simplified).
     * If zoomed in enough so that the number of points in the spectra
     * is less than the number of pixels in the viewer then then lines
     * will be calculated if possible.
     */
    SpectraViewer2D.prototype.full_draw = function() {
        const ppm_range = this.scale.x.domain()[1] - this.scale.x.domain()[0];
        const point_range = this.points_per_ppm * ppm_range;
        const pixel_range = Math.abs(this.scale.x.range()[1] - this.scale.x.range()[0]);
        ( (pixel_range / point_range) > 3 ) ? this.draw(false, true, 1) : this.draw(false, false);
    }

    /**
     * Draw by calculating the line shapes, using every visible pixel.
     */
    SpectraViewer2D.prototype.calc_draw = function() {
        this.draw(false, true, 1);
    }

    /**
     * Draw by calculating the line shapes, skipping several pixels
     */
    SpectraViewer2D.prototype.fast_calc_draw = function() {
        // this.draw(true, true, 2);
        this.draw(false, true, 2);
    }

    /**
     * Draw using simplified data if available. When zoomed in enough
     * the full data will be used.
     */
    SpectraViewer2D.prototype.fast_draw = function() {
        // Turn off fast draw when zoomed in 10x or more
        // TODO: determine at what level fast draw should be turned off based on the spectra length
        //       - this would be best if done at the spectrum level
        this.draw(true);
        // this.draw(true);
    }

    /**
     * Flash a message on the center of the viewer.
     */
    SpectraViewer2D.prototype.flash = function(msg) {
        this.context.font = this.adjust_font(1.5);
        this.context.textAlign = 'center';
        this.context.textBaseline = 'center';
        const x = JSV.pixel(this.width) / 2;
        const y = JSV.pixel(this.height) / 2;
        this.context.fillText(msg, x, y);
    }

    // Draws any information in 'data' onto the left side of the viewer
    SpectraViewer2D.prototype.draw_debug = function(y) {
        if (!this.debug) return;
        const context = this.context;
        const data = this.debug_data;

        context.font = this.adjust_font(1, 'monospace');
        context.fillStyle = 'black';
        const line_height = JSV.pixel(18);
        y =  y || 0;
        let x;
        if (this.axis_x_reverse) {
            x = JSV.pixel(10);
            context.textAlign = 'left';
        } else {
            x = JSV.pixel(this.width - 10);
            context.textAlign = 'right';
        }
        const section_keys = this.debug === true ? Object.keys(data) : this.debug;
        let i = 0;
        section_keys.forEach(function(section_key) {
            const data_keys = Object.keys(data[section_key]);
            data_keys.forEach(function(data_key) {
                context.fillText((section_key + '|' + data_key + ': ' + data[section_key][data_key]), x, y + (line_height * i));
                i += 1;
            });
        })
    }

    // Returns a canvas font string adjusted to the size of the canvas
    SpectraViewer2D.prototype.adjust_font = function(font_factor, font_family, font_style) {
        font_factor = font_factor || 1;
        font_family = font_family || 'Sans-Serif';
        font_style  = font_style  || '';
        // ratio of default font size over default canvas width
        const ratio = 9 / 1400 * font_factor;
        const fontsize = ratio * JSV.pixel(this.width) + JSV.pixel(5);
        return font_style + ' ' + fontsize + 'pt ' + font_family;
    }

    SpectraViewer2D.prototype.draw_axes = function() {
        const scale = this.scale;
        this.context.strokeStyle = 'black'; // axes color
        this.context.lineWidth = 1; // axes color
        this.context.setLineDash([1,0]);
        // Clear plot graphics from the X axis area
        const y_gutter = JSV.pixel(this.axis_y_gutter);
        const x_gutter = JSV.pixel(this.axis_x_gutter);
        // Clear plot graphics from the X axis area
        this.context.clearRect(scale.x.range_min(), scale.y.range_max(), scale.x.range_max() + y_gutter, x_gutter);
        // Clear plot graphics from the Y axis area
        const x = this.axis_x_reverse ? scale.x.range_max() : 0;
        this.context.clearRect(x, scale.y.range_min(), y_gutter, scale.y.range_max() + x_gutter);
        // Draw
        this.context.fillStyle = 'black';
        if (this.axis_y_show) this.draw_y_axis();
        if (this.axis_x_show) this.draw_x_axis();
    }

    SpectraViewer2D.prototype.draw_x_axis = function() {
        // Create a variable so we can access the chart in the tick drawing function
        const self = this;
        const tick_length = JSV.pixel(this.tick_length);
        const context = this.context;
        const scale = this.scale;

        // Draw axis line
        context.beginPath();
        context.moveTo(scale.x.range()[1], scale.y.range()[1]);
        context.lineTo(scale.x.range()[0], scale.y.range()[1]);
        context.stroke();
        // Set up text
        context.textAlign = 'center';
        context.textBaseline = 'top';
        context.font = this.font;
        // Draw ticks and text
        scale.x.ticks(this.x_tick_count).forEach(function(tick_x) {
            context.beginPath();
            context.moveTo(scale.x(tick_x), scale.y.range()[1]);
            context.lineTo(scale.x(tick_x), scale.y.range()[1] + tick_length);
            context.stroke();
            let rounded_tick_x = +tick_x.toFixed(self.tick_precision);
            context.fillText(self.axis_x_tick_format(rounded_tick_x), scale.x(tick_x), scale.y.range()[1] + tick_length);
        });
        // Draw x label
        if (this.axis_x_title) {
            context.font = this.axis_title_font;
            const label_x = scale.x.range_diff() / 2;
            const label_y = scale.y.range()[1] + (3 * tick_length);
            context.fillText(this.axis_x_title, label_x, label_y)
        }
    }

    SpectraViewer2D.prototype.draw_y_axis = function() {
        const sv = this;
        const context = this.context;
        const scale = this.scale;
        const padding = 5;
        const direction = this.axis_x_reverse ? 1 : -1;
        const tick_length = direction * JSV.pixel(this.tick_length);
        const text_x = scale.x.range()[0] + (direction * JSV.pixel(this.tick_length + padding));
        // Draw axis line
        context.beginPath();
        context.moveTo(scale.x.range()[0], scale.y.range()[1]);
        context.lineTo(scale.x.range()[0], scale.y.range()[0]);
        context.stroke();
        // Set up text
        context.textAlign = this.axis_x_reverse ? 'left' : 'right';
        context.textBaseline = 'middle';
        context.font = this.font;
        let max_label_width = 0;

        // Draw ticks and text
        let y_ticks = scale.y.ticks(this.tick_count);
        y_ticks.forEach(function(tick_y) {
            context.beginPath();
            context.moveTo(scale.x.range()[0], scale.y(tick_y));
            context.lineTo(scale.x.range()[0] + tick_length, scale.y(tick_y));
            context.stroke();
            context.fillText(sv.axis_y_tick_format(tick_y), text_x, scale.y(tick_y));
            if (sv.axis_y_title) {
                const label_width = Number(context.measureText(tick_y).width);
                if (label_width > max_label_width) max_label_width = label_width;
            }
        });
        // Draw y label
        if (this.axis_y_title) {
            const margin = JSV.pixel(4);
            const gutter = JSV.pixel(this.axis_y_gutter);
            const font_height = /(\d+\.?\d*)pt/.exec(this.axis_title_font)[1];
            // Width of text and ticks filling up the gutter
            const draw_width = max_label_width + Math.abs(tick_length) + Number(font_height) + margin + padding;
            if ( draw_width < gutter) {
                context.save();
                context.textAlign = 'center';
                context.textBaseline = 'middle';
                context.font = this.axis_title_font;
                // Determine center point of label
                const label_x = scale.x.range()[0] + ((7 * tick_length) * direction);
                const label_y = scale.y.range()[1] / 2;
                // Move to center
                context.translate(label_x, label_y);
                // Rotate Label
                context.rotate(direction * Math.PI / 2);
                // Move back to origin
                context.translate(-label_x, -label_y);
                // Draw label
                context.fillText(this.axis_y_title, label_x, label_y);
                context.restore();
            }
        }
    }

    SpectraViewer2D.prototype.draw_grid_lines = function(axis) {
        const sv = this;
        const context = this.context;
        const orig_context = context;
        const scale = this.scale;
        scale[axis].ticks(this.tick_count).forEach(function(tick) {
            context.beginPath();
            if (axis == 'y') {
                context.moveTo(scale.x.range()[0], scale.y(tick));
                context.lineTo(scale.x.range()[1], scale.y(tick));
            } else if (axis == 'x') {
                context.moveTo(scale.x(tick), scale.y.range()[0]);
                context.lineTo(scale.x(tick), scale.y.range()[1]);
            }
            context.strokeStyle = 'rgb(210,210,210)'; // axes color
            context.lineWidth = JSV.pixel(0.5);
            // context.setLineDash([2,2]);
            context.stroke();
        });
        this.context = orig_context;
    }

    SpectraViewer2D.prototype.elements = function(element_type, parent, possible_elements, visible_only) {
        const sv = this;
        let elements = possible_elements;
        if (!possible_elements) {
            parent = JSV.default_for(parent, sv);
            if (typeof parent == 'string') {
                parent = sv.spectra(parent);
            }
            if (!parent) {
                elements = new JSV.SVSet();
            } else if (element_type == 'peak') {
                elements = parent.peaks();
            } else if (element_type == 'cluster') {
                elements = parent.clusters();
            } else if (element_type == 'compound') {
                elements = parent.compounds();
            } else if (element_type == 'spectrum') {
                elements = parent.spectra();
            } else {
                elements = new JSV.SVSet();
            }
        }
        if (visible_only) {
            elements = new JSV.SVSet(elements.filter(function(e) { return e.visible; }));
        }
        return elements;
    }


    SpectraViewer2D.prototype.image = function(width, height) {
        width = width || this.width;
        height = height || this.height;

        // Save current settings
        const orig_context = this.context;

        // Generate new context and scales
        const temp_canvas = d3v7.select('body').append('canvas')
            .attr('width', width)
            .attr('height', height)
            .node();

        JSV.scale_resolution(temp_canvas, JSV.pixel_ratio);
        this.context = temp_canvas.getContext('2d');

        const scaled_height = JSV.pixel(height - this.axis_x_gutter);
        this.scale.x.range(this.x_range(width));
        this.scale.y.range([scaled_height, 0]);

        // Generate image
        this.full_draw();
        image = temp_canvas.toDataURL();

        // Restore original settings
        this.scale.x.range(this.x_range());
        this.scale.y.range([this.scaled_height, 0]);
        this.context = orig_context;

        // Delete temp canvas
        d3v7.select(temp_canvas).remove();

        return image;
    }


    JSV.SpectraViewer2D = SpectraViewer2D;

})(JSpectraViewer);//////////////////////////////////////////////////////////////////////////////
// Initializing Zooming, Dragging, Brushing, Drag-n-Drop, AdjustFit
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * Initialize Spectra Viewer Dragging.
   */
  JSV.SpectraViewer.prototype.initialize_dragging = function() {
    const sv = this;
    sv.drag_event = false;
    sv.drag_behavior = d3v7.drag()
        .on('start', dragstart)
        .on('drag',      dragging)
        .on('end',   dragend);
    sv.svg.call(sv.drag_behavior);

    function dragstart(event) {
      sv.drag_event = true;
      event.sourceEvent.preventDefault(); // Prevent text cursor
      sv.svg.style('cursor', 'all-scroll');
      // Store current selection to restore it if dragging does occur
      // current_selected_elements = sv.selection.elements();
      sv.trigger('drag-start');
    }

    function dragging(event) {
      const start_time = new Date().getTime();
      // Restore selected peaks
      // if (sv.selection.empty()) sv.selection._elements = current_selected_elements;
      sv.translate_axis('x', event.dx);
      sv.translate_axis('y', event.dy);
      sv.trigger('drag');
      sv.fast_draw();

      // DEBUG INFO
      if (sv.debug) {
        console.log('dragging');
        sv.debug_data.time['drag'] = JSV.elapsed_time(start_time);
        sv.debug_data.drag['dX'] = JSV.round(event.dx);
        sv.debug_data.drag['dY'] = JSV.round(event.dy);
        sv.debug_data.drag['zX'] = JSV.round(sv.zoom_x);
        sv.debug_data.drag['zY'] = JSV.round(sv.zoom_y);
      }
    }

    function dragend() {
      sv.drag_event = false;
      sv.trigger('drag-end');
      sv.full_draw();
    }
  }

  /**
   * Initialize Spectra Viewer Zooming.
   */
  JSV.SpectraViewer.prototype.initialize_zooming = function() {
    const sv = this;
    sv.zoom_behavior = d3v7.zoom()
        .scaleExtent([1, sv.zoom_max])
        .on('start', zoomstart)
        .on('zoom',      zooming)
        .on('end',   zoomend);
    sv.svg.call(sv.zoom_behavior)
        .on('mousedown.zoom', null) // Remove click zoom behaviour
        .on('dblclick.zoom', null); // Remove double click zoom behaviour
    // Set starting zoom levels and axis
    sv.zoom_x = 1;
    sv.zoom_y = 1;
    sv.zoom_axis = 'x';
    // Possigle keys: 'altKey', 'shiftKey', 'ctrlKey', 'metaKey'
    // sv.zoom_y_key = 'shiftKey';
    sv.zoom_y_key = 'altKey';

    function zoomstart(event) {
      if (sv.drag_event) return; // Spectra dragging takes precedence over zooming
      const zoom_y_key_down = event.sourceEvent[sv.zoom_y_key];
      sv.set_zoom_axis(zoom_y_key_down, event);
      sv.set_zoom_cursor(zoom_y_key_down);
      sv.trigger('zoom-start');
    }

    function zooming(event) {
      if (sv.drag_event) return; // Spectra dragging takes precedence over zooming
      const start_time = new Date().getTime();

      // Set up which axis to zoom
      sv.set_zoom_axis(event.sourceEvent[sv.zoom_y_key], event);

      // Scale axes based on current level
      sv.scale_axis(sv.zoom_axis, event.transform.k, event);

      // sv.trigger('zoom');
      sv.fast_draw();

      // DEBUG INFO
      if (sv.debug) {
        console.log('zooming');
        sv.debug_data.time['zoom'] = JSV.elapsed_time(start_time);
        sv.debug_data.zoom['zX'] = JSV.round(sv.zoom_x);
        sv.debug_data.zoom['zY'] = JSV.round(sv.zoom_y);
      }
    }

    function zoomend() {
      if (sv.drag_event) return; // Spectra dragging takes precedence over zooming
      sv.svg.style('cursor', 'all-scroll');
      sv.trigger('zoom-end');
      sv.full_draw();
    }
  }

  /**
   * Initialize Spectra Viewer Drag-n-Drop.
   */
  JSV.SpectraViewer.prototype.initialize_drag_drop_load = function() {
    const sv = this;
    this.svg.on('dragleave.dragndrop', function(event) {
      event.preventDefault();
      event.stopPropagation();
      sv.draw();
    });

    this.svg.on('dragover.dragndrop', function(event) {
      event.preventDefault();
      event.stopPropagation();
      sv.draw();
      sv.flash('Drop Bayesil JSON File...');
    });

    this.svg.on('drop.dragndrop', function(event) {
      event.preventDefault();
      event.stopPropagation();
      sv.draw();
      const file = event.dataTransfer.files[0];
      // console.log(file.type)
      sv.flash('Loading "' + file.name + '"...');
      const reader = new FileReader();
      sv.json_file = file;
      reader.onload = function() {
        const json_obj = reader.result;
        try {
          const json_parsed = JSON.parse(json_obj);
          // TODO: add file and error checking
          sv.replace_bayesil_data(json_parsed, true);
          sv.trigger('drop');
        } catch (e) {
          sv.draw();
          sv.flash('Could not read file: ' + e.message);
        }
      }
      reader.readAsText(file);
    });
  }

  /** @ignore */

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// Initializing Zooming, Dragging, Brushing, Drag-n-Drop, AdjustFit
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {
    /**
     * Initialize Spectra Viewer Zooming.
     */
    JSV.SpectraViewer2D.prototype.initialize_zooming = function() {
        const sv = this;
        sv.zoom_behavior = d3v7.zoom()
            .scaleExtent([sv.zoom_min, sv.zoom_max])
            .on('zoom',      zooming)
        sv.svg.call(sv.zoom_behavior)
            .on('dblclick.zoom', null); // Remove double click zoom behaviour

        function zooming(event) {
            if (sv.drag_event) return; // Spectra dragging takes precedence over zooming
            sv.threshold = event.transform.k;
            sv.fast_draw();
        }

    }


    JSV.SpectraViewer2D.prototype.drag_zoom = function() {
        const sv = this;
        const context = sv.svg;

        const start = (event) => {
            sv.zoom_rect.origin = [event.x, event.y];
            sv.zoom_rect.translated_origin = sv.absolute_to_translated_context(JSV.pixel(sv.zoom_rect.origin[0]),
                JSV.pixel(sv.zoom_rect.origin[1]));

            sv.zoom_rect.r = context.append("rect")
                .attr("id", "zoom-rect")
                .attr("style", "fill:blue;fill-opacity:0.1;")
                .attr('x', sv.zoom_rect.origin[0])
                .attr('y', sv.zoom_rect.origin[1])
                .attr('width', 1)
                .attr('height', 1);
        }

        const drag = (event) => {
            sv.zoom_rect.r
                .attr("x", Math.min(sv.zoom_rect.origin[0], event.x))
                .attr("y", Math.min(sv.zoom_rect.origin[1], event.y))
                .attr("width", Math.abs(event.x - sv.zoom_rect.origin[0]))
                .attr("height", Math.abs(event.y - sv.zoom_rect.origin[1]));
        }

        const end = (event) => {
            const sorter = (a, b) => {
                return a - b;
            };

            const translated_mouse = sv.absolute_to_translated_context(JSV.pixel(event.x), JSV.pixel(event.y));
            if (event.x !== sv.zoom_rect.origin[0] && event.y !== sv.zoom_rect.origin[1]) {
                sv.move_to([[sv.zoom_rect.translated_origin[0], translated_mouse[0]].sort(sorter),
                    [sv.zoom_rect.translated_origin[1], translated_mouse[1]].sort(sorter)],
                    500);
            }
            sv.zoom_rect.r.remove();
        }

        return {
            start: start,
            drag: drag,
            end: end
        };
    }

    /** @ignore */

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Scale
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  const LinearScale = function () {

    const d3_scale = d3v7.scaleLinear();

    d3_scale.min = function (x) {
      if (!arguments.length) return d3_scale.domain()[0];
      if (x > d3_scale.max()) {
        throw new Error('x must be less than domain max: ' + d3_scale.max());
      }
      // d3_scale.domain([ x, d3_scale.domain()[1] ]);
      d3_scale.domain([x, d3_scale.max()]);
      return d3_scale;
    }

    d3_scale.max = function (x) {
      if (!arguments.length) return d3_scale.domain()[1];
      if (x < d3_scale.min()) {
        throw new Error('x must be greater than domain min: ' + d3_scale.min());
      }
      // d3_scale.domain([ d3_scale.domain()[0], x ]);
      d3_scale.domain([d3_scale.min(), x]);
      return d3_scale;
    }

    d3_scale.diff = function () {
      return Math.abs(d3_scale.max() - d3_scale.min());
    }

    d3_scale.range_diff = function () {
      return Math.abs(d3_scale.range()[1] - d3_scale.range()[0]);
    }

    d3_scale.range_min = function () {
      return d3v7.min(d3_scale.range());
    }

    d3_scale.range_max = function () {
      return d3v7.max(d3_scale.range());
    }

    return d3_scale;
  };

  const SVScale = function () {
    this.x = JSV.LinearScale();
    this.y = JSV.LinearScale();
    this.initialized = false;
  };

  SVScale.prototype.clamp = function(x) {
    this.x.clamp(x);
    this.y.clamp(x);
  }

  /**
   * Update the scale/boundary based on the added data and the current scale/boundary.
   * @param {Object} data Data must be an object with an x and y array of min/max values.
   *   For example:
   *   ```js
   *   boundaries = { x: [-1, 10], y: [0, 1] };
   *   ```
   */
  SVScale.prototype.update = function(data) {
    // TODO: Handle multiple spectra better here
    // Only assign current x/y min/max if boundaries have been set before
    const current_x_max = this.initialized ? this.x.max() : null
    // const current_x_min = this.initialized ? this.x.min() : null
    const current_y_max = this.initialized ? this.y.max() : null
    // const current_y_min = this.initialized ? this.y.min() : null

    // Determine new boundaries (min/max x/y)
    let x_max = Number.NEGATIVE_INFINITY;
    let x_min = Number.POSITIVE_INFINITY;
    let y_max = Number.NEGATIVE_INFINITY;
    let y_min = Number.POSITIVE_INFINITY;

    data.x.concat(current_x_max).forEach((x) => {
        if(x > x_max) x_max = x;
      }
    )

    data.x.forEach((x) => {
      if(x < x_min) x_min = x;
    })

    data.y.concat(current_x_max).forEach((y) => {
          if(y > y_max) y_max = y;
        }
    )

    data.y.forEach((y) => {
      if(y < y_min) y_min = y;
    })

    // Update scale boundaries
    this.x.domain([x_min, x_max]);
    this.y.domain([y_min, y_max]);
    this.initialized = true;
  }

  JSV.LinearScale = LinearScale;
  JSV.SVScale = SVScale;

})(JSpectraViewer);


//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Events
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * SVEvents is a system to plug in callbacks to specific events in JSV.
   * Use [on](#on) to add a callback and [off](#off) to remove it.
   * Here are a list of events supported in JSV:
   *
   *  Event               | Description
   *  --------------------|-------------
   *  drag-start          | Called once before viewer starts drag animation
   *  drag                | Called every frame of the drag animation
   *  drag-end            | Called after dragging is complete
   *  zoom-start          | Called once before viewer starts zoom animation
   *  zoom                | Called every frame of the zoom animation
   *  zoom-end            | Called after zooming is complete
   *  domain-change       | Called after the viewer domains have changed
   *  adjust-start        | Called once on mouse down on a selection
   *  adjust              | Called while moving the selection
   *  adjust-end          | Called once after adjustment is complete, including deletions and peak creation
   *  adjust-peak-created | Called after peak creation
   *  selection-add       | Called when an element is added to the selection
   *  selection-remove    | Called after an element is removed from the selection
   *  selection-clear     | Called before the selection is cleared
   *  selection-empty     | Called after the selection becomes empty
   *  highlight-start     | Called when an element is highlighted
   *  highlight-end       | Called when an element is unhighlighted
   *  label-click         | Called when a annotation label is clicked
   *  cluster-navigator-updated | Called when the SVClusterNavigator table is updated
   *
   *  structure-click     | Called when atom or structure container is clicked
   */
  const SVEvents = function () {
    const handlers = {};

    /**
     * Attach a callback function to a specific JSV event.
     * ```js
     * sv = new JSV.SpectraViewer('#my-spectra');
     * sv.on('drag-start', function() { console.log('Dragging has begun!') };
     *
     * // The event can be namespaced for easier removal later
     * sv.on('drag-start.my_plugin', function() { console.log('Dragging has begun!') };
     * ```
     * @param {String} event Name of event. Events can be namespaced.
     * @param {Function} callback Function to call when event is triggered
     */
    this.on = function (event, callback) {
      check_type(event);
      const type = parse_event(event);
      if (!handlers[type]) handlers[type] = [];
      handlers[type].push(new Handler(event, callback));
    }

    /**
     * Remove a callback function from a specific JSV event. If no __callback__ is provided,
     * then all callbacks for the event will be removed. Namespaced events can and should be used
     * to avoid unintentionally removing callbacks attached by other plugins.
     * ```js
     * // Remove all callbacks attached to the 'drag-start' event.
     * // This includes any namespaced events.
     * sv.off('drag-start');
     *
     * // Remove all callbacks attached to the 'drag-start' event namespaced to 'my_plugin'
     * sv.off('drag-start.my_plugin');
     *
     * // Remove all callbacks attached to any events namespaced to 'my_plugin'
     * sv.off('.my_plugin');
     * ```
     * @param {String} event Name of event. Events can be namespaced.
     * @param {Function} callback Specfic function to remove
     */
    this.off = function (event, callback) {
      check_type(event);
      const type = parse_event(event);
      const namespace = parse_namespace(event);
      // If no callback is supplied remove all of them
      if (arguments.length == 1) {
        if (namespace) {
          if (type) {
            handlers[type] = handlers[type].filter(function (h) {
              return h.namespace != namespace;
            });
          } else {
            Object.keys(handlers).forEach(function (key) {
              handlers[key] = handlers[key].filter(function (h) {
                return h.namespace != namespace;
              });
            });
          }
        } else {
          handlers[type] = undefined;
        }
      } else {
        // Remove specific callback
        handlers[type] = handlers[type].filter(function (h) {
          return h.callback != callback;
        });
      }
    }

    /**
     * Trigger a callback function for a specific event.
     * ```js
     * // Triggers all callback functions associated with drag-start
     * sv.trigger('drag-start');
     *
     * // Triggers can also be namespaced
     * sv.trigger('drag-start.my_plugin');
     * ```
     * @param {String} event Name of event. Events can be namespaced.
     * @param {Object} object Object to be passed back to 'on'.
     */
    this.trigger = function (event, object) {
      check_type(event);
      const type = parse_event(event);
      const namespace = parse_namespace(event);
      if (Array.isArray(handlers[type])) {
        handlers[type].forEach(function (handler) {
          if (namespace) {
            if (handler.namespace == namespace) handler.callback.call(null, object);
          } else {
            handler.callback.call(null, object);
          }
        });
      }
    }

    /** @ignore */

    var check_type = function (type) {
      if (typeof type != 'string') {
        throw new Error('Type must be a string');
      }
    }
  };

  const Handler = function(event, callback) {
    this.callback = callback;
    this.event_type = parse_event(event);
    this.namespace = parse_namespace(event);
  }

  const parse_event = function(event) {
    return event.replace(/\..*/, '');
  }

  const parse_namespace = function(event) {
    const result = event.match(/\.(.*)/);
    return result ? result[1] : undefined
  }


  JSV.SVEvents = SVEvents;

})(JSpectraViewer);
//////////////////////////////////////////////////////////////////////////////
// SVSet
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * SVSet is essentially an array for holding JSV Objects. Any method
   * that works directly on an Array (Mutator methods) will work on a SVSet
   * (e.g. pop, push, reverse)
   *
   * If a single array is provided it will be converted to an SVSet.
   * If mulitple elements are provided, they will be added to the new SVSet.
   */
  const SVSet = function () {
    if ((arguments.length == 1) && (Array.isArray(arguments[0]))) {
      this.push.apply(this, arguments[0])
    } else if (arguments.length > 0) {
      this.push.apply(this, arguments)
    }
  };
  SVSet.prototype = Object.create(Array.prototype);

  /**
   * Return the string 'SVSet'
   * @return {String}
   */
  SVSet.prototype.toString = function() { return 'SVSet' }

  /**
   * Push the elements of the supplied SVSet/Array on to the SVSet.
   * @param {SVSet|Array} svset SVSet or Array to add
   * @return {SVSet}
   */
  SVSet.prototype.merge = function(svset) {
    this.push.apply(this, svset);
    return this;
  };

  /**
   * Change one or more properties of each element of the SVSet.
   * ```javascript
   * my_svset.attr(property, value)
   * my_svset.attr( {property1: value1, property2: value2} )
   * ```
   *
   * @param {Property|Value} attributes A property name and the new value.
   * @param {Object}     attributes An object properties and their new values.
   * @return {SVSet}
   */
  SVSet.prototype.attr = function(attributes) {
    if ( (arguments.length == 1) && (typeof attributes == 'object') ) {
      const keys = Object.keys(attributes);
      const key_len = keys.length;
      let set_i = 0;
      const set_len = this.length;
      for (; set_i < set_len; set_i++) {
        for (let key_i=0; key_i < key_len; key_i++) {
          this[set_i][keys[key_i]] = attributes[keys[key_i]];
        }
      }
    } else if (arguments.length == 2) {
      let i = 0;
      const len = this.length;
      for (; i < len; i++) {
        this[i][arguments[0]] = arguments[1];
      }
    } else if (attributes != undefined) {
      throw new Error('attr(): must be 2 arguments or a single object');
    }
    return this;
  }

  /**
   * Call the draw method for each element in the SVSet.
   * See [SVPath.draw](SVPath.js.html#draw) for details
   * @param {} context
   * @param {} scale
   * @param {} fast
   * @param {} calculated
   * @param {} pixel_skip
   * @retrun {SVSet}
   */
  SVSet.prototype.draw = function(context, scale, fast, calculated, pixel_skip, threshold = null) {
    let i = 0;
    const len = this.length;
    for (; i < len; i++) {
      if(threshold)
        this[i].draw(context, scale, fast, calculated, pixel_skip);
      else
        this[i].draw(context, scale, fast, calculated, pixel_skip, threshold);
    }
    return this;
  }

  /**
   * Iterates through each element of the SVSet and run the callback.
   * In the callback _this_ will refer to the element.
   * ```javascript
   * .each(function(index, element))
   * ```
   *
   * Note: This is slower then a _forEach_ or a _for loop_ directly on the set.
   * @param {Function} callback Callback run on each element of SVSet.
   *   The callback will be called with 2 parameters: the index of the element
   *   and the element itself.
   * @return {SVSet}
   */
  SVSet.prototype.each = function(callback) {
    let i = 0;
    const len = this.length;
    for (; i < len; i++) {
      callback.call(this[i], i, this);
    }
    return this;
  }

  /**
   * Returns true if the SVSet contains the element.
   * @param {Object} element Element to check for
   * @return {Boolean}
   */
  SVSet.prototype.contains = function(element) {
    return (this.indexOf(element) >= 0)
  }

  /**
   * Returns new SVSet with element removed
   * @return {SVSet}
   */
  SVSet.prototype.remove = function(element) {
    let self = this;
    self = new SVSet( self.filter(function(i) { return i != element }) );
    return self;
  }

  /**
   * Return true if the SVSet is empty.
   * @return {Boolean}
   */
  SVSet.prototype.empty = function() {
    return this.length == 0;
  }

  /**
   * Returns true if the SVSet is not empty.
   * @return {Boolean}
   */
  SVSet.prototype.present = function() {
    return this.length > 0;
  }

  /**
   * Sorts the SVSet by the provided property name.
   * @param {String} property Property to order each element set by [default: 'center']
   * @param {Boolean} descending Order in descending order (default: false)
   * @return {SVSet}
   */
  SVSet.prototype.order_by = function(property, descending) {
    // Sort by function call
    if (this.length > 0) {

      if (typeof this[0][property] === 'function'){
        this.sort(function(a,b) {
          if (a[property]() > b[property]()) {
            return 1;
          } else if (a[property]() < b[property]()) {
            return -1;
          } else {
            return 0;
          }
        })
      } else {
        // Sort by property
        this.sort(function(a,b) {
          if (a[property] > b[property]) {
            return 1;
          } else if (a[property] < b[property]) {
            return -1;
          } else {
            return 0;
          }
        })
      }
    }
    if (descending) this.reverse();
    return this;
  }

  SVSet.prototype.lineWidth = function(width) {
    let i = 0;
    const len = this.length;
    for (; i < len; i++) {
      this[i].lineWidth = width;
    }
    return this;
  }

  /**
   * Retrieve subset of SVSet or an individual element from SVSet depending on term provided.
   * @param {Undefined} term Return full SVSet
   * @param {Integer}   term Return element at that index (base-1)
   * @param {String}    term Return first element with id same as string. If the id starts
   *   with 'path-id-', the first element with that path-id will be returned.
   * @param {Array}     term Return SVSet with elements with matching ids
   * @return {SVSet|or|Element}
   */
  SVSet.prototype.get = function(term) {
    // if (arguments.length == 0) {
    if (term == undefined) {
      return this;
    } else if (Number.isInteger(term)) {
      return this[term-1];
    } else if (typeof term == 'string') {
      if ( term.match(/^path-id-/) ) {
        return this.filter(function(element) { return element.path_id() == term; })[0];
      } else if ( term.match(/^label-id-/) ) {
        return this.filter(function(element) { return element.label_id() == term; })[0];
      } else {
        return this.filter(function(element) { return element.id == term; })[0];
      }
    } else if (Array.isArray(term)) {
      const filtered = this.filter(function (element) {
        return term.some(function (id) {
          return element.id == id;
        });
      });
      const svset = new SVSet();
      svset.push.apply(svset, filtered);
      return svset;
    } else {
      return new SVSet();
    }
  }

  /**
   * Returns true if set matchs the supplied set. The order does not matter
   * and duplicates are ignored.
   * @param {SVSet}     term SVSet to compare against
   * @return {Boolean}
   */
  SVSet.prototype.equals = function(set) {
    if (set.toString() != 'SVSet' && !Array.isArray(set)) { return false }
    const setA = this.unique();
    const setB = set.unique();
    let equals = true;
    if (setA.length != setB.length) {
      return false
    }
    setA.forEach(function(a) {
      if (!setB.contains(a)) {
        equals = false
        return
      }
    })
    return equals
  }

  /**
   * Return new SVSet with no duplicated values.
   * @return {SVSet}
   */
  SVSet.prototype.unique = function() {
    return new SVSet(this.filter( onlyUnique ));
  }

  function onlyUnique(value, index, self) {
    return self.indexOf(value) === index;
  }

  // Polyfill for Array
  SVSet.prototype.find = function(predicate) {
    if (typeof predicate !== 'function') {
      throw new TypeError('predicate must be a function');
    }
    const list = Object(this);
    const length = list.length >>> 0;
    const thisArg = arguments[1];
    let value;

    for (let i = 0; i < length; i++) {
      value = list[i];
      if (predicate.call(thisArg, value, i, list)) {
        return value;
      }
    }
    return undefined;
  };

  /** @ignore */

  JSV.SVSet = SVSet;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// SVData
// Object to store X/Y data
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  const SVData = function (data) {
    if (data === undefined) {
      data = {x: [], y: []}
    }
    ;
    // If data is an array of objects convert them.
    if (data instanceof Array) {
      data = array_to_object(data);
    }
    // Confirm x and y are the same length
    if (data.x.length != data.y.length) {
      throw new Error('x and y must be the same length!');
    }
    if (data.yi && (data.yi.length != data.x.length)) {
      throw new Error('If present yi must be the same length as x and y!');
    }
    // Confirm arrays start at lowest x values first. If not reverse.
    const reverse_data = (data.x[0] > data.x[data.x.length - 1]);

    this.x = reverse_data ? data.x.reverse() : data.x;
    this.y = reverse_data ? data.y.reverse() : data.y;
    this.yi = (data.yi && reverse_data) ? data.yi.reverse() : data.yi;
  };

  SVData.prototype.length = function() {
    return this.x.length;
  }

  SVData.prototype.asArray = function() {
    const data = [];
    let point;
    let i = 0;
    const len = this.length();
    for (; i < len; i++) {
      point = { x: this.x[i], y: this.y[i] }
      if (this.yi) { point.yi = this.yi[i]; }
      data.push(point);
    }
    return data;
  }

  SVData.prototype.index_of = function(search_value, upper) {
    return JSV.index_of_value(this.x, search_value, upper);
  }

  SVData.prototype.simplify = function(tolerance, highQuality) {
    // return new SVData( simplify(this.asArray(), tolerance, highQuality) );
    return new SVData( JSV.simplify(this, tolerance, highQuality) );
  }

  const array_to_object = function(data) {
    const x = [], y = [], yi = [];
    let i = 0;
    const len = data.length;
    for (; i < len; i++) {
      x.push(data[i].x);
      y.push(data[i].y);
      if (data[0].yi) { yi.push(data[i].yi); }
    }
    const object = {x: x, y: y};
    if (data[0] && data[0].yi) { object.yi = yi; }
    return object;
  }



  JSV.SVData = SVData;

})(JSpectraViewer);
//////////////////////////////////////////////////////////////////////////////
// SVData2D
// Object to store X/Y/Z data
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

    const SVData2D = function (data) {
        if (data === undefined) {
            data = {x: [], y: [], z: []}
        }
        ;
        // If data is an array of objects convert them.
        if (data instanceof Array) {
            data = array_to_object(data);
        }
        // Confirm x and y are the same length
        if (data.z.length != (data.x.length * data.y.length)) {
            throw new Error('z length must be divisible by x and y length!')
        }

        this.x = data.x;
        this.y = data.y;
        this.z = data.z;
    };

    SVData2D.prototype.length = function() {
        return this.x.length;
    }

    SVData2D.prototype.asArray = function() {
        let data = [];
        let point;
        for (let i=0, len=this.length(); i < len; i++) {
            point = { x: this.x[i], y: this.y[i], z: this.z[i] }
            data.push(point);
        }
        return data;
    }

    // SVData2D.prototype.index_of = function(search_value, upper) {
    //     return JSV.index_of_value(this.x, search_value, upper);
    // }

    // SVData2D.prototype.simplify = function(tolerance, highQuality) {
    //     // return new SVData( simplify(this.asArray(), tolerance, highQuality) );
    //     return new SVData( JSV.simplify(this, tolerance, highQuality) );
    // }

    let array_to_object = function(data) {
        let x = [ ], y = [ ], z = [ ];
        let i = 0;
        const len = data.length;
        for (; i < len; i++) {
            x.push(data[i].x);
            y.push(data[i].y);
            z.push(data[i].z);
        }
        let object = {x:x, y:y, z:z};
        return object;
    }



    JSV.SVData2D = SVData2D;

})(JSpectraViewer);
//////////////////////////////////////////////////////////////////////////////
// SVPath
// Objects that will be drawn as paths should inherit from SVPath
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  path_id = 0;

  /**
   * Objects that will be drawn as paths should inherit form SVPath
   * (e.g. [Spectrum](Spectrum.js.html), [Peak](Peak.js.html), etc)
   * @param {Object} options Properties for the canvas context to use when drawing the path
   *
   *   - _color_ [default: 'black']
   *   - _fill_ [default: '']
   *   - _lineWidth_ [default: 1]
   *   - _visible_ [default: true]
   *   - _dash_ [default: []]
   *
   * @param {Object} meta User-defined _key_:_value_ pairs to add to the SVPath.
   */
  const SVPath = function(options, meta) {
    options = options || {};
    this.meta = meta || {};
    this.lineWidth = JSV.default_for(options.lineWidth, 1);
    this.color     = JSV.default_for(options.color, 'black');
    this.visible   = JSV.default_for(options.visible, true);
    this.dash      = JSV.default_for(options.dash, []);
    this.fill      = options.fill;
    this.path_id();
  }

  SVPath.prototype.peaks = function() {
    return new JSV.SVSet();
  }

  SVPath.prototype.path_id = function() {
    const new_id = generate_path_id();
    this.path_id = function() { return new_id; }
    return new_id;
  }

  const generate_path_id = function() {
    return 'path-id-' + path_id++;
  }

  SVPath.prototype.attr = function(attributes) {
    const keys = Object.keys(attributes);
    for (let i=0, len=keys.length; i < len; i++) {
      this[keys[i]] = attributes[keys[i]];
    }
    return this;
  }

  SVPath.prototype.display_settings = function(options) {
    if (options) {
      return this.attr(options);
    } else {
      return {
        color: this.color,
        fill: this.fill,
        lineWidth: this.lineWidth,
        visible: this.visible,
        dash: this.dash
      }
    }
  }

  // Determine best data to use for drawing based on 'fast' and 'calculate' and the
  // available data in the SVPath.
  SVPath.prototype.data_for_draw = function(scale, fast, calculate, pixel_skip) {
    let data;
    const has_data = this.xy_data != undefined;

    if (this.phasing && has_data && this.xy_data.yi != undefined) {
      let ph;
      const y = [ ];
      const len = this.xy_data.length();
      for (let i=0; i < len; i++) {
        ph = Math.PI * (1*this.ph0 + 1*(len-i-1)*this.ph1/len) / 180.0;
        y.push( ( Math.cos(ph) * this.xy_data.y[i] ) + ( Math.sin(ph) * this.xy_data.yi[i] ) );
      }
      data = {x:this.xy_data.x, y:y};
    } else if ( (calculate && this.peaks().length > 0) || (!has_data && this.peaks().length > 0) ) {
      if (!pixel_skip) pixel_skip = 1;
      const number_of_points = scale.x.range_diff() / JSV.pixel_ratio / pixel_skip;
      const fast_calc = pixel_skip > 1;
      data = JSV.xy_from_peaks(this.peaks(), number_of_points, scale.x.domain()[0], scale.x.domain()[1], fast_calc);
    } else if (has_data) {
      data = fast ? this.simple_xy_data : this.xy_data;
    } else {
      data = {x: [0], y: [0]};
      console.error('No data or Peaks for SVPath:');
    }
    return data;
  }

  SVPath.prototype.draw = function(context, scale, fast, calculate, pixel_skip) {
    if (this.visible) {
      const data = this.data_for_draw(scale, fast, calculate, pixel_skip);
      const y_axis_elevation = 10;
      const min_index = JSV.index_of_value(data.x, scale.x.domain()[0], false);
      const max_index = JSV.index_of_value(data.x, scale.x.domain()[1], true);
      context.translate(0.5, 0.5);
      context.beginPath();
      const y_start = this.fill ? scale.y(0) - y_axis_elevation : scale.y(data.y[min_index]) - y_axis_elevation;
      context.moveTo( scale.x(data.x[min_index]), y_start );
      for (let i = min_index; i <= max_index; i++) {
        context.lineTo( scale.x(data.x[i]), scale.y(data.y[i]) - y_axis_elevation );
      }
      if (this.fill) {
        context.lineTo( scale.x(data.x[max_index]), scale.y(0) - y_axis_elevation );
        context.fillStyle = this.fill;
        context.fill();
      }
      // context.lineWidth = JSV.pixel(0.5);
      context.lineWidth = JSV.default_for(this.lineWidth, 1);
      context.strokeStyle = this.color;
      context.setLineDash(this.dash);
      context.stroke();
      context.translate(-0.5, -0.5);
    }
  }

  JSV.SVPath = SVPath;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// SVDot
// Objects that will be drawn as paths should inherit from SVPath
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

    plot_id = 0;

    /**
     * Objects that will be drawn as paths should inherit form SVPath
     * (e.g. [Spectrum](Spectrum.js.html), [Peak](Peak.js.html), etc)
     * @param {Object} options Properties for the canvas context to use when drawing the path
     *
     *   - _color_ [default: 'black']
     *   - _fill_ [default: '']
     *   - _lineWidth_ [default: 1]
     *   - _visible_ [default: true]
     *   - _dash_ [default: []]
     *
     * @param {Object} meta User-defined _key_:_value_ pairs to add to the SVPath.
     */
    const SVDot = function (options, meta) {
        options = options || {};
        this.meta = meta || {};
        this.dotSize = JSV.default_for(options.dotSize, 1);
        this.color = JSV.default_for(options.color, 'black');
        this.visible = JSV.default_for(options.visible, true);
        this.plot_id();
    };

    SVDot.prototype.plot_id = function() {
        let new_id = generate_plot_id();
        this.path_id = function() { return new_id; }
        return new_id;
    }

    const generate_plot_id = function() {
        return 'plot-id-' + plot_id++;
    }

    SVDot.prototype.attr = function(attributes) {
        const keys = Object.keys(attributes);
        let i = 0;
        const len = keys.length;
        for (; i < len; i++) {
            this[keys[i]] = attributes[keys[i]];
        }
        return this;
    }

    SVDot.prototype.display_settings = function(options) {
        if (options) {
            return this.attr(options);
        } else {
            return {
                color: this.color,
                fill: this.fill,
                lineWidth: this.lineWidth,
                visible: this.visible,
                dash: this.dash
            }
        }
    }


    SVDot.prototype.sum = function(a) {
        return a.reduce((acc, val) => acc + val)
    }

    SVDot.prototype.mean = function(a) {
        return this.sum(a) / a.length
    }

    SVDot.prototype.stddev = function(arr) {
        const arr_mean = this.mean(arr)
        const r = function(acc, val) {
            return acc + ((val - arr_mean) * (val - arr_mean))
        }
        return Math.sqrt(arr.reduce(r, 0.0) / arr.length)
    }

    SVDot.prototype.find_peaks = function(z, threshold) {
        // init variables
        const signals = Array(z.length).fill(0);
        let std_dev = this.stddev(z)
        let av = this.mean(z)

        for(let i = 0; i<z.length; i++){
            if(z[i] > av + (std_dev * threshold)){
                signals[i] = 1 // positive peak
            } else if (z[i] < av - (std_dev * threshold)){
                signals[i] = -1 // negative peak
            }
        }
        return signals
    }

    // Determine best data to use for drawing based on 'fast' and 'calculate' and the
    // available data in the SVPath.
    SVDot.prototype.data_for_draw = function() {
        let data;
        const has_data = this.xyz_data != undefined;
        if (has_data) {
            return {x: this.xyz_data.x, y: this.xyz_data.y, z: this.xyz_data.z}
        } else {
            data = {x: [0], y: [0], z: [0]};
            console.log('No data or Peaks for SVPath:');
            console.log(this);
            return data
        }
    }

    SVDot.prototype.draw = function(context, scale, fast, scaled_height, x_range, threshold) {

        let draw_countours = (contours) => {
            contours.forEach(c =>{
                    if (c.coordinates.length == 0) return;
                    c.coordinates.forEach( coords => {
                        context.beginPath();
                        coords.forEach( contour => {
                            contour.forEach(coord => {
                                const x_range = scale.x.range();
                                const y_range = scale.y.range();
                                let x_coord = scale.x( this.original_scale.x.invert(coord[0]))
                                let y_coord = scale.y( this.original_scale.y.invert(coord[1]))
                                if(x_coord < x_range[1]){
                                    x_coord = x_range[1]
                                } else if(x_coord > x_range[0]){
                                    x_coord = x_range[0]
                                }
                                if(y_coord < y_range[0]){
                                    y_coord = y_range[0]
                                } else if(y_coord > y_range[1]){
                                    y_coord = y_range[1]
                                }
                                context.lineTo(x_coord, y_coord);
                            })
                        })
                        context.stroke();
                    })
                })
        }

        if (this.visible) {
            let svd = this;
            let data = this.data_for_draw();

            // We will use the first scale provided to calculate contours and later map the results to the current scale
            if(!this.original_scale){
                this.original_scale = new JSV.SVScale();
                this.original_scale.x.domain(scale.x.domain());
                this.original_scale.y.domain(scale.y.domain());
                this.original_scale.x.range(scale.x.range());
                this.original_scale.y.range(scale.y.range());
            }

            if(!this.old_threshold || this.old_threshold != threshold){
                this.old_threshold = threshold;

                // find peaks
                const peakArray = svd.find_peaks(data.z, threshold);

                let positivePeaks = [];
                let negativePeaks = [];

                // Generate Contours
                for (let row = 0; row < data.y.length; row++) {
                    for (let col = 0; col < data.x.length; col++) {
                        if (peakArray[(data.x.length * row) + col] === -1) {
                            negativePeaks.push([scale.x(data.x[col]), scale.y(data.y[row])]);
                        } else if (peakArray[(data.x.length * row) + col] === 1) {
                            positivePeaks.push([this.original_scale.x(data.x[col]),
                                this.original_scale.y(data.y[row])]);
                        }
                    }
                }

                this.posContours = d3v7.contourDensity()
                    .size([data.x.length * 3, data.y.length * 3])
                    .bandwidth(10)    // smaller = more precision in lines = more lines
                    (positivePeaks);

                this.negContours = d3v7.contourDensity()
                    .size([data.x.length * 3, data.y.length * 3])
                    .bandwidth(10)    // smaller = more precision in lines = more lines
                    (negativePeaks);
            }

            // Plot contours
            context.strokeStyle = 'blue';
            draw_countours(this.posContours);
            context.strokeStyle = 'red';
            draw_countours(this.negContours);

            // avoid doing this too often
            if(!fast) {

                // calculate the projection
                let x_projection = {};
                let y_projection = {};
                for (let row = 0; row < data.y.length; row++) {
                    let y_val = data.y[row]
                    for (let col = 0; col < data.x.length; col++) {
                        let x_val = data.x[col]
                        let z_val = data.z[(data.x.length * row) + col]
                        if (x_projection[x_val] === undefined) {
                            x_projection[x_val] = 0;
                        }
                        if (y_projection[y_val] === undefined) {
                            y_projection[y_val] = 0;
                        }
                        x_projection[x_val] += z_val;
                        y_projection[y_val] += z_val;
                    }
                }


                let y_proj_vals = Object.values(y_projection);
                let x_proj_vals = Object.values(x_projection);
                let x_proj_max = Math.max(...x_proj_vals);
                let x_proj_min = Math.min(...x_proj_vals);
                let y_proj_max = Math.max(...y_proj_vals);
                let y_proj_min = Math.min(...y_proj_vals);

                const x_old_range = (x_proj_max - x_proj_min);
                const y_old_range = (y_proj_max - y_proj_min);

                // scale x_projection
                Object.keys(x_projection).map(function (key, index) {
                    x_projection[key] = ((x_projection[key] - x_proj_min) / x_old_range);
                });

                // scale y_projection
                Object.keys(y_projection).map(function (key, index) {
                    y_projection[key] = (((y_projection[key] - y_proj_min)) / y_old_range);
                });

                // plot top 1d nmr from projections
                let horizontal_1d = d3v7.scaleLinear()
                    .domain([x_proj_min, x_proj_max])
                    .range([0, -(scaled_height * 0.12) + 10]);

                let vertical_1d = d3v7.scaleLinear()
                    .domain([y_proj_min, y_proj_max])
                    .range([0, -(x_range[0] * 0.08) + 10]);

                context.lineWidth = 1;
                context.strokeStyle = "blue";

                // plot x_projection
                context.beginPath();
                for (let i = 0; i <= data.x.length; i++) {
                    const x_coord = scale.x(data.x[i]);
                    const range = scale.x.range();
                    if (x_coord >= range[1] && x_coord <= range[0]) {
                        context.lineTo(x_coord, horizontal_1d(x_proj_vals[i]));
                    }
                }
                context.stroke();

                // plot y_projection
                context.beginPath();
                for (let i = 0; i <= data.y.length; i++) {
                    const y_coord = scale.y(data.y[i]);
                    const range = scale.y.range();
                    if (y_coord >= range[0] && y_coord <= range[1]) {
                        context.lineTo(vertical_1d(y_proj_vals[i]), scale.y(data.y[i]));
                    }
                }
                context.stroke();
            }

        }
    }

    JSV.SVDot = SVDot;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Legend
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  function Rect(x, y, width, height) {
    this.x = x;
    this.y = y;
    this.width = width;
    this.height = height;
  }

  Rect.prototype.contains_pt = function(x, y) {
    return ( x >= this.x && x <= (this.x + this.width) && y >= this.y && y <= (this.y + this.height) )
  }


  function SVLegend(sv) {
    const self = this;
    this.sv = sv;
    this.max_label_length = 0;
    this.margin_top = JSV.pixel(5);
    this.margin_side = JSV.pixel(10);
    this.key_label_space = JSV.pixel(5);
    this.key_width = JSV.pixel(10);
    this.line_width = JSV.pixel(3);
    this.text_height = JSV.pixel(18);
    this.items = [];

    sv.svg.on('click.legend', function(event) {
      let pt = sv.mouse(event, sv.canvas);
      for (let i=0, len=self.items.length; i < len; i++) {
        let item = self.items[i];
        if (item.rect.contains_pt(pt[0], pt[1]) && sv.legend_show) {
          item.path.visible = !item.path.visible;
          sv.full_draw();
        }
      }
    })
  }

  SVLegend.prototype.bottom = function() {
    const text_y = (this.sv.zoombox.visible ? JSV.pixel(this.sv.zoombox.height) : 0) + this.margin_top;
    return this.sv.legend_show ? this.offset_y : text_y;
  }

  SVLegend.prototype.update = function() {
    const sv = this.sv;
    const context = sv.context;
    this.offset_y = (sv.zoombox.visible ? JSV.pixel(sv.zoombox.height) : 0) + this.margin_top;
    const offset_x = this.margin_side;
    context.font = sv.adjust_font(1, 'monospace');
    this.items = [];
    for (let i = 0; i < sv.spectra().length; i++) {
      const spectrum = sv.spectra()[i];
      // if (! spectrum.active) continue;
      const item = new JSV.SVLegendItem(this);
      const rect = new Rect();
      rect.y = this.offset_y;
      rect.height = this.text_height;
      rect.width = this.key_width + this.key_label_space + context.measureText(spectrum.name).width;
      rect.x = sv.axis_x_reverse ?
          offset_x :
          JSV.pixel(sv.width) - offset_x - rect.width;
      item.rect = rect;
      item.path = spectrum;
      this.offset_y += this.text_height;
      this.items.push(item);
      this.max_label_length = Math.max(item.path.name.length, this.max_label_length);
    }
  }

  SVLegend.prototype.draw = function() {
    const sv = this.sv;
    const context = sv.context;
    context.save();
    context.textAlign = 'left';
    context.textBaseline = 'middle';
    context.font = sv.adjust_font(1, 'monospace');
    for (let i = 0; i < this.items.length; i++) {
      this.items[i].draw(this.max_label_length);
    }
    context.restore();
  }

  function SVLegendItem(legend, path, rect) {
    this.legend = legend;
    this.sv = legend.sv;
    this.path = path; // e.g. spectra
    this.rect = rect; // Rect defining space in legend
  }

  SVLegendItem.prototype.key_x1 = function() {
    return this.sv.axis_x_reverse ?
        this.rect.x :
        this.rect.x + this.rect.width - this.legend.key_width;
  }

  SVLegendItem.prototype.key_x2 = function() {
    return this.key_x1() + this.legend.key_width;
  }

  SVLegendItem.prototype.key_y = function() {
    return this.rect.y + (this.rect.height / 2);
  }

  SVLegendItem.prototype.label_x = function() {
    return this.sv.axis_x_reverse ?
        this.rect.x + this.legend.key_width + this.legend.key_label_space :
        this.rect.x;
  }


  SVLegendItem.prototype.draw = function(label_len) {
    let rect = this.rect;
    this.sv.context.clearRect(rect.x, rect.y, rect.width, rect.height);
    if (this.path.visible) this.draw_key();
    this.draw_label(label_len);
  }

  SVLegendItem.prototype.draw_key = function() {
    let context = this.sv.context;
    context.beginPath();
    context.moveTo(this.key_x1(), this.key_y());
    context.lineTo(this.key_x2(), this.key_y());
    context.lineWidth = this.legend.line_width;
    context.strokeStyle = this.path.color;
    context.setLineDash(this.path.dash);
    context.stroke();
  }

  SVLegendItem.prototype.draw_label = function(label_len) {
    this.sv.context.fillStyle = this.path.visible ? 'black' : '#999';
    let checkbox = this.path.visible ? "\u2612" : "\u2610"
    let label =  `${this.path.name.padEnd(label_len)} ${checkbox}`
    this.sv.context.fillText(label, this.label_x(), this.key_y())
  }

  JSV.SVLegend = SVLegend;
  JSV.SVLegendItem = SVLegendItem;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Labels
//////////////////////////////////////////////////////////////////////////////
// TODO:
// - Only check for collisions with spectrum if number of labels is < 100
// - When checking for overlap with spectrum only use 4 points in increments of (width / 3)
// - consider having labels per spectrum, them group visible ones together for drawing
(function(JSV) {

  function SVAnnotation(sv, options = {}) {
    const self = this;
    this.sv = sv;
    this.highlighted_label;
    this.point_gap = JSV.default_for(options.point_gap, JSV.pixel(12));
    this.label_color = JSV.default_for(options.label_color, '#5555DD');
    this.visible = JSV.default_for(options.visible, true);
    this.hover = JSV.default_for(options.hover, true);
    this.labels = new JSV.SVSet();
    this.visible_labels = new JSV.SVSet();
    // Test label-click
    // sv.on('label-click', function(label) {
    //   console.log(label.text)
    // })
    sv.svg.on('mousedown.label', function() {
      if (self.highlighted_label && self.hover) {
        sv.trigger('label-click', self.highlighted_label);
      }
    });
  }

  SVAnnotation.prototype.get = function(term) {
    return this.labels.get(term);
  }

  SVAnnotation.prototype.update = function() {
    const sv = this.sv;
    const labels = sv.labels ? sv.labels.get() : new JSV.SVSet();
    sv.spectra().forEach(function(spectrum) {
      if (spectrum.visible && spectrum.active && spectrum.labels) {
        labels.push.apply(labels, spectrum.labels.get())
      }
    });
    this.labels = labels;

    let visible_labels = this.reduce_labels_by_view();
    visible_labels = this.reduce_labels_by_height(visible_labels);
    visible_labels = this.adjust_labels(visible_labels);
    this.visible_labels = visible_labels;
  }

  SVAnnotation.prototype.rect_for_label = function(label) {
    const sv = this.sv;
    let rect, x, y;
    const point_x = sv.scale.x(label.x);
    const point_y = sv.scale.y(label.y);
    const text_width = sv.context.measureText(label.text).width;
    const text_height = JSV.pixel(label.font_size);
    if (label.vertical) {
      x = point_x - (text_height/2);
      y = point_y - text_width - this.point_gap;
      rect = new Rect(x, y, text_height, text_width);
    } else {
      x = point_x - (text_width/2);
      y = point_y - text_height - this.point_gap;
      rect = new Rect(x, y, text_height, text_width);
    }
    return rect;
  }

  // Return labels within view
  SVAnnotation.prototype.reduce_labels_by_view = function(labels) {
    if (!labels) labels = this.labels;
    const scale = this.sv.scale;
    return labels.filter(function(label) { return (label.x > scale.x.min() && label.x < scale.x.max() && label.y < scale.y.max())})
  }

  // Return only highest labels of overlapping sets
  SVAnnotation.prototype.reduce_labels_by_height = function(labels) {
    if (!labels) labels = this.labels;
    const scale = this.sv.scale;
    const font_min = 6;
    const reduced = new JSV.SVSet();

    let i = 0;
    const len = labels.length;
    for (; i < len; i++) {
      label = labels[i];
      label.font_size = font_min;
      label.rect = this.rect_for_label(label);
      label.adjust_rect(reduced);
      if (label.stack_level < 3) {
        reduced.push(label);
      }
    }

    return reduced;
  }

  SVAnnotation.prototype.adjust_labels = function(labels) {
    const sv = this.sv;
    let peak, label, label_rects, spectra_overlap;
    // Number of stacked labels to accept before trying to reduce the font size
    const max_stack = 1;
    // space between label line and peak/label
    const line_spacer = JSV.pixel(2);
    // minimum gap between peak and label
    const peak_gap = JSV.pixel(12);
    const font_max = 13;
    const font_min = 8;
    const font_range = font_max - font_min;
    let font_current = font_max;
    let font_current_min = font_min;

    const scale = sv.scale;
    const context = sv.context;
    context.save();
    let bad_stack = true;
    while(bad_stack) {
      bad_stack = false;
      adjusted_labels = new JSV.SVSet();
      context.font = JSV.pixel(font_current) + "px Arial";
      // Adjust min font based on zoom level
      font_current_min = font_min + Math.round(font_range * sv.zoom_x / sv.zoom_max);
      let i = 0;
      const len = labels.length;
      for (; i < len; i++) {
        label = labels[i];
        label.font_size = font_current;
        label.rect = this.rect_for_label(label);
        // spectra_overlap = this.label_over_spectra(label, this.sv.peaks());
        label.adjust_rect(adjusted_labels);
        if ( (label.stack_level > max_stack) && font_current > font_current_min) {
          bad_stack = true;
          font_current -= 1;
          break;
        } else {
          adjusted_labels.push(label);
        }
      }
    }
    return labels;
  }

  // Check if the bottom line of rect overlaps with sumline created from peaks
  SVAnnotation.prototype.label_over_spectra = function(label, peaks, tolerance) {
    const sv = this.sv;
    const x_vals = [];
    for (let i=0, len=label.rect.width; i < len; i++) {
      x_vals.push(sv.scale.x.invert(label.rect.x + i));
    }
    const y = sv.scale.y.invert(label.rect.bottom());
    let overlap = false;
    for (let i=0, len=x_vals.length; i < len; i++) {
      if (y < JSV.sum_of_peaks(x_vals[i], peaks)) {
        overlap = true;
        break;
      }
    }
    return overlap;
  }

  SVAnnotation.prototype.draw = function() {
    if (!this.visible) { return }
    const sv = this.sv;
    const context = sv.context;
    const scale = sv.scale;
    let label;
    const line_spacer = JSV.pixel(2);
    context.save();

    this.update();
    const visible_labels = this.visible_labels;
    // Draw the label lines, joining the peak to the label
    for (let i=0, len=visible_labels.length; i < len; i++) {
      label = visible_labels[i];
      const line_x = scale.x(label.x);
      const line_y1 = scale.y(label.y) - line_spacer;
      const line_y2 = label.rect.bottom() + line_spacer;
      context.beginPath();
      context.moveTo(line_x, line_y1);
      context.lineTo(line_x, line_y2);
      context.strokeStyle = '#999999';
      context.lineWidth = JSV.pixel(0.5);
      context.stroke();
    }

    // Draw the labels
    context.fillStyle = this.label_color;
    for (let i=0, len=visible_labels.length; i < len; i++) {
      label = visible_labels[i];
      if (label === this.highlighted_label) {
        context.font = "bold " + context.font;
      } else {
        context.font = context.font.replace('bold ', '');
      }
      context.textBaseline = 'top';
      if (label.vertical) {
        context.textAlign = 'right';
        context.save();
        context.translate(label.rect.x, label.rect.y);
        context.rotate(-Math.PI/2);
        context.fillText(label.text, 0, 0);
        context.restore();
      } else {
        context.textAlign = 'left';
        context.fillText(label.text, label.rect.x, label.rect.y);
      }
    }
    context.restore();
  }

  SVAnnotation.prototype.check_hover = function() {
    const sv = this.sv;
    let label;
    if (this.hover) {
      const x = sv.scale.x(sv.mx);
      const y = sv.scale.y(sv.my);
      const old_label = this.highlighted_label;
      let current_label;

      let i = 0;
      const len = this.visible_labels.length;
      for (; i < len; i++) {
        label = this.visible_labels[i];
        if (label.rect.contains_pt(x, y)) {
          current_label = label;
          break;
        }
      }
      if (old_label != current_label) {
        this.highlighted_label = current_label;
        this.sv.trigger('click-start');
        sv.full_draw();
      }
      if (this.highlighted_label) {
        sv.svg.style('cursor', 'pointer');
      } else {
        sv.svg.style('cursor', 'move');
      }
    }
  }


  function SVLabelSet(options) {
    options = options || {};
    this.spectrum = options.spectrum
    this.labels = new JSV.SVSet();
  }

  SVLabelSet.prototype.get = function(term) {
    return this.labels.get(term);
  }

  SVLabelSet.prototype.add = function(data, display, meta) {
    this.labels.push( new SVLabel(this, data, display, meta) );
  }

  // If the SVLabelSet is associated with a spectrum with peaks those labels will be added
  SVLabelSet.prototype.add_peaks = function() {
    if (this.spectrum) {
      const peaks = this.spectrum.peaks();
      let i = 0;
      const len = peaks.length;
      for (; i < len; i++) {
        const peak = peaks[i];
        const y = JSV.sum_of_peaks(peak.center, peak.spectrum().peaks());
        this.add({ x: peak.center, y: y, text: peak.label() })
      }
    }
  }


  // If the SVLabelSet is associated with a spectrum with peaks those labels will be regenerated
  SVLabelSet.prototype.update_peaks = function() {
    if (this.spectrum) {
      // TODO: only remove peak associated labels
      this.labels = new JSV.SVSet();
      this.add_peaks();
    }
  }


  label_id = 0;

  function SVLabel(label_set, data, display, meta) {
    data = data || {};
    display_defaults = { vertical: true, font_size: 12 };
    display = JSV.merge(display_defaults, data.display, display);
    this.meta = JSV.merge(data.meta, meta);

    this.label_set = label_set;
    this.vertical = display.vertical;
    this.font_size = display.font_size;
    // this.id = data.id;
    // this.name = data.name || this.id;
    this.text = data.text;
    this.x = data.x;
    this.y = data.y;
    this.stack_level = 0;
    this.label_id();
  }

  SVLabel.prototype.label_id = function() {
    const new_id = generate_label_id();
    this.label_id = function() { return new_id; }
    return new_id;
  }

  const generate_label_id = function() {
    return 'label-id-' + label_id++;
  }

  // Adjust the rect so it does not overlap any rects in rect_array
  // Returns an object with the adjusted rect and how many levels the
  // rect had to be stacked to not overlap any more.
  SVLabel.prototype.adjust_rect = function(labels) {
    const overlap = true;
    let count = 0;
    while( this.overlap(labels) ) {
      this.rect.y -= JSV.pixel(this.font_size) * 2;
      count += 1;
    }
    this.stack_level = count
  }

  SVLabel.prototype.overlap =  function(labels) {
    return this.rect.overlap( labels.map( function(label) { return label.rect; } ) );
  }


  function Rect(x, y, width, height) {
    this.x = x;
    this.y = y;
    this.width = width;
    this.height = height;
  }

  Rect.prototype.bottom = function() {
    return this.y + this.height;
  }

  Rect.prototype.top = function() {
    return this.y;
  }

  Rect.prototype.left = function() {
    return this.x;
  }

  Rect.prototype.right = function() {
    return this.x + this.width;
  }

  // Check if rect overlaps with any rects in rect_array
  Rect.prototype.overlap = function(rect_array) {
    // Gap between labels
    const width_gap = JSV.pixel(4);
    const r1 = this;
    let overlap = false;
    let i = 0;
    const len = rect_array.length;
    for (; i < len; i++){
      const r2 = rect_array[i];
      if (r1.x <= r2.right() && r2.x <= (r1.right() + width_gap) && r1.y <= r2.bottom() && r2.y <= r1.bottom()) {
        overlap = true;
        break;
      }else{
        overlap = false;
      }
    }
    return overlap;
  }

  Rect.prototype.contains_pt = function(x, y) {
    return ( x >= this.x && x <= (this.x + this.width) && y >= this.y && y <= (this.y + this.height) )
  }

  JSV.SVAnnotation = SVAnnotation;
  JSV.SVLabelSet = SVLabelSet;
  JSV.SVLabel = SVLabel;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// Spectrum
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * Spectra can be made up from basic xy data, a peak list, or a set of one or
   * more [Compounds](Compound.js.html).
   *
   * Spectrum inherits methods and properties from [SVPath](SVPath.js.html).
   *
   * @param {Object} data Data used to create the Spectrum. See examples described in [SpectraViewer.add_spectrum](SpectraViewer.js.html#add_spectrum).
   * @param {Object} display Display options passed to [SVPath](SVPath.js.html)
   * @param {Object} meta User-defined _key_:_value_ pairs to add to the Spectrum.
   * @return {Spectrum}
   */
  function Spectrum(data, display, meta) {
    const self = this;
    data = data || { };
    display_defaults = { };
    display = JSV.merge(display_defaults, data.display, display);
    meta = JSV.merge(data.meta, meta);
    JSV.SVPath.call(this, display, meta);

    this.id = data.id;
    this.name = data.name || this.id;
    this.tolerance = data.tolerance;
    this.noise;
    this.active = true;
    this.xy_data = new JSV.SVData();
    this.simple_xy_data = new JSV.SVData();
    this.labels = new JSV.SVLabelSet({spectrum: this});
    this._compounds = new JSV.SVSet();

    if (data.compounds) {
      data.compounds.forEach(function(compound_data) {
        self.add_compound(new JSV.Compound(compound_data));
      })
    } else if (data.peak_list) {
      const compound_data = { clusters: [ { peaks: data.peak_list } ] };
      self.add_compound(new JSV.Compound(compound_data));
    }

    if (self.compounds().present()) {
      const number_of_points = JSV.default_for(data.number_of_points, 10000);
      const min_x = JSV.default_for(data.min_x, -1);
      const max_x = JSV.default_for(data.max_x, 10);
      if (!data.xy_line) {
        const xy_line = JSV.xy_from_peaks(self.peaks(), number_of_points, min_x, max_x);
        self.add_xy_line(xy_line);
      }
    }
    if (data.xy_line) {
      self.add_xy_line(data.xy_line);
    }

    if (data.labels) {
      data.labels.forEach(function(label) {
        self.labels.add(label);
      });
    }

  }
  JSV.inherits(Spectrum, JSV.SVPath);

  Spectrum.prototype.toString = function() { return 'Spectrum'; }

  /**
   * Add a [Compound](Compound.js.html) to the spectrum
   * @param {Compound} compound [Compound](Compound.js.html) to add
   */
  Spectrum.prototype.add_compound = function(compound) {
    this._compounds.push(compound);
    compound._spectrum = this;
    // TODO: update xy_data??
  }

  /**
   * Update xy_line based on peak data. Also update the zoombox.
   */
  Spectrum.prototype.update = function() {
    const sv = this._sv;
    const xy_data = JSV.xy_from_peaks(this.peaks(), this.xy_data.length(), sv.boundary.x.min(), sv.boundary.x.max());
    this.add_xy_line(xy_data);
    sv.zoombox.update();
  }

  /**
   * Returns an [SVSet](SVSet.js.html) of Compounds or a single Compound from the Spectrum.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Compound}
   */
  Spectrum.prototype.compounds = function(term) {
    return this._compounds.get(term)
  }

  Spectrum.prototype.add_xy_line = function(xy_data) {
    this.xy_data = new JSV.SVData(xy_data);
    this.noise = this.calculate_noise();
    const tolerance = this.tolerance || this.noise;
    this.simple_xy_data = this.xy_data.simplify(tolerance, true);
  }

  // Noise is calculated as the difference between the maximum
  // and minimum peak betwen the range.
  // TODO: if region does not exist, use first 0.01 in data
  Spectrum.prototype.calculate_noise = function() {
    // Region to calculate noise
    const region = [-0.05, -0.04];
    const xy_min = this.xy_data.index_of(region[0]);
    const xy_max = this.xy_data.index_of(region[1]);
    const y_noise = this.xy_data.y.slice(xy_min, xy_max);
    return (d3v7.max(y_noise) - d3v7.min(y_noise)).toPrecision(2);
  }

  /**
   * Returns an [SVSet](SVSet.js.html) of Clusters or a single Cluster from the Spectrum.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Cluster}
   */
  Spectrum.prototype.clusters = function(term) {
    const clusters = new JSV.SVSet();
    let i = 0;
    const len = this._compounds.length;
    for (; i < len; i++) {
      clusters.merge(this._compounds[i].clusters());
    }
    return clusters.get(term);
  }

  /**
   * Returns an [SVSet](SVSet.js.html) of Peaks or a single Peak from the Spectrum.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Peak}
   */
  Spectrum.prototype.peaks = function(term) {
    const peaks = new JSV.SVSet();
    this.compounds().forEach(function(compound) {
      compound.clusters().forEach(function(cluster) {
        peaks.merge(cluster.peaks());
      })
    })
    return peaks.get(term);
  }

  /////////////////////////////////////////////////////////////////////////////
  // Spectrum Properties (setters/getters)
  /////////////////////////////////////////////////////////////////////////////
  Object.defineProperties(Spectrum.prototype, {
    'active': {
      get: function() { return this._active },
      set: function(val) {
        this._active = val;
        const sv = this._sv;
        if (sv) {
          sv.reset_boundaries();
          sv.all_spectra().each(function() {
            if (this.active) {
              sv.boundary.update(this.xy_data);
              sv.scale.update(this.xy_data);
            }
          });
          sv.zoombox.update();
          sv.legend.update();
        }
      }
    }
  });

  JSV.Spectrum = Spectrum;

})(JSpectraViewer);//////////////////////////////////////////////////////////////////////////////
// Spectrum 2D
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

    /**
     * 2D Spectra are made up from basic xyz data
     *
     * Spectrum2D inherits methods and properties from [SVDot](SVDot.js.html).
     *
     * @param {Object} data Data used to create the Spectrum. See examples described in [SpectraViewer.add_spectrum](SpectraViewer.js.html#add_spectrum).
     * @param {Object} display Display options passed to [SVDot](SVDot.js.html)
     * @param {Object} meta User-defined _key_:_value_ pairs to add to the Spectrum.
     * @return {Spectrum2D}
     */
    function Spectrum2D(data, display, meta) {
        const self = this;
        data = data || { };
        display_defaults = { };
        display = JSV.merge(display_defaults, data.display, display);
        meta = JSV.merge(data.meta, meta);
        JSV.SVDot.call(this, display, meta);
        this._compounds = new JSV.SVSet();

        this.id = data.id;
        this.name = data.name || this.id;
        // this.tolerance = data.tolerance;
        // this.noise;
        this.active = true;
        this.xyz_data = new JSV.SVData2D();

        if(data.compounds) {
            data.compounds
        }

        self.add_xyz_plot(data.json_data);

        if (data.labels) {
            data.labels.forEach(function(label) {
                self.labels.add(label);
            });
        }

    }
    JSV.inherits(Spectrum2D, JSV.SVDot);

    Spectrum2D.prototype.toString = function() { return 'Spectrum2D'; }

    /**
     * Add a [Compound](Compound.js.html) to the spectrum
     * @param {Compound} compound [Compound](Compound.js.html) to add
     */
    Spectrum2D.prototype.add_compound = function(compound) {
        this._compounds.push(compound);
        compound._spectrum = this;
        // TODO: update xy_data??
    }

    Spectrum2D.prototype.add_xyz_plot = function(xyz_data) {
        this.xyz_data = new JSV.SVData2D(xyz_data);
        // this.noise = this.calculate_noise();
        // var tolerance = this.tolerance || this.noise
        //this.simple_xy_data = this.xy_data.simplify(tolerance, true);
    }

    /**
     * Returns an [SVSet](SVSet.js.html) of Compounds or a single Compound from the Spectrum.
     * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
     * @return {SVSet|or|Compound}
     */
    Spectrum2D.prototype.compounds = function(term) {
        return this._compounds.get(term)
    }


    /////////////////////////////////////////////////////////////////////////////
    // Spectrum Properties (setters/getters)
    /////////////////////////////////////////////////////////////////////////////
    Object.defineProperties(Spectrum2D.prototype, {
        'active': {
            get: function() { return this._active },
            set: function(val) {
                this._active = val;
                const sv = this._sv;
                if (sv) {
                    sv.reset_boundaries();
                    sv.all_spectra().each(function() {
                        if (this.active) {
                            sv.boundary.update(this.xy_data);
                            sv.scale.update(this.xy_data);
                        }
                    });
                    sv.zoombox.update();
                    sv.legend.update();
                }
            }
        }
    });

    JSV.Spectrum2D = Spectrum2D;

})(JSpectraViewer);//////////////////////////////////////////////////////////////////////////////
// Compound
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * Compounds are made up of a group of [Clusters](Cluster.js.html).
   *
   * ```javascript
   * data = [
   *   {
   *     id: 'HMDB01659',
   *     name: 'Acetone',
   *     concentration: '50',
   *     clusters: [
   *       {
   *         peaks: [
   *           { center: 2.22, amplitude: 0.6, width: 0.005 }
   *           { center: 2.34, amplitude: 0.6, width: 0.005 }
   *         ]
   *       }
   *     ]
   *   }
   * ]
   * ```
   *
   * Compound inherits methods and properties from [SVPath](SVPath.js.html).
   *
   * @param {Object} data Data used to create the Compound. See example below
   * @param {Object} display Display options passed to [SVPath](SVPath.js.html)
   * @param {Object} meta User-defined _key_:_value_ pairs to add to the Compound.
   * @return {Compound}
   */
  function Compound(data, display, meta) {
    const self = this;
    data = data || { };
    display_defaults = { visible: false };
    display = JSV.merge(display_defaults, data.display, display);
    meta = JSV.merge(data.meta, meta);
    JSV.SVPath.call(this, display, meta);

    this._clusters = new JSV.SVSet();
    if (data) {
      this.id = data.id;
      this.name = data.name;
      this.concentration = data.concentration
      this.original_concentration = data.concentration
      if (data.clusters) {
        data.clusters.forEach(function(cluster_data) {
          self.add_cluster(new JSV.Cluster(cluster_data))
        });
      }
    }
    // this.update_xy_data();
    this.update();
  }
  JSV.inherits(Compound, JSV.SVPath);

  Compound.prototype.toString = function() { return 'Compound'; }

  /**
   * Add a [Cluster](Cluster.js.html) to the Compound
   * @param {Cluster} cluster [Cluster](Cluster.js.html) to add
   */
  Compound.prototype.add_cluster = function(cluster) {
    this._clusters.push(cluster);
    cluster._compound = this;
    // cluster.update_center();
    // TODO: update xy_data??
  }

  /**
   * Update the xy_data simple_xy_data associated with this Compound.
   * Also update the order of the clusters.
   */
  Compound.prototype.update = function() {
    this.x_min = JSV.peak_min(this.peaks());
    this.x_max = JSV.peak_max(this.peaks());
    this.xy_data = new JSV.SVData(JSV.xy_from_peaks(this.peaks(), 5000, this.x_min, this.x_max));
    this.simple_xy_data = this.xy_data.simplify(0.0005, true);
    this._clusters.order_by('center');
  }

  /**
   * Returns an [SVSet](SVSet.js.html) of Clusters or a single Cluster from the Compound.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Cluster}
   */
  Compound.prototype.clusters = function(term) {
    return this._clusters.get(term);
  }

  /**
   * Returns an [SVSet](SVSet.js.html) of peaks or a single Peak from the Compound.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Peak}
   */
  Compound.prototype.peaks = function(term) {
    const peaks = new JSV.SVSet();
    this._clusters.forEach(function(cluster) {
      peaks.merge(cluster.peaks());
    })
    return peaks.get(term);
  }

  /**
   * Returns the parent [Spectrum](Spectrum.js.html).
   */
  Compound.prototype.spectrum = function() {
    return this._spectrum;
  }

  Compound.prototype.intersect = function(x, y, margin) {
    const cy = JSV.sum_of_peaks(x, this.peaks());
    // return y <= (cy + margin) && y >= (cy - margin);
    return y <= (cy) && y >= 0;
  }

  Compound.prototype.display_concentration = function() {
    return JSV.round(this.concentration, 1) + ' \u03BCM';
  }

  /**
   * Return the center of the compound: an average of center value for all the
   * peaks in the compound.
   * @return {Number}
   */
  Compound.prototype.center = function() {
    let center;
    if (this.peaks().present()) {
      center = d3v7.mean(this.peaks(), function(p) { return p.center });
    }
    return center;
  }

  Compound.prototype.atom_assignment_nmrml = function() {
    if (!window.X2JS) {
      console.log("X2JS needs to be installed to create nmrML: https://github.com/abdmob/x2js");
      return
    }
    const formatter = d3v7.format('.2f');
    const x2js = new X2JS();
    const json = {atomAssignmentList: {multiplet: []}};
    this.clusters().forEach(function(cluster) {
      const peaks = [];
      cluster.peaks().forEach(function(peak) {
        peaks.push({
          _center: peak.center,
          _amplitude: peak.amplitude,
          _width: peak.width
        })
      });
      const multiplet = {
        _center: formatter(cluster.center()),
        atoms: {_atomRefs: cluster.atom_refs.join(' ')},
        // multiplicity: { _TODO: 'TODO' },
        multiplicity: cluster.nmrml_multiplet_json(),
        peakList: {
          peak: peaks
        }
      };
      json.atomAssignmentList.multiplet.push(multiplet)
    });
    let nmrml = x2js.json2xml_str(json);
    nmrml = nmrml.replace(/><\/peak>/g, '/>')
    return nmrml
  }


  JSV.Compound = Compound;

})(JSpectraViewer);//////////////////////////////////////////////////////////////////////////////
// Cluster
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * Clusters are made up of a group of [Peaks](Peak.js.html). Typically a
   * cluster for an NMR spectrum would correspond to a multiplet of peaks.
   *
   * ```javascript
   * data = {
   *   peaks: [
   *     { center: 2.22, amplitude: 0.6, width: 0.005 },
   *     { center: 2.34, amplitude: 0.6, width: 0.005 }
   *   ]
   * }
   *
   * // The data may also contain upper_bound and lower attributes.
   * // The bounds can shown on the Viewer when a cluster is selected,
   * // if the SVSelection.show_bounds is true.
   *
   * data = {
   *   peaks: [...],
   *   lower_bound: 2.0,
   *   upper_bound: 2.4
   * }
   * ```
   *
   * Cluster inherits methods and properties from [SVPath](SVPath.js.html).
   *
   * @param {Object} data Data used to create the cluster. See example below
   * @param {Object} display Display options passed to [SVPath](SVPath.js.html)
   * @param {Object} meta User-defined _key_:_value_ pairs to add to the Cluster.
   * @return {Cluster}
   */
  function Cluster(data, display, meta) {
    const self = this;
    data = data || { };
    display_defaults = { visible: false };
    display = JSV.merge(display_defaults, data.display, display);
    meta = JSV.merge(data.meta, meta);
    JSV.SVPath.call(this, display, meta);

    this._peaks = new JSV.SVSet();

    this._atoms = new JSV.SVSet();

    this.lower_bound = JSV.number(data.lower_bound);
    this.upper_bound = JSV.number(data.upper_bound);
    this.weight = JSV.number(data.weight);
    this.protons = JSV.number(data.protons);

    if (data.peaks) {
      data.peaks.forEach(function(peak_data) {
        self.add_peak(new JSV.Peak(peak_data))
      });
    }

  }
  JSV.inherits(Cluster, JSV.SVPath);

  Cluster.prototype.toString = function() { return 'Cluster'; }

  /**
   * Add a [Peak](Peak.js.html) to the cluster
   * @param {Peak} peak [Peak](Peak.js.html) to add
   */
  Cluster.prototype.add_peak = function(peak) {
    this._peaks.push(peak);
    this.multiplet_type = undefined;
    peak._cluster = this;
  }


  /**
   * Reorder all the peaks in the Cluster by their center property.
   */
  Cluster.prototype.update = function() {
    this._peaks.order_by('center');
  }


  /**
   * Returns an [SVSet](SVSet.js.html) of peaks or a single Peak from the Cluster.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Peak}
   */
  Cluster.prototype.peaks = function(term) {
    return this._peaks.get(term);
  }

  /**
   * Returns the parent [Compound](Compound.js.html).
   */
  Cluster.prototype.compound = function() {
    return this._compound;
  }

  /**
   * Returns the parent [Spectrum](Spectrum.js.html).
   */
  Cluster.prototype.spectrum = function() {
    return this.compound().spectrum();
  }

  /**
   * Return the center of the cluster: an average of center value for all the
   * peaks in the cluster.
   * @return {Number}
   */
  Cluster.prototype.center = function() {
    let center;
    if (this.peaks().present()) {
      center = d3v7.mean(this.peaks(), function(p) { return p.center });
    }
    return center;
  }

  /**
   * Returns an SVSet of atoms or a single Atom from the Cluster.
   * @param {Integer|String|Array} term See [SVSet.get](SVSet.js.html#get) for details.
   * @return {SVSet|or|Atom}
   */
  Cluster.prototype.atoms = function(term) {
    return this._atoms.get(term);
  }


  // http://pubs.acs.org/paragonplus/submission/acs_nmr_guidelines.pdf
  // http://www.chem.uci.edu/~jsnowick/groupweb/files/MultipletGuideV4.pdf
  Cluster.prototype.determine_multiplet_type = function() {
    const self = this;
    const peaks = self.peaks();
    let type = 'm';
    if (peaks.length == 1) {
      type = 's';
    } else if (peaks.length == 2) {
      type = 'd';
    } else if (peaks.length == 3) {
      type = 't';
    } else if (peaks.length == 4) {
      if (double_of_doublets(peaks)) {
        type = 'dd';
      } else {
        type = 'q';
      }
    } else if (peaks.length == 5) {
      type = 'quint';
    } else if (peaks.length == 6) {
      if (triplet_of_doublets(peaks)) {
        type = 'td';
      } else {
        type = 'dt';
      }
    }
    return type
  }

  // All the peaks should be close to the same height
  const double_of_doublets = function(peaks) {
    const within = 0.1; // amount all heights must be within of the max height
    const heights = peaks.map(function (p) {
      return p.amplitude
    });
    const max_height = d3v7.max(heights);
    const range_min = max_height * (1 - within);
    const range_max = max_height * (1 + within);
    return heights.every(function(h) { return  h >= range_min && h <= range_max })
  }

  // The middle 2 peaks should be higher than all the other peaks
  const triplet_of_doublets = function(peaks) {
    const heights = peaks.map(function (p) {
      return p.amplitude
    });
    const middle_height = d3v7.min(heights.slice(2, 3));
    const check_heights = heights.slice(0, 1).concat(heights.slice(4, 5));
    return check_heights.every(function(h) { return  h <= middle_height })
  }

  // Return the json required to create the multiplet nmrml
  Cluster.prototype.nmrml_multiplet_json = function() {
    let json;
    switch (this.multiplet_type) {
      case 's':
        json = {_cvRef: 'NMRCV', _accession: 'NMR:1000194', _name: 'singlet feature'};
        break;
      case 'd':
        json = {_cvRef: 'NMRCV', _accession: 'NMR:1000184', _name: 'doublet feature'};
        break;
      case 't':
        json = {_cvRef: 'NMRCV', _accession: 'NMR:1000185', _name: 'triplet feature'};
        break;
      case 'q':
        json = {_cvRef: 'NMRCV', _accession: 'NMR:1000186', _name: 'quatruplet feature'};
        break;
      case 'dd':
        json = {_cvRef: 'NMRCV', _accession: 'NMR:1000192', _name: 'doublet of doublets feature'};
        break;
      case 'quint':
        json = {_cvRef: 'NMRCV', _accession: 'NMR:1000195', _name: 'quintet feature'};
        break;
      case 'dt':
        json = {_cvRef: 'NMRCV', _accession: 'NMR:1000196', _name: 'doublet of triplets feature'};
        break;
      case 'td':
        json = {_cvRef: 'NMRCV', _accession: 'NMR:1000197', _name: 'triplet of doublets feature'};
        break;
      case 'm':
        json = {_cvRef: 'NMRCV', _accession: 'NMR:1400305', _name: 'multiplet feature'};
        break;
      default:
        json = {_cvRef: 'NMRCV', _accession: 'NMR:1400305', _name: 'multiplet feature'};
    }
    return json
  }
  /////////////////////////////////////////////////////////////////////////////
  // Cluster Properties (setters/getters)
  /////////////////////////////////////////////////////////////////////////////
  Object.defineProperties(Cluster.prototype, {
    'multiplet_type': {
      get: function() {
        if (!this._multiplet_type) {
          this._multiplet_type = this.determine_multiplet_type();
        }
        return this._multiplet_type
      },
      set: function(type) {
        this._multiplet_type = type;
      }
    },
    'atom_refs': {
      get: function() {
        if (this.atoms().length > 0) {
          return this.atoms().map(function(a) { return a.id });
        } else {
          return this._atom_refs || []
        }
      },
      set: function(refs) {
        this._atom_refs = refs;
      }
    },
    'dir_dim_atom_refs': {
      get: function() {
        return this.dir_dim_atom_refs;
      },
      set: function(refs) {
        this._dir_dim_atom_refs = refs;
      }
    },
    'indir_dim_atom_refs': {
      get: function() {
        return this.indir_dim_atom_refs;
      },
      set: function(refs) {
        this._indir_dim_atom_refs = refs;
      }
    }
  });

  JSV.Cluster = Cluster;

})(JSpectraViewer);


//////////////////////////////////////////////////////////////////////////////
// Peak
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * Peaks are the base element that make up a Spectrum. One or more peaks
   * group together to form a Cluster. One or more Clusters group together to
   * form a compound. One or more compounds group together to form a spectrum.
   * To draw a cluster, compound or spectrum, all the peaks are summed
   * together at a particular x value to determine the y value.
   *
   * Peak inherits methods and properties from [SVPath](SVPath.js.html).
   *
   * @param {Object} data Data used to create the peak:
   *
   *   - _center_: center of the peak
   *   - _width_: width of peak at half height
   *   - _amplitude_: height of peak
   *
   * @param {Object} display Display options passed to [SVPath](SVPath.js.html)
   * @param {Object} meta User-defined _key_:_value_ pairs to add to the Peak.
   * @return {Peak}
   */
  function Peak(data, display, meta) {
    const self = this;
    data = data || { };
    display_defaults = { visible: false };
    display = JSV.merge(display_defaults, data.display, display);
    meta = JSV.merge(data.meta, meta);
    JSV.SVPath.call(this, display, meta);

    this.center = JSV.number(data.center);
    this.original_amplitude = JSV.number(data.original_amplitude);
    this.amplitude = JSV.number(data.amplitude);
    this.width = JSV.number(data.width);
    // this.update_xy_data();
  }
  JSV.inherits(Peak, JSV.SVPath);

  Peak.prototype.toString = function() { return 'Peak'; }

  Peak.prototype.set_amplitude = function(new_value) {
    if (new_value <= 0) new_value = 0.00001;
    this.amplitude = new_value;
  }

  // Set the center to the 'new_value'
  // Also updates cluster center and bounds
  Peak.prototype.set_center = function(new_value) {
    this.center = new_value;
  }

  Peak.prototype.update = function() {
  }

  /**
   * Remove the peak from the parent Cluster. If the parent Cluster has no more peaks,
   * then it is removed from the parent Compound and so on. If there are no more peaks
   * in the Spectrum, then the spectrum is removed from the viewer.
   */
  Peak.prototype.delete = function() {
    const self = this;
    const cluster = this.cluster();
    const compound = this.compound();
    const spectrum = this.spectrum();
    const sv = spectrum._sv;
    const updated_peaks = cluster._peaks.filter(function (p) {
      return p != self
    });
    cluster._peaks = new JSV.SVSet(updated_peaks);
    cluster.multiplet_type = undefined;
    if (cluster.peaks().empty()) {
      const updated_clusters = compound._clusters.filter(function (c) {
        return c != cluster;
      });
      compound._clusters = new JSV.SVSet(updated_clusters);
    }
    if (compound.peaks().empty()) {
      const updated_compounds = spectrum._compounds.filter(function (c) {
        return c != compound;
      });
      spectrum._compounds = new JSV.SVSet(updated_compounds);
    }
    if (spectrum.peaks().empty()) {
      const updated_spectra = sv._spectra.filter(function (c) {
        return c != spectrum;
      });
      sv._spectra = new JSV.SVSet(updated_spectra);
    }
  }

  /**
   * Returns itself in a [SVSet](SVSet.js.html).
   */
  Peak.prototype.peaks = function() {
    return new JSV.SVSet(this);
  }

  /**
   * Returns the parent [Cluster](Cluster.js.html).
   */
  Peak.prototype.cluster = function() {
    return this._cluster;
  }

  /**
   * Returns the parent [Compound](Compound.js.html).
   */
  Peak.prototype.compound = function() {
    return this.cluster().compound();
  }

  /**
   * Returns the parent [Spectrum](Spectrum.js.html).
   */
  Peak.prototype.spectrum = function() {
    return this.compound().spectrum();
  }


  /**
   * Returns the peak center to 3 decimal places. Used for creating labels.
   */
  Peak.prototype.label = function() {
    return this.center.toFixed(3);
  }

  JSV.Peak = Peak;

})(JSpectraViewer);
//////////////////////////////////////////////////////////////////////////////
// SVSelection
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * The SVSelection object controls selections of peaks, etc on Viewer.
   * Selections do not work with spectra created from xy data. The spectra must
   * be created with compound data or as peak lists.
   * The following options can be set when creating a [SpectraViewer](SpectraViewer.js.html):
   *
   *  Option                | Default     | Description
   *  ----------------------|-------------------------------------------------
   *  element_type          | _undefined_ | What type of elements are to be selected ('peak', 'cluster', 'compound', _undefined_)
   *  allow_multiselect     | true        | Allow the selection of multiple elements using the Shift Key
   *  allow_adjust          | true        | Allow the selected elements to be moved
   *  allow_width_adjust    | true        | Allow the width of the selected elements to be changed
   *  allow_peak_creation   | false       | Allow new peaks to be created by holding 'A' or 'âŒ˜' while clicking on the viewer
   *  show_bounds           | true        | Show the upper and lower bounds markers for the selection on the x-axis, if available
   *  show_indicators       | false       | Show mark on x-axis below each element selected
   *  display_info          | true        | Display selection details in the top corner of the Viewer
   *  constrain_adjust      | _undefined_ | When adjusting the selection, should parent elements of selection also be adjusted. For example if constrain_ajdust is 'compound', then when a peak is adjusted, all the peaks from the parent compound will also be adjusted in amplitude. Options are 'cluster', 'compound' or _undefined_.
   *  restriction_spectrum_id | _undefined  | Resrict selection to only work with the spectrum with this ID.
   *  possible_elements     | _undefined_ | Restrict selection to only these elements [Array or [SVSet](SVSet.js.html)]
   *  display               | {fill: 'rgba(150, 150, 150, 0.2)'} | Display options as described in [SVPath](SVPath.js.html)
   *
   *
   * @param {Object} sv The [SpectraViewer](SpectraViewer.js.html) object
   * @param {Object} options Options for how the selection/adjustments should work. Described below.
   * @return {SVSelection}
   */
  function SVSelection(sv, options) {
    JSV.SVPath.call(this);
    const self = this;
    this.sv = sv;
    this.element_type = options.element_type;
    this.allow_multiselect = JSV.default_for(options.allow_multiselect, true);
    this.allow_adjust = JSV.default_for(options.allow_adjust, true);
    this.allow_width_adjust = JSV.default_for(options.allow_width_adjust, true);
    this.allow_peak_creation = JSV.default_for(options.allow_peak_creation, false);
    this.show_bounds = JSV.default_for(options.show_bounds, true);
    this.show_indicators = JSV.default_for(options.show_indicators, false);
    this.display_info = JSV.default_for(options.display_info, true);
    this.constrain_adjust = options.constrain_adjust
    this.restriction_spectrum_id = options.restriction_spectrum_id;
    this.possible_elements = options.possible_elements;
    display_defaults = { fill: 'rgba(150, 150, 150, 0.2)', visible: true };
    this.display_settings(JSV.merge(display_defaults, options.display));
    this.handle_spacer = JSV.pixel(10);
    this.handle_size = JSV.pixel(15);
    this._elements = new JSV.SVSet();
    this.initialize_selection_events();
    // this.popup_box = this.sv.sv_wrapper.append('div').attr('class', 'jsv-select-popup-box').style('display', 'none');
    this.popup_box = this.sv.sv_wrapper.append('div').attr('class', 'jsv-select-popup-box').style('visibility', 'hidden');
    this.text_container = this.popup_box.append('div').attr('class', 'jsv-select-text-container');
    this.draw = function() {
      if (self.present()) {
        // Call SVPath draw method
        SVSelection.prototype.draw.apply(this, arguments);
        // First argument is the context; Second argument is the scale
        self.draw_adjust_handles(arguments[0], arguments[1]);
      }
      // Draw box with selection information
      (self.present() && self.display_info) ? self.show_popup_box() : self.hide_popup_box();
    }
  }
  JSV.inherits(SVSelection, JSV.SVPath);

  SVSelection.prototype.peaks = function(term) {
    const peaks = new JSV.SVSet();
    let i = 0;
    const len = this._elements.length;
    for (; i < len; i++) {
      peaks.merge(this._elements[i].peaks());
    }
    return peaks.get(term);
  }

  SVSelection.prototype.elements = function(term) {
    return this._elements.get(term);
  }

  SVSelection.prototype.clear = function() {
    this.sv.trigger('selection-clear');
    this._elements = new JSV.SVSet();
    this.sv.trigger('selection-empty');
    return this
  }

  SVSelection.prototype.add_element = function(element) {
    this._elements.push(element);
    // Remove any previous highlighting
    this.sv.highlight.remove();
    this.sv.trigger('selection-add');
    return this
  }

  SVSelection.prototype.remove_element = function(element) {
    this._elements = new JSV.SVSet( this._elements.filter(function(e) { return e != element }) );
    this.sv.trigger('selection-remove');
    if (this.empty()) this.sv.trigger('selection-empty');
    return this
  }

  SVSelection.prototype.size = function() {
    return this._elements.length;
  }

  SVSelection.prototype.present = function() {
    return this._elements.length > 0;
  }

  SVSelection.prototype.empty = function() {
    return this._elements.length == 0;
  }

  SVSelection.prototype.contains = function(element) {
    return this._elements.contains(element);
  }

  SVSelection.prototype.delete = function() {
    this.peaks().each(function() { this.delete(); });
    this.update();
    this.clear();
    this.sv.trigger('adjust-end');
    this.sv.full_draw();
  }

  SVSelection.prototype.hide_popup_box = function() {
    // this.popup_box.style('display', 'none');
    this.popup_box.style('visibility', 'hidden');
  }

  SVSelection.prototype.show_popup_box = function() {
    const element = this.highlighted_element;
    // Increase popup width before adding text, so text_container is not compressed
    this.popup_box.style('width', '100%');
    const position = this.sv.axis_x_reverse ? 'right' : 'left';
    this.popup_box.style(position, this.sv.axis_y_gutter + JSV.pixel(2) + 'px');
    this.text_container.html(this.info_text());
    // this.popup_box.style('display', 'block').style('width', parseInt(this.text_container.style('width')) + 20);
    const box_width = this.text_container.node().offsetWidth + 20;
    this.popup_box.style('visibility', 'visible').style('width', box_width + 'px');
  }

  SVSelection.prototype.info_text = function() {
    let compound_text = '', cluster_text = '', peak_text = '';
    const compounds = this.compounds();
    const clusters = this.clusters();
    const peaks = this.peaks();
    if (compounds.length == 1) {
      const compound = compounds[0];
      compound_text = compound.name;
      if (compound.concentration !== undefined) {
        compound_text + ' [' + compound.display_concentration() + ']';
      }
    } else {
      compound_text = compounds.length + ' compounds';
    }
    if (clusters.length == 1) {
      const cluster = clusters[0];
      cluster_text = 'cluster: ' + cluster.center().toFixed(3) + ' ppm';
    } else {
      cluster_text = clusters.length + ' clusters';
    }
    if (peaks.length == 1) {
      const peak = peaks[0];
      peak_text = 'peak: ' + peak.center.toFixed(3) + ' ppm';
    } else {
      peak_text = peaks.length + ' peaks';
    }
    text = '<p>' + compound_text + '</p><p>' + cluster_text + '</p><p>' + peak_text + '</p>';
    return text;
  }

  SVSelection.prototype.compounds = function(term) {
    const compounds = new JSV.SVSet();
    this.peaks().forEach(function(peak) {
      compounds.push(peak.compound());
    });
    return compounds.unique().get(term);
  }

  SVSelection.prototype.clusters = function(term) {
    const clusters = new JSV.SVSet();
    this.peaks().forEach(function(peak) {
      clusters.push(peak.cluster());
    });
    return clusters.unique().get(term);
  }

  SVSelection.prototype.adjust_concentration = function() {
    return this.element_type == 'compound' || this.constrain_adjust == 'compound';
  }

  SVSelection.prototype.draw_adjust_handles = function(context, scale) {
    const self = this;
    const sv = this.sv;
    let spacer_direction;
    if (self.allow_adjust && self.allow_width_adjust && self.present()) {
      const spacer = this.handle_spacer;
      const handle_size = this.handle_size;
      const domains = sv.get_peak_domains(self.peaks(), true);
      const width_min = scale.x(d3v7.min(domains[0]));
      const width_max = scale.x(d3v7.max(domains[0]));
      if (sv.axis_x_reverse) {
        this.handle_min_x = width_min + spacer;
        this.handle_max_x = width_max - spacer - handle_size;
        spacer_direction = -1;
      } else {
        this.handle_min_x = width_min - spacer - handle_size;
        this.handle_max_x = width_max + spacer;
        spacer_direction = 1;
      }
      const half_height = (scale.y(d3v7.max(domains[1]) / 2));
      this.handle_y = half_height - (handle_size / 2);

      context.translate(0.5, 0.5);
      // Handles
      context.beginPath();
      context.strokeStyle = 'rgba(50, 50, 50, 0.9)';
      context.rect(this.handle_min_x, this.handle_y, handle_size, handle_size);
      context.rect(this.handle_max_x, this.handle_y, handle_size, handle_size);
      context.stroke();

      // Line to Handles
      context.beginPath();
      context.strokeStyle = 'rgba(150, 150, 150, 0.5)';
      context.moveTo(width_min, half_height);
      context.lineTo(width_min - (spacer * spacer_direction), half_height);
      context.moveTo(width_max, half_height);
      context.lineTo(width_max + (spacer * spacer_direction), half_height);

      // context.moveTo(width_x1 - spacer, half_height);
      // context.lineTo(width_x1, half_height);
      // context.moveTo(width_x2, half_height);
      // context.lineTo(width_x2 + spacer, half_height);
      context.stroke();

      context.translate(-0.5, -0.5);
    }
  }

  // Draw bounds of single selected element, if the element
  // has lower_bound and upper_bound properties and a center function.
  SVSelection.prototype.draw_bounds = function(context, scale) {
    const self = this;
    const size = JSV.pixel(10);
    const sv = this.sv;
    if ( should_draw_bounds() ) {
      const element = this.elements(1);
      const domains = sv.get_peak_domains(self.peaks(), true);
      const y = scale.y.range()[0];
      const lower_x = scale.x(element.lower_bound);
      const upper_x = scale.x(element.upper_bound);
      const center = scale.x(element.center());

      context.save();
      context.translate(0.5, 0.5);
      context.beginPath();
      context.lineWidth = 2;
      // Set color
      if ( (element.center() < element.lower_bound) || (element.center() > element.upper_bound) ) {
        context.strokeStyle = 'red';
        context.fillStyle = 'red';
      } else {
        context.strokeStyle = 'blue';
        context.fillStyle = 'white';
      }
      // upper bound
      context.moveTo(lower_x, y);
      context.lineTo(lower_x + size, y - size);
      context.lineTo(lower_x + size, y + size);
      context.lineTo(lower_x, y);
      // lower bound
      context.moveTo(upper_x, y);
      context.lineTo(upper_x - size, y - size);
      context.lineTo(upper_x - size, y + size);
      context.lineTo(upper_x, y);
      // center line
      context.moveTo(center, y - size);
      context.lineTo(center, y + size);

      context.fill();
      context.stroke();

      context.translate(-0.5, -0.5);
      context.restore();
    }

    function should_draw_bounds() {
      const element = self.elements(1);
      return self.show_bounds &&
          self.size() == 1 &&
          typeof element.center == 'function' &&
          JSV.isNumeric( element.center() ) &&
          JSV.isNumeric( element.lower_bound ) &&
          JSV.isNumeric( element.upper_bound );
    }
  }

  // Draw indicators on x-axis for each element selected
  SVSelection.prototype.draw_indicators = function(context, scale) {
    const self = this;
    const size = JSV.pixel(10);
    const sv = this.sv;
    if ( self.show_indicators ) {
      const y = scale.y.range()[0];
      context.save();
      context.translate(0.5, 0.5);
      self.elements().forEach(function(element) {
        let center;
        if (typeof element.center == 'function') {
          center = element.center();
        } else {
          center = element.center;
        }
        if (center !== undefined) {
          center = scale.x(center);
          context.beginPath();
          context.lineWidth = JSV.pixel(2);
          // Set color
          context.strokeStyle = 'green';
          context.fillStyle = 'green';
          context.moveTo(center, y - size);
          context.lineTo(center, y + size);

          context.fill();
          context.stroke();
        }
      });

      context.translate(-0.5, -0.5);
      context.restore();
    }

  }

  SVSelection.prototype.mouse_in_handle = function() {
    const self = this;
    const sv = this.sv;
    let handle;
    if (self.allow_adjust && self.allow_width_adjust && self.present()) {
      const x = sv.scale.x(sv.mx);
      const y = sv.scale.y(sv.my);
      if ( (y > this.handle_y) && (y < this.handle_y + this.handle_size) ) {
        if ( (x > this.handle_max_x) && (x < this.handle_max_x + this.handle_size) ) handle = 'left';
        if ( (x > this.handle_min_x) && (x < this.handle_min_x + this.handle_size) ) handle = 'right';
      }
    }
    return handle;
  }

  SVSelection.prototype.mouse_in_selection = function() {
    const self = this;
    const sv = this.sv;
    if (self.present()) {
      const x = sv.scale.x(sv.mx);
      const y = sv.scale.y(sv.my);
      const y_for_mouse_x = JSV.sum_of_peaks(sv.mx, self.peaks());
      return (sv.my <= y_for_mouse_x && sv.my >= 0);
    }
  }

  SVSelection.prototype.update = function() {
    const paths = new JSV.SVSet();
    this.constrained_peaks().forEach(function(peak) {
      paths.push(peak, peak.cluster(), peak.compound(), peak.spectrum());
    });
    paths.unique().forEach(function(p) { p.update(); });
  }

  SVSelection.prototype.constrained_peaks = function() {
    const self = this;
    let peaks;
    if (self.constrain_adjust) {
      peaks = new JSV.SVSet();
      self.peaks().forEach(function(peak) {
        if (peak[self.constrain_adjust]) {
          peaks.merge(peak[self.constrain_adjust]().peaks());
        }
      });
      peaks = peaks.unique();
    } else {
      peaks = self.peaks();
    }
    return peaks;
  }

  /**
   * Initialize Selectiona and  Adjust Fit.
   */
  SVSelection.prototype.initialize_selection_events = function() {
    const sv = this.sv;
    const self = this;
    let x_mouse, y_mouse, dx_mouse, dy_mouse, dx_axis, dy_axis, selected_handle;
    let select_rect, added_elements, select_box_elements, x_start, y_start;
    let max_amplitude, new_amplitude;
    let do_not_clear;

    sv.svg.on('mousedown.selection', function(event) {
      // initial_selected_elements = self.elements();
      do_not_clear = false;
      const down_x = event.x;
      const down_y = event.y;
      if (active_selection(event) && self.allow_adjust) {
        adjuststart();
        sv.svg.on('mousemove.fit', function() { adjusting() });
        sv.svg.on('mouseup.fit', function() {
          adjustend();
          sv.svg.on('mousemove.fit', null);
          sv.svg.on('mouseup.fit', null);
        })
      } else if (select_box_mode(event) && self.allow_multiselect) {
        selectstart();
        sv.svg.on('mousemove.fit', function() { selecting() });
        sv.svg.on('mouseup.fit', function() {
          selectend();
          sv.svg.on('mousemove.fit', null);
          sv.svg.on('mouseup.fit', null);
        })
      } else {
        sv.svg.on('mouseup.selection', function(event) {
          if (down_x == event.x && down_y == event.y) {
            if (create_peak_mode(event)) {
              create_peak();
            } else if (!do_not_clear) {
              self.clear();
            }
          }
          sv.svg.on('mouseup.selection', null);
        });
      }
    })

    function create_peak_mode(event) {
      // 65: 'a'
      // return ( self.allow_peak_creation && ( sv.create_peak_mode || (sv.keyval() == '65') || (d3v7.event.altKey && d3v7.event.metaKey) ) );
      return ( self.allow_peak_creation && ( sv.create_peak_mode || (sv.keyval() == '65') || (event.metaKey) ) );
    }

    function multi_select_mode(event) {
      // 83: 's'
      // return ( self.allow_multiselect && ((sv.keyval() == '83') || (d3v7.event.altKey)) );
      return ( self.allow_multiselect && ((sv.keyval() == '83') || (event.shiftKey)) );
    }

    function select_box_mode(event) {
      return ( multi_select_mode(event) && !self.mouse_in_handle() && !find_element_mouse_over() );
    }

    function find_element_mouse_over() {
      return sv.find_element_mouse_over(self.element_type, self.restriction_spectrum_id, self.possible_elements, self.visible_only);
    }

    function active_selection(event) {
      if (self.mouse_in_handle()) return true;
      if (create_peak_mode(event)) return false;
      let active;

      const element = find_element_mouse_over();
      const mouse_in_selection = self.mouse_in_selection();

      if (element || mouse_in_selection) {
        // Multiselect Mode: remove or add elements
        if (multi_select_mode(event)) {
          if (self.contains(element)) {
            self.remove_element(element);
            active = false;
            do_not_clear = true;
          } else if (!mouse_in_selection) {
            self.add_element(element);
            active = true;
          }
          // Selecting new element
        } else if (!mouse_in_selection) {
          self.clear();
          self.add_element(element);
          // sv.trigger('element_selected');
          active = true;
          do_not_clear = true;
          // Mouse is under sum line of selection
        } else {
          active = true;
        }
        // sv.full_draw();
        sv.fast_calc_draw();
      } else {
        // No element selected
        // self.clear();
        active = false
      }
      return active;
    }

    function adjuststart() {
      x_mouse = d3v7.event.x;
      y_mouse = d3v7.event.y;
      selected_handle = self.mouse_in_handle();
      sv.svg.style('cursor', 'grabbing');
    }

    function adjusting() {
      d3v7.event.stopPropagation(); // silence other listeners
      do_not_clear = true;
      dx_mouse = d3v7.event.x - x_mouse;
      dy_mouse = d3v7.event.y - y_mouse;
      x_mouse = d3v7.event.x
      y_mouse = d3v7.event.y
      dx_axis = sv.pixels_to_units_of_axis('x', dx_mouse);;
      dy_axis = sv.pixels_to_units_of_axis('y', dy_mouse);;
      if (selected_handle) {
        // Set width
        const direction = selected_handle == 'right' ? 1 : -1;
        self.peaks().forEach(function(peak) {
          // TODO: Scale the width change to the maximum width
          peak.width = peak.width - (direction * dx_axis * 2);
          if (peak.width < 0.00001) peak.width = 0.00001;
        });
      } else {
        // Save original amplitudes for concentration adjustments
        if (self.adjust_concentration()) {
          self.compounds().forEach(function(c) {
            c.orig_amplitude = d3v7.max(c.peaks(), function(p) { return p.amplitude; });
          });
        }
        // Set amplitude
        // Scale the amplitude change to the maximum peak
        const peaks = self.constrained_peaks();
        max_amplitude = d3v7.max(peaks, function(p) { return p.amplitude; });
        if (max_amplitude <= 0) max_amplitude = 0.00001;
        peaks.forEach(function(peak) {
          new_amplitude = peak.amplitude + ( dy_axis / max_amplitude * peak.amplitude );
          peak.set_amplitude(new_amplitude);
        });
        // Set center
        self.peaks().forEach(function(peak) {
          peak.set_center(peak.center + dx_axis);
        });
        // Adjust concentrations
        if (self.adjust_concentration()) {
          self.compounds().forEach(function(c) {
            const highest_amplitude = d3v7.max(c.peaks(), function (p) {
              return p.amplitude;
            });
            c.concentration = c.concentration * highest_amplitude / c.orig_amplitude;
          });
        }
      }
      // TODO: scroll viewer when dragging selection outside of view
      sv.trigger('adjust');
      sv.fast_calc_draw();
    }


    function adjustend() {
      self.update();
      sv.svg.style('cursor', 'grab');
      sv.trigger('adjust-end', self.compounds());
      sv.full_draw();
    }

    function selectstart() {
      // if (self.empty()) self._elements = initial_selected_elements;
      added_elements = new JSV.SVSet();
      const mouse_pos = d3v7.mouse(sv.canvas);
      x_start = mouse_pos[0];
      y_start = mouse_pos[1];
      select_rect = sv.svg.append('rect').
      attr('class', 'select-box').
      attr('x', x_start).attr('y', y_start).attr('width', 0).attr('height', 0);
    }

    function selecting() {
      d3v7.event.stopPropagation(); // silence other listeners
      do_not_clear = true;
      const mouse_pos = d3v7.mouse(sv.canvas);
      x_new = mouse_pos[0];
      y_new = mouse_pos[1];
      const select_x = d3v7.min([x_start, x_new]);
      const select_y = d3v7.min([y_start, y_new]);
      const width = Math.abs(x_new - x_start);
      const height = Math.abs(y_new - y_start);
      select_rect.attr('x', select_x).attr('y', select_y).
      attr('width', width).attr('height', height);
      const select_box_elements = elements_in_select_box(select_x, select_y, width, height);
      // Add new elements to selection
      select_box_elements.forEach(function(element) {
        if (!self.contains(element)) {
          self.add_element(element);
          added_elements.push(element);
        }
      });
      // Remove elements that are no longer selected
      added_elements = new JSV.SVSet(added_elements.filter(function(element) {
        if (!select_box_elements.contains(element)) {
          self.remove_element(element);
          return false;
        }
        return true;
      }));
      sv.fast_draw();
    }

    function selectend() {
      select_rect.remove();
      sv.full_draw();
    }

    function elements_in_select_box(x, y, width, height) {
      const selected_elements = new JSV.SVSet();
      let x_plot;
      const y_plot = sv.scale.y.invert(JSV.pixel(y + height));
      const elements = sv.elements(self.element_type, self.restriction_spectrum_id, self.possible_elements, self.visible_only);
      let e = 0;
      const len = elements.length;
      for (; e < len; e++) {
        const element = elements[e];
        for (let i = 0; i < width; i++) {
          x_plot = sv.scale.x.invert( JSV.pixel(x + i) );
          if (y_plot < JSV.sum_of_peaks(x_plot, element.peaks())) {
            selected_elements.push(element);
            break;
          }
        }
      }
      return selected_elements;
    }

    function create_peak() {
      let cluster, width;
      if (self.present()) {
        // Add peak to cluster of first peak in selection
        // TODO: change to cluster of closest peak to mouse click
        cluster = self.peaks(1).cluster();
        width = d3v7.mean(self.peaks(), function(p) { return p.width});
      } else {
        // Add peak to new cluster of first compound of first spectra (or to restriction_spectrum)
        let spectrum;
        if (self.restriction_spectrum_id) {
          spectrum = sv.spectra(self.restriction_spectrum_id);
        } else {
          spectrum = sv.spectra().filter(function(s) { return s.compounds().present(); })[0];
        }
        cluster = new JSV.Cluster();
        spectrum.compounds(1).add_cluster(cluster);
        if (spectrum.peaks().present()) {
          width = d3v7.mean(spectrum.peaks(), function(p) { return p.width; });
        } else {
          width = 0.003;
        }
      }
      const peak = new JSV.Peak({width: width, center: 0});
      cluster.add_peak(peak);
      const mouse_xy = sv.mouse(sv.canvas);
      peak.set_center(sv.scale.x.invert(mouse_xy[0]));
      peak.set_amplitude(sv.scale.y.invert(mouse_xy[1]));
      sv.trigger('adjust-peak-created', peak);
      if (self.element_type == 'peak') {
        self.add_element(peak);
      } else if (self.empty() && self.element_type == 'cluster') {
        self.add_element(cluster);
      } else if (self.empty() && self.element_type == 'compound') {
        self.add_element(cluster.compound());
      }
      adjustend();
    }
  }
  /** @ignore */

  JSV.SVSelection = SVSelection;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Highlighter
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * The SVHighlighter object controls highlighting of peaks, etc on Viewer
   * when the mouse hovers over them.  Highlighting does not work with spectra
   * created from xy data. The spectra must be created with compound data or as
   * peak lists.  The following options can be set when creating a
   * [SpectraViewer](SpectraViewer.js.html):
   *
   *  Option                | Default     | Description
   *  ----------------------|-------------------------------------------------
   *  element_type          | _undefined_ | What type of elements are to be selected ('peak', 'cluster', 'compound', _undefined_)
   *  restriction_spectrum_id | _undefined_  | Resrict highlighting to only work with the spectrum with this ID.
   *  possible_elements     | _undefined_ | Restrict highlighting to only these elements [Array or [SVSet](SVSet.js.html)]
   *  visible_only          | true        | Restrict highlighting to only visible elements
   *  text_display          | true        | Show popup with description of highlighted element. A custom display string can be provided to display specific information about the element. See below for examples.
   *  display               | {lineWidth: 3} | Display options as described in [SVPath](SVPath.js.html)
   *
   * ####Custom Text Display####
   *
   * If a String is provided for _text_display_ it will parsed first to replace sections
   * with this format: #{T:name} where T is how to extract the information and _name_ is the property/function name to call on the element
   * T can be one of:
   *   * p: property (e.g. element.name)
   *   * f: function (e.g. element.name( ))
   *   * m: meta property (e.g. element.meta.name)
   *
   * Example:
   *
   * If _text_display_ was "Compound: #{p:name} [#{f:display_concentration}]", this would
   * this would display something like the following:
   *   Compound: Glucose [132 uM]
   *
   *
   * @param {Object} sv The [SpectraViewer](SpectraViewer.js.html) object
   * @param {Object} options Options for how highlighting should work. Described below.
   * @return {SVSelection}
   */
  function SVHighlighter(sv, options) {
    this.sv = sv;
    this.element_type = options.element_type;
    this.restriction_spectrum_id = options.restriction_spectrum_id;
    this.possible_elements = options.possible_elements;
    this.visible_only = JSV.default_for(options.visible_only, true);
    this.text_display = JSV.default_for(options.text_display, true);
    display_defaults = { lineWidth: 3, visible: true };
    this.display = JSV.merge(display_defaults, options.display);
    // this.popup_box = this.sv.sv_wrapper.append('div').attr('class', 'jsv-highlight-popup-box').style('display', 'none');
    this.popup_box = this.sv.sv_wrapper.append('div').attr('class', 'jsv-highlight-popup-box').style('visibility', 'hidden');
    this.text_container = this.popup_box.append('div').attr('class', 'jsv-highlight-text-container');
  }

  SVHighlighter.prototype.hover = function() {
    const sv = this.sv;
    if (this.element_type) {
      const old_element = this.highlighted_element;
      let element = sv.find_element_mouse_over(this.element_type, this.restriction_spectrum_id, this.possible_elements, this.visible_only);
      if ( sv.selection.mouse_in_selection() || sv.selection.mouse_in_handle() ) {
        element = undefined;
      }
      if (old_element != element) {
        this.highlighted_element = element;
        // Remove previous highlighting
        if (old_element) {
          old_element.display_settings(this.saved_display_settings);
          this.hide_popup_box();
        }
        // Highlight new element
        if (element) {
          this.saved_display_settings = element.display_settings();
          element.display_settings(this.display);
          this.show_popup_box();
        }
        // sv.calc_draw();
        sv.full_draw();
      }
    }
  }

  SVHighlighter.prototype.remove = function() {
    if (this.highlighted_element) {
      this.highlighted_element.display_settings(this.saved_display_settings);
      this.highlighted_element = undefined;
      this.hide_popup_box();
    }
  }

  SVHighlighter.prototype.hide_popup_box = function() {
    this.sv.trigger('highlight-end');
    // this.popup_box.style('display', 'none');
    this.popup_box.style('visibility', 'hidden');
  }

  SVHighlighter.prototype.show_popup_box = function() {
    const element = this.highlighted_element;
    let text = '';
    if (this.text_display === true) {
      text = this.default_text();
    } else if (typeof this.text_display == 'string') {
      text = this.parsed_text();
    }
    this.sv.trigger('highlight-start');
    if (this.text_display) {
      // Increase popup width before adding text, so text_container is not compressed
      this.popup_box.style('width', '100%');
      this.text_container.html(text);
      // this.popup_box.style('display', 'block').style('width', parseInt(this.text_container.style('width')) + 20);
      const box_width = this.text_container.node().offsetWidth + 20;
      // Alter position if menu is showing
      const top = this.sv.menu.visible() ? this.sv.menu.height() + 5 : 15;
      // Show
      this.popup_box.style('visibility', 'visible')
          .style('top', top + 'px')
          .style('width', box_width + 'px');
    }
  }

  SVHighlighter.prototype.default_text = function() {
    let text = '';
    if (this.element_type == 'peak') {
      text = 'Peak: ' + this.highlighted_element.center.toFixed(3) + ' ppm';
    } else if (this.element_type == 'cluster') {
      text = 'Cluster: ' + this.highlighted_element.center().toFixed(3) + ' ppm';
    } else if (this.element_type == 'compound') {
      text = this.highlighted_element.name;
    } else if (this.element_type == 'spectrum') {
      text = this.highlighted_element.name;
    }
    return text;
  }


  SVHighlighter.prototype.parsed_text = function() {
    const element = this.highlighted_element;
    const parser = function (match, p1, p2) {
      let text;
      if (p1 == 'p') {
        text = element[p2];
      } else if (p1 == 'f') {
        text = element[p2]();
      } else if (p1 == 'm') {
        text = element.meta[p2];
      }
      return text;
    };
    // 'bob#{a:1}test#{b:2}'.replace(/\#\{(.):(.*?)\}/g, function(match, p1, p2) {return ' - ' + p2 + ' - '})
    return this.text_display.replace(/#\{(.):(.*?)\}/g, parser);
  }

  JSV.SVHighlighter = SVHighlighter;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// SpectraViewer ClusterNavigator
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  function SVClusterNavigator(sv, id) {
    const self = this;
    this.sv = sv;
    this.id = id;
    this.speed = 400;
    this.active_ids = [];
    this.container = d3v7.select(id);
    // If set at some point the navigator will always show this compound
    // If undefined the current selection will be use
    this.static_compounds;
    this.table = this.container.append('table').style('display', 'none')
        .attr('class', 'jsv-cluster-navigator');
    this.thead = this.table.append('thead');
    // this.thead.html('<tr><th>Compound</th><th>Concentration</th><th>Clusters</th></tr>')
    this.tbody = this.table.append('tbody');

    sv.on('selection-add.cluster-navigator', function() {
      self.table.style('display', 'block');
      self.update_table();
    });
    if (sv.selection.present()) sv.trigger('selection-add.cluster-navigator');

    sv.on('selection-remove.cluster-navigator', function() {
      self.update_table();
    });

    sv.on('adjust-end.cluster-navigator', function() {
      self.update_table();
    });

    sv.on('selection-empty.cluster-navigator', function() {
      if (!self.static_compounds) {
        self.table.style('display', 'none');
      }
    });


    d3v7.select('.jsv-cluster-navigator')
        .on('click', function(event) {
          const target = d3v7.select(event.target);
          window.getSelection().removeAllRanges()
          if (target.classed('cluster-unit')) {
            self.move_to_cluster(target.node());
          } else if (target.classed('jsv-cluster-nav')) {
            self.cycle_clusters(target);
          }
        });
  }

  SVClusterNavigator.prototype.update_table = function() {
    const compounds = this.static_compounds || this.sv.selection.compounds();
    // this.tbody.html( update_rows(this.sv.selection.compounds()) );
    this.tbody.html( update_rows(compounds) );
    if (this.active_ids.length > 0) {
      d3v7.selectAll(this.active_ids.join(',')).classed('active', true);
    }
    this.sv.trigger('cluster-navigator-updated')
  }

  const update_rows = function(compounds) {
    return compounds.map(function(c) { return compound_row(c); }).join('');
  }

  const compound_row = function(compound) {
    html = '<tr>'
    if (compound.name !== undefined) {
      html += '<td>' + compound.name + '</td>'
    }
    if (compound.concentration !== undefined) {
      html += '<td>' + compound.display_concentration() + '</td>'
    }
    html += '<td>' + cluster_cell(compound.clusters()) + '</td></tr>';
    return html
    // return '<tr><td>'+ compound.name + '</td><td>' + compound.display_concentration() + '</td><td>' + cluster_cell(compound.clusters()) + '</td></tr>';
  }

  const cluster_cell = function(clusters) {
    clusters.order_by('center', true);
    const nav_arrows = "<span class='jsv-cluster-nav jsv-cluster-nav-left'>&#10094;</span>&nbsp;<span class='jsv-cluster-nav jsv-cluster-nav-right'>&#10095;</span>";
    return nav_arrows + clusters.map(function(c) { return cluster_div(c); }).join('');
  }
  const cluster_div = function(cluster) {
    return '<div class="cluster-unit" style="display: inline-block; text-decoration: underline; text-decoration-style: dashed;" id="' + cluster.path_id() + '">' + cluster.center().toFixed(2) + '</div>';
  }

  SVClusterNavigator.prototype.cycle_clusters = function(target) {
    const forward = target.classed('jsv-cluster-nav-right');
    console.log("target", target)
    const parent = d3v7.select(target.node().parentNode);
    console.log("parent", parent);
    const active = parent.select('.active');
    const cluster_units = Array.from(parent.selectAll('.cluster-unit'));
    console.log("cluster_units", cluster_units);
    const cluster_ids = cluster_units.map(function (c) {
      return c.id;
    });
    let index = active.node() ? cluster_ids.indexOf(active.node().id) : -1;

    if (index < 0) {
      index = 0;
    } else if (forward) {
      index = (index + 1 < cluster_ids.length) ? index + 1 : 0;
    } else {
      index = (index - 1 < 0) ? cluster_ids.length - 1 : index -1;
    }
    const new_cluster = parent.select('#' + cluster_ids[index]);
    this.move_to_cluster(new_cluster.node());
  }

  SVClusterNavigator.prototype.move_to_cluster = function(cluster_node) {
    const cluster = sv.clusters(cluster_node.id);
    sv.move_to_peaks(cluster.peaks(), this.speed, 2);
    d3v7.select(cluster_node.parentNode).selectAll('.cluster-unit').classed('active', false);
    d3v7.select(cluster_node).classed('active', true);
    this.active_ids = d3v7.selectAll('.cluster-unit.active')[0].map(function(c) { return '#' + c.id; });
    sv.selection.clear();
    sv.selection.add_element(cluster);
  }

  SVClusterNavigator.prototype.detach = function() {
    this.sv.off('.cluster-navigator');
    this.table.remove();
  }

  Object.defineProperties(SVClusterNavigator.prototype, {
    'static_compounds': {
      get: function() {
        return this._static_compounds;
      },
      set: function(compounds) {
        this._static_compounds = compounds;
        this.table.style('display', 'block');
        this.update_table();
      }
    }
  });

  JSV.SVClusterNavigator = SVClusterNavigator;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Menu
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  const download_image = function(sv, dialog) {
    const height = sv.sv_wrapper.select('#jsv-save-height').property('value');
    const width = sv.sv_wrapper.select('#jsv-save-width').property('value');
    const image = sv.image(width, height);
    const window_name = 'JSV-Image-' + width + 'x' + height;
    const win = window.open(image, window_name);
    dialog.close();
    setTimeout(function() { win.document.title = window_name }, 100);
  };
  const download_html = function(sv) {
    return   '' +
        '<div class="jsv-alert">Display the viewer image in a new window to download or print. Note that you must allow pop-ups!</div>' +
        '<div><label class="jsv-label">Width</label><div class="jsv-input-group">' +
        '<input class="jsv-input" id="jsv-save-width" type="text" value="' + sv.width + '" /><div class="jsv-input-addon">px</div></div></div>' +
        '<div><label class="jsv-label">Height</label><div class="jsv-input-group">' +
        '<input class="jsv-input" id="jsv-save-height" type="text" value="' + sv.height + '" /><div class="jsv-input-addon">px</div></div></div>';
  };
  const line_function = d3v7.line()
      .x(function(d) { return d.x; })
      .y(function(d) { return d.y; })
      .curve(d3v7.curveLinear);
  const button = function(svg, x, y, width, height, path_group) {
    const button_group = svg.append('g').attr('class', 'jsv-menu-button');
    button_group.append('rect')
        .attr('x', 0)
        .attr('y', 0)
        .attr('width', width)
        .attr('height', height)
        .attr('rx', 2)
        .attr('ry', 2)
        .style('stroke-width', 1);

    const path = path_group.remove();
    button_group.append('g').
    attr('class', 'jsv-button-image').
    append(function() { return path.node(); });

    button_group.attr('transform', 'translate(' + x + '.5,' + y + '.5)')

    return button_group;
  };
  const download_path = function(svg) {
    const group = svg.append('g');
    const stroke_width = 3;
    group.append('line')
        .attr('x1', 10)
        .attr('y1', 0)
        .attr('x2', 10)
        .attr('y2', 12)
        .attr('stroke-linecap', 'round')
        .attr('stroke-width', stroke_width);;
    group.append('line')
        .attr('x1', 6)
        .attr('y1', 7)
        .attr('x2', 10)
        .attr('y2', 12)
        .attr('stroke-linecap', 'round')
        .attr('stroke-width', stroke_width);
    group.append('line')
        .attr('x1', 14)
        .attr('y1', 7)
        .attr('x2', 10)
        .attr('y2', 12)
        .attr('stroke-linecap', 'round')
        .attr('stroke-width', stroke_width);
    group.append('line')
        .attr('x1', 2)
        .attr('y1', 16)
        .attr('x2', 18)
        .attr('y2', 16)
        .attr('stroke-linecap', 'round')
        .attr('stroke-width', stroke_width);

    return group;
  };
  const settings_path = function(svg) {
    const group = svg.append('g');
    const stroke_width = 4;
    group.append('circle')
        .attr('cx', 10)
        .attr('cy', 10)
        .attr('r', 7)
        .style('fill', 'rgb(75, 75, 75');
    group.append('line')
        .attr('x1', 10)
        .attr('y1', 1)
        .attr('x2', 10)
        .attr('y2', 19)
        .attr('stroke-width', stroke_width);
    group.append('line')
        .attr('x1', 1)
        .attr('y1', 10)
        .attr('x2', 19)
        .attr('y2', 10)
        .attr('stroke-width', stroke_width);
    group.append('line')
        .attr('x1', 3.5)
        .attr('y1', 3.5)
        .attr('x2', 16.5)
        .attr('y2', 16.5)
        .attr('stroke-width', stroke_width);
    group.append('line')
        .attr('x1', 16.5)
        .attr('y1', 3.5)
        .attr('x2', 3.5)
        .attr('y2', 16.5)
        .attr('stroke-width', stroke_width);
    group.append('circle')
        .attr('cx', 10)
        .attr('cy', 10)
        .attr('r', 3)
        .style('fill', 'white');

    return group;
  };
  const scale_path = function(svg, x, y, width, height, angle) {
    const group = svg.append('g');
    const stroke_width = 1;
    const gap = 2;
    const y1_with_gap = y + gap;
    const y2_with_gap = y + height - gap;
    const head_len = 2;
    const center = x + (width / 2);
    group.append('line')
        .attr('x1', x)
        .attr('y1', y)
        .attr('x2', x + width)
        .attr('y2', y)
        .attr('stroke-width', stroke_width);
    group.append('line')
        .attr('x1', x)
        .attr('y1', y + height)
        .attr('x2', x + width)
        .attr('y2', y + height)
        .attr('stroke-width', stroke_width);
    group.append('line')
        .attr('x1', center)
        .attr('y1', y1_with_gap)
        .attr('x2', center)
        .attr('y2', y2_with_gap)
        .attr('stroke-width', stroke_width);
    group.append('line')
        .attr('x1', center)
        .attr('y1', y1_with_gap)
        .attr('x2', center - head_len)
        .attr('y2', y1_with_gap + head_len)
        .attr('stroke-width', stroke_width);
    group.append('line')
        .attr('x1', center)
        .attr('y1', y1_with_gap)
        .attr('x2', center + head_len)
        .attr('y2', y1_with_gap + head_len)
        .attr('stroke-width', stroke_width);
    group.append('line')
        .attr('x1', center)
        .attr('y1', y2_with_gap)
        .attr('x2', center - head_len)
        .attr('y2', y2_with_gap - head_len)
        .attr('stroke-width', stroke_width);;
    group.append('line')
        .attr('x1', center)
        .attr('y1', y2_with_gap)
        .attr('x2', center + head_len)
        .attr('y2', y2_with_gap - head_len)
        .attr('stroke-width', stroke_width);;

    group
        .attr('stroke', 'rgb(150, 150, 150)')
        .attr('transform', 'rotate(' + angle + ',' + x + ',' + y + ')');
    return group;
  };
  const minus_path = function(svg) {
    return svg.append('line')
        .attr('x1', 3)
        .attr('y1', 8)
        .attr('x2', 13)
        .attr('y2', 8)
        .attr('stroke-width', 3)
        .attr('stroke', 'black');
  };
  const plus_path = function(svg) {
    const group = svg.append('g');
    group.append('line')
        .attr('x1', 3)
        .attr('y1', 8)
        .attr('x2', 13)
        .attr('y2', 8)
        .attr('stroke-width', 3)
        .attr('stroke', 'black');

    group.append('line')
        .attr('x1', 8)
        .attr('y1', 3)
        .attr('x2', 8)
        .attr('y2', 13)
        .attr('stroke-width', 3)
        .attr('stroke', 'black');

    return group;
  };
  const path = function(svg, path_data) {
    return svg.append('path')
        .attr('d', line_function(path_data))
        .attr('stroke', 'black')
        // .attr('stroke-linecap', 'round')
        .attr("stroke-width", 3)
        .attr("fill", "none");
  };
// NOTE: need to explicitly state menu and handle sizes here and not just in CSS
  const scroll_interval = function(sv, axis, translate_px, delay) {
    return setInterval(function() {
      sv.translate_axis(axis, translate_px);
      sv.fast_draw();
    }, delay)
  };

// in order to work with hidden elements like tabs
  function SVMenu(sv) {
    const self = this;
    this.sv = sv;
    this.slide_time = 500;
    this._visible = true;
    this.menu = sv.sv_wrapper.append('div')
        .style('visibility', 'visible')
        .attr('class', 'jsv-menu')
        .on('click', function() { window.getSelection().removeAllRanges() });

    this.menu_svg = this.menu.append('svg')
        .attr('width', this.width())
        .attr('height', this.height());

    this.handle = sv.sv_wrapper.append('div')
        .attr('class', 'jsv-menu-handle')
        .on('click', function() {
          if (self.opened()) {
            self.close();
          } else {
            self.open();
          }
        })
        .on('mouseover', function() { self.handle_mouseover(); })
        .on('mouseout', function() { self.handle_mouseout(); });
    const handle_width = 40;
    const handle_height = 12;

    this.handle_svg = this.handle.append('svg')
        .attr('width', handle_width)
        .attr('height', handle_height);

    this.stroke_width = 4
    this.handle_data_closed = [ {x: 0, y: 0}, {x: handle_width/2, y: handle_height - this.stroke_width}, {x: handle_width, y: 0} ];
    this.handle_data_opened = [ {x: 0, y: handle_height}, {x: handle_width/2, y: this.stroke_width}, {x: handle_width, y: handle_height} ];

    this.draw();
    sv.trigger('domain-change.menu');
    // this.close(0);
  }

  SVMenu.prototype.visible = function(value) {
    if (arguments.length == 0) return this._visible;
    if (value) {
      this._visible = true;
      this.handle.style('visibility', 'visible');
      this.menu.style('visibility', 'visible');
    } else {
      this._visible = false;
      this.handle.style('visibility', 'hidden');
      this.menu.style('visibility', 'hidden');
    }
  }

  SVMenu.prototype.opened = function() {
    return (this.menu.style('visibility') == 'visible');
  }

  SVMenu.prototype.width = function() {
    // return this.menu.node().offsetWidth;
    // return this.menu.node().getBoundingClientRect().width;
    return 300;
  }

  SVMenu.prototype.height = function() {
    // return this.menu.node().offsetHeight;
    // return this.menu.node().getBoundingClientRect().height;
    return  41;
  }

  SVMenu.prototype.open = function(duration) {
    duration = JSV.default_for(duration, this.slide_time)
    this.menu.style('visibility', 'visible');
    this.menu.transition().duration(duration)
        .style('top', '0px')
        .style('opacity', 1);

    this.handle_path.transition().duration(duration).attr('d', line_function(this.handle_data_opened))
  }

  SVMenu.prototype.close = function(duration) {
    duration = JSV.default_for(duration, this.slide_time)
    this.menu.transition().duration(duration)
        .style('top', '-50px')
        .style('opacity', 0)
        .on('end', function() {
          d3v7.select(this).style('visibility', 'hidden');
        });

    this.handle_path.transition().duration(duration).attr('d', line_function(this.handle_data_closed))
  }

  SVMenu.prototype.handle_mouseover = function() {
    this.handle_path.transition().duration(200)
        .attr('stroke', 'black');
  }

  SVMenu.prototype.handle_mouseout = function() {
    this.handle_path.transition().duration(200)
        .attr('stroke', 'grey');
  }

  SVMenu.prototype.draw = function() {
    const sv = this.sv;
    const self = this;
    let timeout;
    let translate_px = 5;
    let mousedown_delay = 4;

    // Handle
    this.handle_path = this.handle_svg.append("path")
        .attr("d", line_function(this.handle_data_closed))
        .attr("stroke", "grey")
        .attr("stroke-width", this.stroke_width)
        .attr("fill", "none");

    if(this.sv.constructor.name === 'SpectraViewer') {
      // Scroll/Move Buttons
      const left_arrow_data = [{x: 11, y: 4}, {x: 4, y: 15}, {x: 11, y: 26}];
      const right_arrow_data = [{x: 4, y: 4}, {x: 11, y: 15}, {x: 4, y: 26}];

      const left_arrow = path(this.menu_svg, left_arrow_data);
      const right_arrow = path(this.menu_svg, right_arrow_data);

      this.nav_group = this.menu_svg.append('g');
      this.scroll_left_button = button(this.nav_group, 0, 0, 15, 30, left_arrow);
      this.scroll_right_button = button(this.nav_group, 17, 0, 15, 30, right_arrow);
      this.nav_group.attr('transform', 'translate(' + 7 + ',' + 4 + ')');

      this.scroll_left_button.on('mousedown', function () {
        if (d3v7.select(this).classed('disabled')) return;
        timeout = scroll_interval(sv, 'x', translate_px, mousedown_delay);
        return false;
      })

      this.scroll_right_button.on('mousedown', function () {
        if (d3v7.select(this).classed('disabled')) return;
        timeout = scroll_interval(sv, 'x', -translate_px, mousedown_delay);
        return false;
      })

      $(document).mouseup(function () {
        if (timeout) {
          clearInterval(timeout);
          sv.full_draw();
        }
      });
    } else {
      // reset zoom button
      this.reset_zoom_button = button(this.menu_svg, 10, 4, 30, 30, mag_path(this.menu_svg));
      this.reset_zoom_button.on('click', function() {
        if (d3v7.select(this).classed('disabled')) return;
        const new_domains =  [ [-10, 500], [-10, 500]  ];
        sv.move_to(new_domains, 1000)
      })
    }

    // Zoom Buttons
    this.zoom_group = this.menu_svg.append('g');
    this.zoom_y_minus_button = button(this.zoom_group, 6, 18, 16, 16, minus_path(this.menu_svg));
    this.zoom_y_plus_button = button(this.zoom_group, 6, 0, 16, 16, plus_path(this.menu_svg));
    this.zoom_x_minus_button = button(this.zoom_group, 25, 9, 16, 16, minus_path(this.menu_svg));
    this.zoom_x_plus_button = button(this.zoom_group, 43, 9, 16, 16, plus_path(this.menu_svg));
    scale_path(this.zoom_group, 0, 0.5, 5, 34, 0);
    scale_path(this.zoom_group, 25.5, 32, 5, 34, -90);
    this.zoom_group.attr('transform', 'translate(' + 55 + ',' + 2 + ')');

    this.zoom_x_minus_button.on('click', function() {
      if (d3v7.select(this).classed('disabled')) return;
      const zoom_diff = sv.scale.x.diff() / 2;
      const new_domains = [[sv.scale.x.min() - zoom_diff, sv.scale.x.max() + zoom_diff], sv.scale.y.domain()];
      sv.move_to(new_domains)
    })

    this.zoom_x_plus_button.on('click', function() {
      if (d3v7.select(this).classed('disabled')) return;
      const zoom_diff = sv.scale.x.diff() / 4;
      const new_domains =  [ [sv.scale.x.min() + zoom_diff, sv.scale.x.max() - zoom_diff], sv.scale.y.domain() ];
      sv.move_to(new_domains)
    })

    this.zoom_y_minus_button.on('click', function() {
      if (d3v7.select(this).classed('disabled')) return;
      const zoom_diff = sv.scale.y.diff() / 2;
      const new_domains =  [ sv.scale.x.domain(), [sv.scale.y.min() - zoom_diff, sv.scale.y.max() + zoom_diff] ];
      sv.move_to(new_domains)
    })

    this.zoom_y_plus_button.on('click', function() {
      if (d3v7.select(this).classed('disabled')) return;
      const zoom_diff = sv.scale.y.diff() / 4;
      const new_domains =  [ sv.scale.x.domain(), [sv.scale.y.min() + zoom_diff, sv.scale.y.max() - zoom_diff] ];
      sv.move_to(new_domains)
    })

    // Set button disabled status
    sv.on('domain-change.menu', function() {
      if (sv.zoom_x == 1) {
        self.zoom_x_minus_button.classed('disabled', true);
      } else if (sv.zoom_x >= sv.zoom_max) {
        self.zoom_x_plus_button.classed('disabled', true);
      } else {
        self.zoom_x_minus_button.classed('disabled', false);
        self.zoom_x_plus_button.classed('disabled', false);
      }
      if (sv.zoom_y == 1) {
        self.zoom_y_minus_button.classed('disabled', true);
      } else if (sv.zoom_y >= sv.zoom_max) {
        self.zoom_y_plus_button.classed('disabled', true);
      } else {
        self.zoom_y_minus_button.classed('disabled', false);
        self.zoom_y_plus_button.classed('disabled', false);
      }
      if(sv.constructor.name === "SpectraViewer") {
        if (sv.scale.x.min() == sv.boundary.x.min()) {
          self.scroll_right_button.classed('disabled', true);
        } else {
          self.scroll_right_button.classed('disabled', false);
        }

        if (sv.scale.x.max() == sv.boundary.x.max()) {
          self.scroll_left_button.classed('disabled', true);
        } else {
          self.scroll_left_button.classed('disabled', false);
        }
      } else if (sv.zoom_y === 1 && sv.zoom_x === 1){
        self.reset_zoom_button.classed('disabled', true);
      } else {
        self.reset_zoom_button.classed('disabled', false);
      }
    });


    // Help Button
    const help_icon = this.menu_svg.append('text')
        .attr('x', 15)
        .attr('y', 24)
        .attr('font-family', 'sans-serif')
        .attr('font-size', '26px')
        .attr('stroke-width', 1)
        .attr('fill', 'black')
        .attr('class', 'jsv-button-text')
        .style('text-anchor', 'middle' )
        .text('?');
    this.help_button = button(this.menu_svg, 260, 4, 30, 30, help_icon);

    this.help_button.on('click', function() {
      sv.help.dialog.open();
    })

    // Save/Download Button
    const download_group = download_path(this.menu_svg)
        .attr('transform', 'translate(5,7)');

    this.download_button = button(this.menu_svg, 220, 4, 30, 30, download_group);
    this.download_dialog = new JSV.SVDialog(sv, {
      header_text: 'Save Image',
      content_text: download_html(sv),
      buttons: {
        'Cancel': function() { this.close(); },
        'Generate': function() { download_image(sv, this); }
      }, width: 400,
      height: 250
    });

    this.download_button.on('click', function() {
      self.download_dialog.open();
    })

    // Settings Button
    const settings_group = settings_path(this.menu_svg)
        .attr('transform', 'translate(5,5)');

    this.settings_button = button(this.menu_svg, 180, 4, 30, 30, settings_group);

    this.settings_button.on('click', function() {
      sv.settings.open();
    })

    // JSV Button
    // TODO: add link to JSV website when available
    const jsv_icon = this.menu_svg.append('text')
        .attr('x', 149)
        .attr('y', 32)
        .attr('font-family', 'sans-serif')
        .attr('font-size', '16px')
        .attr('stroke-width', 1)
        .attr('fill', 'grey')
        .attr('class', 'jsv-button-text')
        .style('text-anchor', 'middle' )
        .text('JSV');

  }


  let mag_path = function(svg) {
    let group = svg.append('g');
    group.append('circle')
        .attr('cx', 13)
        .attr('cy', 12)
        .attr('r', 9)
        .style('fill', 'grey');
    group.append('circle')
        .attr('cx', 13)
        .attr('cy', 12)
        .attr('r', 7)
        .style('fill', 'white');
    group.append('line')
        .attr('x1', 17)
        .attr('y1',17)
        .attr('x2', 25)
        .attr('y2', 25)
        .attr('stroke-width', 3)
        .attr('stroke', 'black');
    group.append('line')
        .attr('x1', 9)
        .attr('y1',12)
        .attr('x2', 17)
        .attr('y2', 12)
        .attr('stroke-width', 3)
        .attr('stroke', 'black');
    return group;
  }


  JSV.SVMenu = SVMenu;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Help
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  function SVHelp(sv) {
    this.sv = sv;
    const help_text = (sv.toString() === "SpectraViewer") ? help_text_1d : help_text_2d

    this.dialog = new JSV.SVDialog(sv, {
      header_text: 'JSpectraViewer (JSV) Help',
      content_text: help_text,
      width: 700,
      height: 350
    });

  }

  let help_text_1d = '' +
      'The view of the spectra can be scrolled around or scaled using the controls in the menu or by using the various mouse and keyboard shortcuts:' +
      '<h3>Viewer Controls</h3>' +
      '<table class="jsv-table">' +
      '<thead><tr><th>Action</th><th>Command</th></tr></thead><tbody>' +
      '<tr><td>Zoom In/Out X Axis</td><td>Scroll wheel</td></tr>' +
      '<tr><td>Zoom In/Out Y Axis</td><td>Option/Alt Key + Scroll wheel</td></tr>' +
      '<tr><td>Zoom In on Area</td><td>Option/Alt Key + Click and Drag around area</td></tr>' +
      '<tr><td>Zoom Out Completely</td><td>Option/Alt Key + Click once anywhere on viewer </td></tr>' +
      '<tr><td>Move Around</td><td>Click and Drag</td></tr></tbody></table>' +
      '<h3>Zoombox Controls (box in upper left corner)</h3>' +
      '<table class="jsv-table">' +
      '<thead><tr><th>Action</th><th>Command</th></tr></thead><tbody>' +
      '<tr><td>Move Around</td><td>Click and Drag grey selection box</td></tr>' +
      '<tr><td>Zoom In on Area</td><td>Click on unselected region and drag around new selection</td></tr>' +
      '<tr><td>Zoom Out Completely</td><td>Click once anywhere in unselected region</td></tr>' +
      '<tr><td>Alter Zoomed Area</td><td>Click and Drag on sides of grey selection box</td></tr></tbody></table>' +
      '<h3>Troubleshooting</h3>' +
      '<p>If the viewer is not showing any spectra or is slow, try updating to the latest version of your ' +
      'browser. We have found that <a href="https://www.google.com/chrome" target="_blank">Google Chrome</a> is the fastest.</p></div></div>';

  let help_text_2d = '' +
      'The view of the spectra can be scaled using the controls in the menu or by using the various mouse and keyboard shortcuts:' +
      '<h3>Viewer Controls</h3>' +
      '<table class="jsv-table">' +
      '<thead><tr><th>Action</th><th>Command</th></tr></thead><tbody>' +
      '<tr><td>Adjust Peak Sensitivity</td><td>Scroll wheel</td></tr>' +
      '<tr><td>Zoom In on Area</td><td>Click and Drag around area</td></tr>' +
      '<tr><td>Zoom Out Completely</td><td>Double click inside viewer</td></tr>' +
      '<h3>Troubleshooting</h3>' +
      '<p>If the viewer is not showing any spectra or is slow, try updating to the latest version of your ' +
      'browser. We have found that <a href="https://www.google.com/chrome" target="_blank">Google Chrome</a> is the fastest.</p></div></div>';

  JSV.SVHelp = SVHelp;

})(JSpectraViewer);
//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Dialog
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  function SVDialog(sv, options) {
    options = options || {};
    const self = this;
    this.wrapper = sv.sv_wrapper.node();

    this.fade_time = JSV.default_for(options.fade_time, 500);
    this.header_text = JSV.default_for(options.header_text, '');
    this.content_text = JSV.default_for(options.content_text, '');
    this.height = JSV.default_for(options.height, 300);
    this.width = JSV.default_for(options.width, 300);
    this.buttons = options.buttons

    this.box = d3v7.select(this.wrapper).append('div')
        .style('display', 'none')
        .attr('class', 'jsv-dialog');

    this.header = this.box.append('div')
        .attr('class', 'jsv-dialog-header')
        .html(this.header_text);

    this.dismiss = this.box.append('div')
        .attr('class', 'jsv-dialog-dismiss')
        .html('X')
        .on('click', function() { self.close(); });

    this.contents = this.box.append('div')
        .attr('class', 'jsv-dialog-contents jsv-scroll');

    if (this.buttons) {
      this.footer = this.box.append('div')
          .attr('class', 'jsv-dialog-footer');
      this.generate_buttons();
    }

    this.contents.html(this.content_text);

    this.adjust_size();

    return self;
  }

  SVDialog.prototype.visible = function() {
    return (this.box.style('display') != 'none');
  }


  SVDialog.prototype.open = function() {
    this.adjust_size();
    this.box.style('display', 'block');
    this.box.transition().duration(this.fade_time)
        .style('opacity', 1);
    return this;
  }

  SVDialog.prototype.close = function() {
    this.box.transition().duration(this.fade_time)
        .style('opacity', 0)
        .on('end', function() {
          d3v7.select(this).style('display', 'none');
        });
    return this;
  }

  SVDialog.prototype.generate_buttons = function() {
    const self = this;
    const labels = Object.keys(this.buttons);
    labels.forEach(function(label) {
      self.footer.append('button')
          .html(label)
          .attr('class', 'jsv-button')
          .on('click', function() { self.buttons[label].call(self) });
    });

  }

  SVDialog.prototype.adjust_size = function() {
    // Minimum buffer between dialog and edges of container (times 2)
    const buffer = 50;
    const wrapper_width = this.wrapper.offsetWidth;
    const wrapper_height = this.wrapper.offsetHeight;
    let width = this.width;
    let height = this.height;

    if (this.height > wrapper_height - buffer) height = wrapper_height - buffer;
    if (this.width > wrapper_width - buffer) width = wrapper_width - buffer;

    const header_height = 40;
    const footer_height = this.buttons ? 35 : 0;
    const content_height = height - header_height - footer_height;

    this.box
        .style('width', width + 'px')
        .style('height', height + 'px')

    this.contents
        .style('height', content_height + 'px');
  }

  JSV.SVDialog = SVDialog;

})(JSpectraViewer);


//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Settings
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  function SVSettings(sv, options) {
    options = options || {};
    const self = this;
    this.sv = sv;
    this.object = JSV.default_for(options.object, sv);
    this.title = JSV.default_for(options.title, 'Settings');
    this.height = JSV.default_for(options.height, 400);
    this.width = JSV.default_for(options.width, 400);
    options.settings = options.settings || [];
    this.settings = [];
    options.settings.forEach(function(setting_options) {
      self.settings.push(new Setting(self.object, setting_options));
    });

    this.dialog = new JSV.SVDialog(sv, {
      header_text: this.title,
      content_text: this.settings_html(),
      buttons: {
        'Done': function() { this.close(); }
      },
      width: this.width,
      height: this.height
    });

    return this;
  }

  // The html and listeners are regerated every time the dialog is opened
  // to catch any changes that may have been programatically
  SVSettings.prototype.open = function() {
    const self = this;
    this.dialog.contents.html(this.settings_html());
    d3v7.selectAll('.jsv-setting')
        .on('change', function() {
          const input = d3v7.select(this);
          const setting = self.find(input.attr('id'));
          const value = input.property('checked');
          setting.value(value);
          self.sv.full_draw();
        });

    this.dialog.open();
    return this;
  }

  SVSettings.prototype.close = function() {
    this.dialog.close();
    return this;
  }

  SVSettings.prototype.find = function(id) {
    return this.settings.filter(function(s) { return s.id() == id; })[0];
  }

  SVSettings.prototype.settings_html = function() {
    html = '<div class="jsv-settings">';
    this.settings.forEach(function(setting) {
      html += '<div><label class="jsv-label">' + setting.label + '</label>';
      html += '<div class="jsv-input-group">';
      if (setting.type == 'boolean') {
        html += checkbox(setting);
      }
      html += '</div></div>';
    });
    html += '</div>';

    return html;
  }

  const checkbox = function(setting) {
    html = '';
    html += '<input type="checkbox" class="jsv-input jsv-setting" id="' + setting.id() + '"';
    if (setting.value()) {
      html += ' checked="true" ';
    }
    html +=  ' />';
    return html;
  }

  let setting_id = 0;
  function Setting(object, options) {
    options = options || {};
    this.object = object;
    this.label = options.label;
    this.property = options.property;
    this.type = JSV.default_for(options.type, 'boolean');
    this.id();
  }

  Setting.prototype.id = function() {
    const new_id = generate_setting_id();
    this.id = function() { return new_id; }
    return new_id;
  }

  const generate_setting_id = function() {
    return 'jsv-setting-id-' + setting_id++;
  }

  Setting.prototype.value = function(value) {
    if (arguments.length == 0) {
      if (typeof this.property === 'function') {
        value = this.property.call(this.object);
      } else {
        value = this.object[this.property];
      }
    } else {
      if (typeof this.property === 'function') {
        this.property.call(this.object, value);
      } else {
        this.object[this.property] = value;
      }
      self.sv.full_draw();
    }
    return value;
  }

  JSV.SVSettings = SVSettings;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// Zoombox
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  // ZOOMBOX
  function ZoomBox(sv) {
    JSV.pixel(10);
    this.sv = sv;
    // Set up zoom box size
    let percentage = sv.zoombox_size || 10
    this.width = sv.width * percentage / 100;
    this.height = sv.height * percentage / 100;
    this.adjusting = false;

    // Create SVG
    this.svg = sv.sv_wrapper.append('svg')
        .attr('width', this.width + 1)
        .attr('height', this.height + 1)
        .style('position', 'absolute')
        .style('top', 0)
        .style('left', sv.width - this.width - 1);

    if (sv.axis_x_reverse) {
      this.svg.style('left', 0);
    }

    // Draw Border
    this.border = this.svg.append('rect')
        .attr('x', 0)
        .attr('y', 0)
        .attr('width', this.width)
        .attr('height', this.height)
        .attr('fill', 'white')
        .attr('stroke', '#BBB');

    // Set initial scales
    this.scale = new JSV.SVScale();
    this.scale.y.range([this.height, 0]);
    let x_range = this.sv.axis_x_reverse ? [this.width, 0] : [0, this.width];
    this.scale.x.range(x_range);


    // Brush for selecting a zoom area
    this.initialize_brushing();

    this.spectra_function = d3v7.line()
        .x((d) => this.scale.x(d.x))
        .y((d) => this.scale.y(d.y));
  }

  Object.defineProperty(ZoomBox.prototype, 'visible', {
    get: function() { return this._visible; },
    set: function(val) {
      this._visible = val;
      if (this._visible) {
        this.svg.style('display', 'block');
      } else {
        this.svg.style('display', 'none');
      }
      this.sv.legend.update();
      this.sv.draw();
    }
  });

  // ZOOM BOX BRUSHING
  ZoomBox.prototype.initialize_brushing = function() {
    const zb = this;
    const sv = zb.sv;
    zb.brush = d3v7.brush()
        .extent([[zb.scale.x.range()[1], zb.scale.y.range()[1]], [zb.scale.x.range()[0], zb.scale.y.range()[0]]])
        .on('start', brushstart)
        .on('brush', brushing)
        .on('end',   brushend);

    zb.select_brush = zb.svg.append("g")
        .attr("class", "active-brush")
        .call(zb.brush);

    function brushstart(event) {
      if(event.sourceEvent)
        event.sourceEvent.preventDefault(); // Prevent text cursor
    }

    function brushing(event) {
      if (!(event.selection === null) && !zb.adjusting) {
        let extent;
          if (sv.axis_y_lock !== false) {
            const ex = event.selection;
            const x1 = ex[0][0];
            const x2 = ex[1][0];
            // Set y1 to the min y domain
            const y1 = zb.scale.y.domain()[0];
            const mouse_y = zb.scale.y.invert(d3v7.pointer(event, zb.svg.node())[1]);
            const max_y = zb.scale.y.domain()[1];
            let y2 = Math.min(mouse_y, max_y);
            y2 = Math.max(y1, y2);
            extent = [[zb.scale.x.invert(x2), 0], [zb.scale.x.invert(x1), y2]];
          }
          if (!extent) extent = [[zb.scale.x.invert(event.selection[1][0]), zb.scale.y.invert(event.selection[1][1])],
            [zb.scale.x.invert(event.selection[0][0]), zb.scale.y.invert(event.selection[0][1])]];
          sv.set_domain_to_brush_extent(extent, false);
          sv.fast_draw();
        }

        // DEBUG INFO
        if (sv.debug) {
          sv.debug_data.zbox['X1'] = JSV.round(zb.brush.extent()[0][0]);
          sv.debug_data.zbox['Y1'] = JSV.round(zb.brush.extent()[0][1]);
          sv.debug_data.zbox['X2'] = JSV.round(zb.brush.extent()[1][0]);
          sv.debug_data.zbox['Y2'] = JSV.round(zb.brush.extent()[1][1]);
        }
    }

    function brushend(event) {
      if(!zb.adjusting) {
        // Force setting next zoom to update zoom level
        sv.zoom_axis = '';
        sv.draw();
      }
    }
  }

  ZoomBox.prototype.add_spectrum = function(spectrum) {
    const boundary = this.sv.boundary;
    this.scale.x.domain(boundary.x.domain());
    this.scale.y.domain(boundary.y.domain());

    this.svg.append('path')
        .attr('id', spectrum.id)
        .attr('d', this.spectra_function(spectrum.simple_xy_data.asArray()))
        .attr('stroke', spectrum.color)
        .attr('stroke-width', 0.5)
        .attr('fill', 'none');

    // Redraw brush
    this.set_zoom_area(boundary);
  }

  ZoomBox.prototype.remove_spectra = function(id) {
    this.sv.container.select('path#' + id).remove();
  }

  ZoomBox.prototype.update = function() {
    const self = this;
    const sv = this.sv;
    if (sv.axis_x_reverse) {
      this.svg.style('left', 0);
    } else {
      this.svg.style('left', sv.width - this.width - 1);
    }
    this.sv.all_spectra().forEach(function(spectrum) {
      self.remove_spectra(spectrum.id);
      if (spectrum.active) {
        self.add_spectrum(spectrum);
      }
    });
  }

  ZoomBox.prototype.set_zoom_area = function(scale) {
    let zb = this;
    const x1 = zb.scale.x(scale.x.domain()[1]);
    const x2 = zb.scale.x(scale.x.domain()[0]);
    const y1 = zb.scale.y(scale.y.domain()[1]);
    const y2 = zb.scale.y(scale.y.domain()[0]);
    // Redraw brush
    zb.adjusting = true;
    zb.select_brush.call(zb.brush.move, [[x1, y1], [x2, y2]]);
    zb.adjusting = false;
  }

  JSV.ZoomBox = ZoomBox;

})(JSpectraViewer);


//////////////////////////////////////////////////////////////////////////////
// Utils
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

  /**
   * Return the _default_value_ if _value_ is undefined
   * @param {Object} value         Returned if it is defined
   * @param {Object} default_value Returned if _value_ is undefined
   * @return {Object}
   */
  JSV.default_for = function(value, default_value) {
    return (value === undefined) ? default_value : value;
  }

  /**
   * Return the pixel ratio. The default is 1.
   */
  JSV.pixel_ratio = 1;

  /**
   * Converts provided number of pixels based on pixel ratio which depends on
   * the screen resolution. Typical displays will have a pixel ration of 1,
   * while retina displays will have a pixel ration of 2.
   *
   * **Important**: Whenever drawing on the canvas, convert the pixels first
   * using this method.
   *
   * @param {Integer} value Number of pixels
   * @return {Intger}
   */
  JSV.pixel = function(px) {
    return px * JSV.pixel_ratio;
  }

  JSV.get_pixel_ratio = function(canvas) {
    const context = canvas.getContext('2d');
    //  query the various pixel ratios
    const devicePixelRatio = window.devicePixelRatio || 1;

    const backingStoreRatio = context.webkitBackingStorePixelRatio ||
        context.mozBackingStorePixelRatio ||
        context.msBackingStorePixelRatio ||
        context.oBackingStorePixelRatio ||
        context.backingStorePixelRatio || 1;

    return devicePixelRatio / backingStoreRatio;
  }

  JSV.scale_resolution = function(canvas, ratio){
    // get the canvas and context
    const context = canvas.getContext('2d');

    // upscale the canvas if the two ratios don't match
    if (ratio != 1) {

      const oldWidth = canvas.width;
      const oldHeight = canvas.height;

      canvas.width  = oldWidth  * ratio;
      canvas.height = oldHeight * ratio;

      canvas.style.width  = oldWidth  + 'px';
      canvas.style.height = oldHeight + 'px';
    }
  }

  // Function to create subclasses
  Function.prototype.inherits = function(parent) {
    this.prototype = Object.create(parent.prototype);
  };

  /**
   * Merges top level properties of each supplied object.
   * ```javascript
   * JSV.merge({a:1, b:1}, {b:2, c:2}, {c:3, d:3});
   * //=> {a: 1, b: 2, c: 3, d: 3}
   * ```
   * If a non object is provided, it is ignored. This can be useful if
   * merging function arguments that may be undefined.
   * @param {Object} object_1,object_2,..,object_n Objects to merge
   * @return {Object}
   */
  JSV.merge = function() {
    const data = {};
    let object, keys, key;
    let arg_i = 0;
    const arg_len = arguments.length;
    for (; arg_i < arg_len; arg_i++) {
      object = arguments[arg_i];
      if (typeof object === 'object') {
        keys = Object.keys(object);
        let key_i = 0;
        const key_len = keys.length;
        for (; key_i < key_len; key_i++){
          key = keys[key_i];
          data[key] = object[key];
        }
      }
    }
    return data;
  }

  /**
   * Returns a string id using the _id_base_ and _start_ while
   * making sure the id is not in _current_ids_.
   * ```javascript
   * JSV.unique_id('spectra_', 1, ['spectra_1', 'spectra_2']);
   * //=> 'spectra_3'
   * ```
   * @param {String} id_base Base of ids
   * @param {Integer} start Integer to start trying to creat ids with
   * @param {Array} current_ids Array of current ids
   * @return {String}
   */
  JSV.unique_id = function(id_base, start, current_ids) {
    let id;
    do {
      id = id_base + start;
      start++;
    } while (current_ids.indexOf(id) > -1);
    return id;
  }


  /**
   * Rounds the number use d3v7.round. The only reason to use this method
   * is that it sets the default number of decimal places to 2.
   * @param {Number} value Number to round
   * @param {Integer} places Number of decimal places to round [Default: 2]
   * @return {Number}
   */
  JSV.round = function(value, places=2) {
    return d3v7.round(value, places);
  }

  /**
   * Returns the number of milliseconds elapsed since the supplied time.
   * The returned time will have 'ms' appended to it.
   * @param {Integer} old_time Old time in milliseconds
   * @return {Integer}
   */
  JSV.elapsed_time = function(old_time) {
    const elapsed = (new Date().getTime()) - old_time;
    return elapsed + ' ms';
  }

  /**
   * Binary search to find the index of data where data[index] equals _search_value_.
   * If no element equals value, the returned index will be the upper or lower [default]
   * index that surrounds the value.
   *
   * @param {Array} data Array of numbers. Must be sorted from lowest to highest.
   * @param {Number} search_value The value to search for.
   * @param {Boolean} upper Only used if no element equals the _search_value_
   *
   *    - _true_: return index to right of value
   *    - _false_: return index to left of value [default]
   *
   * @return {Number}
   */
  JSV.index_of_value = function(data, search_value, upper) {
    let min_index = 0;
    let max_index = data.length - 1;
    let current_index, current_value;
    if (data[min_index] >= search_value) return min_index;
    if (data[max_index] <= search_value) return max_index;

    while (max_index - min_index > 1) {
      current_index = (min_index + max_index) / 2 | 0;
      current_value = data[current_index];
      if (current_value < search_value) {
        min_index = current_index;
      } else if (current_value > search_value){
        max_index = current_index;
      } else {
        return current_index;
      }
    }
    return (upper ? max_index : min_index);
  }

  /**
   * Return the y value for the _x_value_ of a lorentzian peak with the specified
   * _center_, _width_, and _amplitude_.
   *
   * @param {Number} x_value The x value used to determine the y value
   * @param {Number} center Center of the lorentzian
   * @param {Number} width Width of the lorentzian at half height
   * @param {Number} amplitude Amplitude of the lorentzian
   * @return {Number}
   */
  JSV.lorentzian = function(x_value, center, width, amplitude) {
    const w2 = width * width;
    const shift = x_value - center;
    return amplitude * w2 / (w2 + (4 * shift * shift));
  }

  /**
   * Calculates the cumulative Y value from the peaks at _x_value_.
   * Each peak must have the following properties:
   *   - _center_
   *   - _width_
   *   - _amplitude_
   *
   * @param {Number} x_value The x value used to determine the y value
   * @param {Array} peaks An Array or SVSet of Peaks.
   * @return {Number}
   */
  JSV.sum_of_peaks = function(x_value, peaks) {
    let sum = 0;
    let peak;
    for (let i = 0; i < peaks.length; i++) {
      peak = peaks[i];
      sum += JSV.lorentzian(x_value, peak.center, peak.width, peak.amplitude);
    }
    return sum;
  };

  /**
   * Generate an array of x/y points for the provided _peaks_, making sure to
   * include the points at the peak amplitude for the peak within the limits.
   *
   * The returned xy_data will have the following structure:
   * ```javascript
   * xy_data = { x: [1,2,3], y: [4,5,6] }
   * ```
   *
   * @param {SVSet} peaks A SVSet of peaks
   * @param {Number} number_of_points Number of points to create from the _lowerlim_ to the _upperlim_
   * @param {Number} lowerlim Lowest x value
   * @param {Number} lowerlim Highest x value
   * @param {Boolean} fast If true, only use peaks within the limits or close to the outside limits. This speeds up the calculation by ignoring peaks that are far away and thus contribute little to the plot within the limits.
   * @return {Object}
   */
  JSV.xy_from_peaks = function(peaks, number_of_points, lowerlim, upperlim, fast) {
    if (fast) peaks = JSV.peaks_in_range(peaks, lowerlim, upperlim, 6);
    const peaks_in_view = JSV.peaks_in_range(peaks, lowerlim, upperlim);
    const centers = peaks_in_view.order_by('center').map(function (p) {
      return p.center;
    });
    let xpos = lowerlim;
    const xmax = upperlim;
    const x = [], y = [];
    const delta = ((upperlim - lowerlim) / number_of_points);
    while (xpos < xmax) {
      x.push(xpos);
      y.push(JSV.sum_of_peaks(xpos, peaks));
      if (centers[0] && (centers[0] <= xpos + delta) ) {
        xpos = centers.shift();
      } else {
        xpos += delta;
      }
    }
    // Add last point
    x.push(upperlim);
    y.push(JSV.sum_of_peaks(upperlim, peaks));
    return {x:x, y:y};
  }

  /**
   * Return a SVSet of Peaks that are within the limits or close to the limits.
   *
   * @param {SVSet} peaks A SVSet of Peaks
   * @param {Number} lowerlim The lower limit to look for peaks.
   * @param {Number} upperlim The upper limit to look for peaks.
   * @param {range} range Used to find peaks that are close to but outside of the limits. For example, a range of 2 will include peaks that have center value withing 2 peak widths of the limit.
   * @return {SVSet}
   */
  JSV.peaks_in_range = function(peaks, lowerlim, upperlim, range) {
    range = range || 0;
    const new_peaks = new JSV.SVSet();
    peaks.forEach(function(peak) {
      if ( (JSV.peak_min([peak], range) < upperlim) && (JSV.peak_max([peak], range) > lowerlim ) ) {
        new_peaks.push(peak);
      }
    });
    // console.log(peaks.length + ' -> ' + new_peaks.length)
    return new_peaks
  }


  // TODO: this should be merged or extracted from get_peak_domains()
  /**
   * Returns the minimum x value for the supplied set of peaks.
   * First finds the minimum peak by center, then subtracts the
   * number of widths from the center.
   *
   * @param {SVSet} peaks SVSet of Peaks
   * @param {Number} widths The number of widths to subtract from the minimum peak center
   * @return {Number}
   */
  JSV.peak_min = function(peaks, widths) {
    widths = widths || 3;
    let min, best_peak;
    if (peaks.length > 0) {
      best_peak = peaks[0];
    }
    peaks.forEach(function(peak) {
      if (peak.center < best_peak.center) {
        best_peak = peak;
      }
    })
    if (best_peak) {
      min = best_peak.center - (widths * best_peak.width);
    }
    return min;
  }
  /**
   * Returns the maximum x value for the supplied set of peaks.
   * First finds the maximum peak by center, then adds the
   * number of widths to the center.
   *
   * @param {SVSet} peaks SVSet of Peaks
   * @param {Number} widths The number of widths to add from the maximum peak center
   * @return {Number}
   */
  JSV.peak_max = function(peaks, widths) {
    widths = widths || 3;
    let max, best_peak;
    if (peaks.length > 0) {
      best_peak = peaks[0];
    }
    peaks.forEach(function(peak) {
      if (peak.center > best_peak.center) {
        best_peak = peak;
      }
    })
    if (best_peak) {
      max = best_peak.center + (widths * best_peak.width);
    }
    return max;
  }

  /**
   * Returns a number unless _n_ is undefined in which case _undefined_ is returned.
   * @param {Object} n The object to convert to a number
   * @return {Number}
   */
  JSV.number = function(n) {
    if (n === undefined) return;
    return Number(n);
  }

  /**
   * Convience function to determine if an object is a number.
   * @param {Object} n The object to check
   * @return {Boolean}
   */
  JSV.isNumeric = function (n) {
    return isFinite(n) && parseFloat(n) == n;
  }

  /**
   * Return the number of decimal places found in _num_.
   *
   * @param {Number} num The number to check
   * @return {Number}
   */
  JSV.decimalPlaces = function(num) {
    const match = ('' + num).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);
    if (!match) { return 0; }
    return Math.max(
        0,
        // Number of digits right of decimal point.
        (match[1] ? match[1].length : 0)
        // Adjust for scientific notation.
        - (match[2] ? +match[2] : 0));
  }

  // COLORS
  // http://krazydad.com/tutorials/makecolors.php
  JSV.colors = function(len, center, width, alpha, freq1, freq2, freq3,
                        phase1, phase2, phase3) {
    const colors = [];
    if (len == undefined)      len    = 50;
    if (center == undefined)   center = 200;
    if (width == undefined)    width  = 30;
    if (alpha == undefined)    alpha  = 1;
    if (freq1 == undefined)    freq1  = 2.4;
    if (freq2 == undefined)    freq2  = 2.4;
    if (freq3 == undefined)    freq3  = 2.4;
    if (phase1 == undefined)   phase1 = 0;
    if (phase2 == undefined)   phase2 = 2;
    if (phase3 == undefined)   phase3 = 4;

    for (let i = 0; i < len; ++i) {
      const red = Math.round(Math.sin(freq1 * i + phase1) * width + center);
      const green = Math.round(Math.sin(freq2 * i + phase2) * width + center);
      const blue = Math.round(Math.sin(freq3 * i + phase3) * width + center);
      colors.push('rgba(' + red + ',' + green + ',' + blue + ',' + alpha + ')');
    }
    return colors;
  }

  JSV.test_colors = function(colors) {
    colors.forEach(function(color) {
      document.write( '<font style="color:' + color + '">&#9608;</font>')
    })
    document.write( '<br/>')
  }

  // The xy data is the content of the spectrumDataArray element, it is an
  // array of 64 bit floats. The array is of the form:
  // [x_1,y_1,x_2,y_2,...,x_n,y_n]  the bytes are encoded as base64. They can
  // also be compressed with zlib.
  // Here is a javascript function to decode the xy data, that returns it as an
  // array with xy points, add true as a second option if the data is
  // compressed:
  JSV.convert_base64_to_float64 = function(encoded_data, zipped) {
    // function convert_base64_to_float64(encoded_data,zipped) {

    if(typeof zipped === 'undefined') { zipped = false; }

    const byte_string = atob(encoded_data);
    let int_array = new Int8Array(byte_string.length);
    if(zipped) {
      int_array = new Zlib.Deflate(int_array);
    }

    for (let i = 0; i < byte_string.length; i++){
      int_array[i] = byte_string.charCodeAt(i);
    }
    const values = new Float64Array(int_array.buffer);
    // new DataView(int_array.buffer).getFloat64(0, true) // For little endian

    return values;
  }


  /**
   * Convert JSON results from Bayesil to the proper format for JSV
   * @param {Object} bayesil_data Bayesil results as JSON
   * @return {Object}
   */
  JSV.convert_bayesil_data = function(bayesil_data) {
    const spectrum = {
      id: JSV.default_for(bayesil_data.id, 'Fit'),
      name: JSV.default_for(bayesil_data.name, 'Fit'),
      compounds: [],
      display: JSV.merge({color: 'blue'}, bayesil_data.display),
      meta: {dss_concentration: bayesil_data.dss_conc}
    };
    bayesil_data.metabolites.forEach(function(compound_data) {
      const compound = {
        concentration: compound_data.concentration,
        original_concentration: compound_data.concentration,
        concentration_units: '\u03BCM',
        id: compound_data.id,
        name: compound_data.name,
        clusters: [],
        display: {visible: false},
        meta: {
          dss_ratio: JSV.default_for(JSV.number(compound_data.dss_ratio), 1),
          threshold: compound_data.threshold,
          score: compound_data.score,
        }
      };

      compound.meta.amplitude_coefficient = compound.concentration / compound.meta.dss_ratio / spectrum.meta.dss_concentration;

      compound_data.clusters.forEach(function(cluster_data) {
        const cluster = {
          peaks: [],
          meta: {shift: cluster_data.shift}
        };
        cluster_data.shift = JSV.default_for(cluster_data.shift, 0);
        // cluster.center = cluster_data.center;
        cluster.upper_bound = cluster_data.upper_bound;
        cluster.lower_bound = cluster_data.lower_bound;
        cluster.meta.lower_bound_diff = cluster_data.lower_bound_diff;
        cluster.meta.upper_bound_diff = cluster_data.upper_bound_diff;
        cluster_data.peaks.forEach(function(peak_data) {

          const peak = {
            center: Number(peak_data.center) + Number(cluster_data.shift),
            width: Number(peak_data.width),
            original_amplitude: Number(peak_data.amplitude),
            amplitude: Number(peak_data.amplitude) * Number(compound.meta.amplitude_coefficient),
            meta: {initial_width: peak_data.initial_width}
          };
          cluster.peaks.push(peak);
        });
        compound.clusters.push(cluster);
      });
      spectrum.compounds.push(compound);
    });

    return spectrum;
  }

  /**
   * Convert spectra data to Bayesil JSON format
   * @param {Object} xy_spectrum The fid spectrum to extract xy data from
   * @param {Object} fit_spectrum The fit  spectrum to extract compound data from
   * @return {Object}
   */
  JSV.create_bayesil_json = function(xy_spectrum, fit_spectrum) {

    xy_spectrum = xy_spectrum || { xy_data: { x: [], y: [] } };
    const bayesil = {
      dss_conc: fit_spectrum.meta.dss_concentration,
      metabolites: [],
      meta: xy_spectrum.meta
    };

    fit_spectrum.compounds().forEach(function(compound) {

      const compound_data = {
        name: compound.name,
        id: compound.id,
        concentration: compound.concentration,
        dss_ratio: compound.meta.dss_ratio,
        score: compound.meta.score,
        threshold: compound.meta.threshold,
        clusters: []
      };
      // Update amplitude_coefficient in case concentration has changed
      // amplitude_coefficient is only used for converted to and from the Bayesil JSON format
      compound.meta.amplitude_coefficient = compound.concentration / compound.meta.dss_ratio / fit_spectrum.meta.dss_concentration;
      compound.clusters().forEach(function(cluster) {
        const cluster_data = {
          center: cluster.center(),
          initial_center: cluster.meta.initial_center,
          shift: cluster.center(), // TEMP
          lower_bound: cluster.lower_bound,
          upper_bound: cluster.upper_bound,
          peaks: []
        };
        cluster.peaks().forEach(function(peak) {
          let amplitude;
// If the concentration has been changed from 0, display the compound peaks
          if (compound.original_concentration == 0){
            amplitude = peak.original_amplitude;

          }else{
            amplitude = peak.amplitude / compound.meta.amplitude_coefficient;
          }
          const peak_data = {
            amplitude: amplitude,
            center: peak.center - cluster.center(),
            width: peak.width,
            initial_width: peak.meta.initial_width
          };

          if (isNaN(peak_data.amplitude)) peak_data.amplitude = 0;
          cluster_data.peaks.push(peak_data);
        });
        compound_data.clusters.push(cluster_data);
      });
      bayesil.metabolites.push(compound_data);
    });
    bayesil.spectrum_xy = {x: xy_spectrum.xy_data.x, y: xy_spectrum.xy_data.y};

    return bayesil;
  }

  /////////////////////////////////////////////////////////////////////////////
  // POLYFILLS
  /////////////////////////////////////////////////////////////////////////////
  if (!Number.isInteger) {
    Number.isInteger = function isInteger (nVal) {
      return typeof nVal === "number" && isFinite(nVal) && nVal > -9007199254740992 && nVal < 9007199254740992 && Math.floor(nVal) === nVal;
    };
  }


})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// Simplify code
// Adapted from Simplify.js by Vladimir Agafonkin (Details are at the bottom)
//////////////////////////////////////////////////////////////////////////////
(function(JSV) {

    let _points = [];

    // square distance between 2 points
    function getSqDist(p1, p2) {

        const dx = _points.x[p1] - _points.x[p2],
            dy = _points.y[p1] - _points.y[p2];

        return dx * dx + dy * dy;
    }

    // square distance from a point to a segment
    function getSqSegDist(p, p1, p2) {

        let x = _points.x[p1],
            y = _points.y[p1],
            dx = _points.x[p2] - x,
            dy = _points.y[p2] - y;

        if (dx !== 0 || dy !== 0) {

            const t = ((_points.x[p] - x) * dx + (_points.y[p] - y) * dy) / (dx * dx + dy * dy);

            if (t > 1) {
                x = _points.x[p2];
                y = _points.y[p2];

            } else if (t > 0) {
                x += dx * t;
                y += dy * t;
            }
        }

        dx = _points.x[p] - x;
        dy = _points.y[p] - y;

        return dx * dx + dy * dy;
    }

    // basic distance-based simplification
    function simplifyRadialDist(pts, sqTolerance) {

        let prevPoint = pts[0];
        const newPoints = [prevPoint];
        let pt;

        let i = 1;
        const len = pts.length;
        for (; i < len; i++) {
            pt = pts[i];

            if (getSqDist(pt, prevPoint) > sqTolerance) {
                newPoints.push(pt);
                prevPoint = pt;
            }
        }

        if (prevPoint !== pt) newPoints.push(pt);

        return newPoints;
    }

    // simplification using optimized Douglas-Peucker algorithm with recursion elimination
    function simplifyDouglasPeucker(pts, sqTolerance) {

        const len = pts.length,
            MarkerArray = typeof Uint8Array !== 'undefined' ? Uint8Array : Array,
            markers = new MarkerArray(len);
        let first = 0,
            last = len - 1;
        const stack = [],
            newPoints = [];
        let i, maxSqDist, sqDist, index;

        markers[first] = markers[last] = 1;

        while (last) {

            maxSqDist = 0;

            for (i = first + 1; i < last; i++) {
                sqDist = getSqSegDist(pts[i], pts[first], pts[last]);

                if (sqDist > maxSqDist) {
                    index = i;
                    maxSqDist = sqDist;
                }
            }

            if (maxSqDist > sqTolerance) {
                markers[index] = 1;
                stack.push(first, index, index, last);
            }

            last = stack.pop();
            first = stack.pop();
        }

        for (i = 0; i < len; i++) {
            if (markers[i]) newPoints.push(pts[i]);
        }

        return newPoints;
    }

    /**
     * Simplify.js is a high-performance JS polyline simplification library by Vladimir Agafonkin.
     * [Orignal Project](http://mourner.github.io/simplify-js)
     *
     * It has been adapted here to work with a different point format.
     * The format used in JSV is an object of arrays:
     * ```js
     * points = { x: [1, 2, 3], y: [10, 20, 30] };
     * ```
     * There can also be an optional yi array, to hold the imaginary component of
     * the line. This is used when incorporating phasing into the viewer.
     *
     * The orginal format was an array of points:
     * ```js
     * // Do not use this format
     * points = [ {x:1, y:10}, {x:2, y:20}, {x:3, y:30} ];
     * ```
     * ***
     * Copyright (c) 2012, Vladimir Agafonkin
     * All rights reserved.
     *
     * Redistribution and use in source and binary forms, with or without modification, are
     * permitted provided that the following conditions are met:
     *
     *   1. Redistributions of source code must retain the above copyright notice, this list of
     *      conditions and the following disclaimer.
     *
     *   2. Redistributions in binary form must reproduce the above copyright notice, this list
     *      of conditions and the following disclaimer in the documentation and/or other materials
     *      provided with the distribution.
     *
     * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY
     * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
     * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
     * COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
     * EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
     * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
     * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR
     * TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
     * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.)
     *
     * @param {Object} points An object containing an x and y array of points. There may also be an yi array.
     * @param {Number} tolerance Affects the amount of simplification. [Default: 1]
     * @param {Boolean} highestQuality Excludes distance-based preprocessing step which leads to highest quality simplification but runs ~10-20 times slower. [Default: false]
     */
    function simplify(points, tolerance, highestQuality) {

        const length = points.x.length;
        if (length <= 1) return points;

        _points = points;
        let pts = new Array(length);
        for (let i=0; i < length; i++) {
            pts[i] = i;
        }

        const sqTolerance = tolerance !== undefined ? tolerance * tolerance : 1;

        pts = highestQuality ? pts : simplifyRadialDist(pts, sqTolerance);
        pts = simplifyDouglasPeucker(pts, sqTolerance);

        const x = new Array(pts.length);
        const y = new Array(pts.length);
        for (let i=0, len=pts.length; i < len; i++) {
            x[i] = _points.x[pts[i]];
            y[i] = _points.y[pts[i]];
        }
        let yi;
        if (_points.yi) {
            yi = new Array(pts.length);
            for (let i=0, len=pts.length; i < len; i++) {
                yi[i] = _points.yi[pts[i]];
            }
        }

        points = {x:x, y:y};
        if (yi) { points.yi = yi; }
        return points
    }
    /** @ignore */

    JSV.simplify = simplify;

})(JSpectraViewer);

// test_1=" \n  Marvin  02071110482D          \n\n  9  9  0  0  0  0            999 V2000\n   12.5055   -6.4299    0.0000 O   0  0  0  0  0  0  0  0  0  0  0  0\n   12.5073   -5.6033    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n   11.7891   -4.3542    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0\n   11.7891   -5.1974    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n   11.0709   -3.9483    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n   12.5073   -3.9483    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n   11.0709   -3.1053    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n   12.5073   -3.1053    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n   11.7891   -2.6993    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n  1  2  1  0  0  0  0\n  4  3  1  0  0  0  0\n  5  3  1  0  0  0  0\n  6  3  1  0  0  0  0\n  2  4  1  0  0  0  0\n  7  5  1  0  0  0  0\n  8  6  1  0  0  0  0\n  9  7  1  0  0  0  0\n  9  8  1  0  0  0  0\nM  END\n"
// test_2=" \n  Marvin  01220814402D          \n\n 13 14  0  0  0  0            999 V2000\n    0.0000   -0.8250    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n    0.7145   -0.4125    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n    0.7145    0.4125    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n    0.0000    0.8250    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -0.7145    0.4125    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n   -0.7145   -0.4125    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n    3.1130   -1.0105    0.0000 O   0  0  0  0  0  0  0  0  0  0  0  0\n    2.5610   -1.6236    0.0000 C   0  0  2  0  0  0  0  0  0  0  0  0\n    2.8159   -2.4082    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0\n    1.7540   -1.4521    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n    1.4991   -0.6674    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n    1.9840    0.0000    0.0000 C   0  0  0  0  0  0  0  0  0  0  0  0\n    1.4991    0.6674    0.0000 N   0  0  0  0  0  0  0  0  0  0  0  0\n  2  1  2  0  0  0  0\n 11  2  1  0  0  0  0\n  3  2  1  0  0  0  0\n 13  3  1  0  0  0  0\n  3  4  2  0  0  0  0\n  4  5  1  0  0  0  0\n  6  1  1  0  0  0  0\n  5  6  2  0  0  0  0\n  8  7  1  6  0  0  0\n  8  9  1  0  0  0  0\n  8 10  1  0  0  0  0\n 10 11  1  0  0  0  0\n 11 12  2  0  0  0  0\n 12 13  1  0  0  0  0\nM  END\n"

// Adapted from: https://bitbucket.org/sbeisken/specktackle/src/e2cfb3f9d6730b0292b67fc425366975ff3495ec/js/util/mol2svg.js?at=master&fileviewer=file-view-default
// TODO: Add license
// GNU LESSER GENERAL PUBLIC LICENSE: Version 3, 29 June 2007
//

(function(JSV) {

  /**
   * The SVStructure object controls the displaying of a chemical structure.
   * SVStructure requires the [X2JS library](https://github.com/abdmob/x2js) to read structures in nmrML format.
   * The following options can be set when creating a
   * [SpectraViewer](SpectraViewer.js.html):
   *
   *  Option                | Default     | Description
   *  ----------------------|-------------------------------------------------
   *  container_id          | _undefined_ | ID of dom element where the structure will be draw (e.g. '#my-structure')
   *  width                 | 200         | Width of strucutre panel
   *  height                | 200         | Height of strucutre panel
   *  data                  | _undefined_ | Structure data as a string. It can be in molfile or nmrML format.
   *  format                | mol         | Structure data format: 'mol' or 'nmrML'. The nmrML format is similar to CML.
   *  show_hydrogen         | false       | Show hydrogens in structure
   *  show_atom_numbers     | true        | Show atom numbers in structure
   *  nmr_nucleus           | _undefined_ | NMR nucleus ('13C', '1H', or _undefined_)
   *
   *
   * @param {Object} options Options for how the Structure should work. Described below.
   * @return {SVSelection}
   */
  function SVStructure(options) {
    this.container_id = options.container_id
    this.width = options.width || 200,   // width of the panel
    this.height = options.height || 200,  // height of the panel
    this.container = d3v7.select(this.container_id); //element id or the element itself to be the container for the image
    this.container.classed('js-structure', true);
    this.data = options.data;
    this.format = options.format || 'mol';
    this.x_scale;     // linear d3v7 x scale function
    this.y_scale;     // linear d3v7 y scale function
    this.avgL = 0;    // scaled average bond length (for font size scaling)

    this.show_hydrogen = JSV.default_for(options.show_hydrogen, false);
    this.show_atom_numbers = JSV.default_for(options.show_atom_numbers, false);
    this.use_jsmol = JSV.default_for(options.use_jsmol, false);
    this.nmr_nucleus = options.nmr_nucleus;
    this.interactive_1H = true;
    this._atoms = new JSV.SVSet();
    this._bonds = new JSV.SVSet();

    if (this.data) {
      this.read_data(this.data, this.format);
    }

    // Setup SVEvents
    this.handlers = new JSV.SVEvents();
    this.on = this.handlers.on
    this.off = this.handlers.off
    this.trigger = this.handlers.trigger
  }

  SVStructure.prototype.nmr_1H = function () {
    return this.nmr_nucleus && this.nmr_nucleus.toUpperCase() == '1H'
  }

  SVStructure.prototype.nmr_13C = function () {
    return this.nmr_nucleus && this.nmr_nucleus.toUpperCase() == '13C'
  }

  /**
   * Initializes the viewport and appends it to the element identified
   * by the given identifier. The linear d3v7 x- and y-scales are set
   * to translate from the viewport coordinates to the mol coordinates.
   */
  SVStructure.prototype.initSVG = function () {
    const self = this;
    const container = self.container;
    // x minimum and maximum
    const xExtrema = d3v7.extent(self.atoms(), function (atom) {
      return atom.x;
    });
    // y minimum and maximum
    const yExtrema = d3v7.extent(self.atoms(), function (atom) {
      return atom.y;
    });

    // dimensions of molecule graph
    const m = [15, 15, 20, 15];   // margins
    self.margins = m;
    let wp = self.width - m[1] - m[3];   // width
    let hp = self.height - m[0] - m[2];   // height

    // Scale the image to the contrained dimension
    const container_ratio = wp / hp;
    self.structure_ratio = ( xExtrema[1] - xExtrema[0] ) / ( yExtrema[1] - yExtrema[0] );
    const constrained_dim = (container_ratio >= self.structure_ratio) ? 'height' : 'width';
    if (constrained_dim == 'height') {
      wp = hp * self.structure_ratio;
    } else {
      hp = wp / self.structure_ratio;
    }
    self.wp = wp;
    self.hp = hp;
    // X scale will fit all values within pixels 0-w
    self.x_scale = d3v7.scaleLinear().domain([xExtrema[0], xExtrema[1]]).range([m[1], wp-m[3]]);
    // Y scale will fit all values within pixels h-0
    self.y_scale = d3v7.scaleLinear().domain([yExtrema[0], yExtrema[1]]).range([hp-m[2], m[0]]);

    // Adjust container
    container.style({
      height: self.height + 'px',
      width: self.width + 'px'
    })

    self.add_menu_bar();
    if(!this.use_jsmol) self.add_resizer();

    // Remove any previous structure
    // container.select('svg').remove();
    container.select('div.js-graph-div').remove();
    let graph = null;
    if(!this.use_jsmol) {
      self.graph_div = container.append('div')
          .attr('class', 'js-graph-div')
          .style({
            width: wp + 'px',
            height: hp + 'px',
            margin: '0 auto',
            'margin-top': ((self.height - hp - m[0]) / 2) + 'px',
            // border: '1px solid black'
          })
      graph = self.graph_div.append('svg:svg')
          .attr('width', '100%')
          .attr('height', '100%')
          .append('svg:g');
    }
    return graph;
  };

  SVStructure.prototype.add_menu_bar = function () {
    const self = this;
    // Remove any previous menu bar
    self.container.select('div.menubar').remove();
    const menu = self.container.insert('div', ":first-child")
        .attr('class', 'menubar');
    self.button_toggle_H = menu.append('div')
        .attr('class', 'toggle-hydrogens menu-button button-on')
        .style('width', '60px')
        .text('Show H')
        .on('click', function() {
          self.toggleH();
          d3v7.event.stopPropagation();
        });
    self.button_toggle_numbers = menu.append('div')
        .attr('class', 'toggle-atom-ids menu-button button-on')
        .text('Show #')
        .style('width', '60px')
        // .style('padding-bottom', '50px')
        .on('click', function() {
          self.toggleAtomNumbers();
          d3v7.event.stopPropagation();
        });
    let feedback = "Click and drag to rotate the structure";
    self.feedback_div = menu.append('p')
        .attr('class', 'feedback')
        .text(feedback);
    self.menu_buttons_width  = self.button_toggle_numbers.offsetWidth + self.button_toggle_numbers.offsetWidth;
    // self.menu_title = menu.append('div')
    //     .attr('class', 'structure-title')
    //     .style('width', self.menu_buttons_width + 'px')
    //     .text(self.title);
  }

  SVStructure.prototype.add_resizer = function () {
    const self = this;
    const m = self.margins;
    const wp = self.wp;
    const hp = self.hp;
    const body = d3v7.select('body');
    self.resizer = self.container.append('div')
        .attr('class', 'js-structure-resizer')
        .on('mousedown.resizer', function() {
          let x_mouse = d3v7.event.x;
          let y_mouse = d3v7.event.y;
          d3v7.event.preventDefault();
          body.on('mousemove.resizer', function() {
            // window.getSelection().removeAllRanges()
            dx_mouse = d3v7.event.x - x_mouse;
            dy_mouse = d3v7.event.y - y_mouse;
            x_mouse = d3v7.event.x
            y_mouse = d3v7.event.y
            self.height = self.height + dy_mouse;
            self.width = self.width + dx_mouse;
            self.container.style({
              height: self.height + 'px',
              width: self.width + 'px'
            })
            const container_ratio = self.width / self.height;
            const constrained_dim = (container_ratio >= self.structure_ratio) ? 'height' : 'width';
            let ratio;
            if (constrained_dim == 'height') {
              ratio = (self.height - m[0] - m[2]) / hp;
            } else {
              ratio = (self.width - m[1] - m[3]) / wp;
            }
            self.graph.attr('transform', 'scale(' + ratio + ',' + ratio + ')');
            const graph_div_height = hp * ratio;
            const graph_div_top_margin = (self.height - graph_div_height - m[0]) / 2;
            self.graph_div.style({
              width: wp * ratio + 'px',
              height: hp * ratio + 'px',
              'margin-top': graph_div_top_margin + 'px'
            })
            self.menu_title.style('width', self.width - self.menu_buttons_width + 'px');
            self.trigger('structure-resize');
          });
          body.on('mouseup.resizer', function() {
            body.on('mousemove.resizer', null);
            body.on('mouseup.resizer', null);
          });
        })
        .on('click.resizer', function() {
          d3v7.event.stopPropagation();
        });

    // Draw the resizer icon
    const resizer_path = [
      {x: 9, y: 4}, {x: 4, y: 4}, {x: 4, y: 9}, {x: 4, y: 4}, // Top Left
      {x: 16, y: 16}, // Join line
      {x: 16, y: 11}, {x: 16, y: 16}, {x: 11, y: 16} // Bottom Right
    ];
    const line_function = d3v7.svg.line()
        .x(function (d) {
          return d.x;
        })
        .y(function (d) {
          return d.y;
        })
        .interpolate("linear");
    const resizer_svg = self.resizer.append('svg')
        .style({
          width: '100%',
          height: '100%'
        })
        .append('path')
        .attr("d", line_function(resizer_path))
        .attr("stroke", "grey")
        .attr("stroke-width", 1)
        .attr("stroke-linejoin", 'round')
        .attr("stroke-linecap", 'round')
        .attr("fill", "none");

  }

  /**
   * Draws the bonds onto the SVG element. Note that the bonds are drawn
   * first before anything else is added.
   */
  SVStructure.prototype.drawBonds = function () {
    const self = this;
    const graph = this.graph;
    const x = this.x_scale;
    const y = this.y_scale;
    this.create_bond_functions();
    this.bonds().forEach(function(bond) {
      let dxx1;
      let dyy1;
      let dxx2;
      let dyy2;
      const a1 = bond.a1;
      const a2 = bond.a2;
      let bond_path;

      // apply backing by calculating the unit vector and
      // subsequent scaling: shortens the drawn bond
      const dox = a2.x - a1.x,
          doy = a2.y - a1.y,
          l = Math.sqrt(dox * dox + doy * doy),
          dx = (dox / l) * (0.2),
          dy = (doy / l) * (0.2);

      // get adjusted x and y coordinates
      const x1 = a1.x + dx,
          y1 = a1.y + dy,
          x2 = a2.x - dx,
          y2 = a2.y - dy;

      // update average bond length for font scaling
      self.avgL += distance(x(x1), y(y1), x(x2), y(y2));

      let off,    // offset factor for stereo bonds
          xOff,   // total offset in x
          yOff,   // total offset in y
          xyData = []; // two dimensional data array
      if (bond.order === 1) {                 // single bond
        if (bond.stereo === 1) {            // single wedge bond
          const length = distance(x1, y1, x2, y2);
          off = 0.1;
          xOff = off * (y2 - y1) / length;
          yOff = off * (x1 - x2) / length;
          xyData = [
            [x1, y1],
            [x2 + xOff, y2 + yOff],
            [x2 - xOff, y2 - yOff]
          ];
          bond_path = graph.append('svg:path')
              .style('fill', 'black')
              .style('stroke-width', 1)
              .attr('d', self.wedgeBond(xyData));
        } else if (bond.stereo === 6) {     // single hash bond
          off = 0.2;
          xOff = off * (y2 - y1) / l;
          yOff = off * (x1 - x2) / l;
          dxx1 = x2 + xOff - x1;
          dyy1 = y2 + yOff - y1;
          dxx2 = x2 - xOff - x1;
          dyy2 = y2 - yOff - y1;
          for (let j = 0.05; j <= 1; j += 0.15) {
            xyData.push(
                [x1 + dxx1 * j, y1 + dyy1 * j],
                [x1 + dxx2 * j, y1 + dyy2 * j]
            );
          }

          bond_path = graph.append('svg:path')
              .style('fill', 'none')
              .style('stroke-width', 1)
              .attr('d', self.hashBond(xyData))
              .attr('stroke', 'black');
        } else if (bond.stereo === 4) {     // single wiggly bond
          off = 0.2;
          xOff = off * (y2 - y1) / l;
          yOff = off * (x1 - x2) / l;
          dxx1 = x2 + xOff - x1;
          dyy1 = y2 + yOff - y1;
          dxx2 = x2 - xOff - x1;
          dyy2 = y2 - yOff - y1;
          for (let j = 0.05; j <= 1; j += 0.1) {
            if (xyData.length % 2 === 0) {
              xyData.push(
                  [x1 + dxx1 * j, y1 + dyy1 * j]
              );
            } else {
              xyData.push(
                  [x1 + dxx2 * j, y1 + dyy2 * j]
              );
            }
          }

          bond_path = graph.append('svg:path')
              .attr('d', self.wigglyBond(xyData))
              .attr('fill', 'none')
              .style('stroke-width', 1)
              .attr('stroke', 'black');
        } else {                                // single plain bond
          xyData = [
            [x1, y1], [x2, y2]
          ];
          bond_path = graph.append('svg:path')
              .attr('d', self.plainBond(xyData))
              .attr('stroke-width', '1')
              .attr('stroke-linecap', 'round')
              .attr('stroke-linejoin', 'round')
              .attr('stroke', 'black');
        }
      } else if (bond.order === 2) {          // double bond
        off = 0.1;
        xOff = off * (y2 - y1) / l;
        yOff = off * (x1 - x2) / l;
        xyData = [
          [x1 + xOff, y1 + yOff], [x2 + xOff, y2 + yOff],
          [x1 - xOff, y1 - yOff], [x2 - xOff, y2 - yOff]
        ];
        bond_path = graph.append('svg:path').attr('d', self.plainBond(xyData))
            .attr('stroke-width', '1')
            .style('fill', 'none')
            .attr('stroke-linecap', 'round')
            .attr('stroke-linejoin', 'round')
            .attr('stroke', 'black');
      } else if (bond.order === 3) {          // triple bond
        off = 0.15;
        xOff = off * (y2 - y1) / l;
        yOff = off * (x1 - x2) / l;
        xyData = [
          [x1, y1], [x2, y2],
          [x1 + xOff, y1 + yOff], [x2 + xOff, y2 + yOff],
          [x1 - xOff, y1 - yOff], [x2 - xOff, y2 - yOff]
        ];
        bond_path = graph.append('svg:path')
            .attr('d', self.plainBond(xyData))
            .attr('stroke-width', '1')
            .attr('stroke-linecap', 'round')
            .attr('stroke-linejoin', 'round')
            .attr('stroke', 'black');
      }
      if (a1.is('H') || a2.is('H')) {
        bond_path.classed('bond-H', true);
      }
    });
    self.avgL /= this.bonds().length; // get average bond length
  };

  /**
   * Draws the atoms onto the SVG element. Note that the atoms are drawn
   * on top of the bonds.
   */
  SVStructure.prototype.drawAtoms = function () {
    const graph = this.graph;
    const avgL = this.avgL;
    const x = this.x_scale;
    const y = this.y_scale;
    this.atoms().forEach(function(atom) {
      const g = graph.append('svg:g')
          .attr('transform', 'translate(' + x(atom.x) + ',' + y(atom.y) + ')');
      g.attr('class', 'atom');
      // Add NMR classes
      if ( (atom.is('C') || atom.is('N') ) && (atom.bonded_Hs().length > 0) ) {
        g.classed('nmr-1h', true);
      }
      if ( atom.is('C') ) {
        g.classed('nmr-13c', true);
      }
      if (atom.is('H')) {
        g.classed('atom-H', true);
        if (atom.isBondedTo('C') || atom.isBondedTo('N')) {
          g.classed('nmr-1h', true);
        }
      }
      g.attr('data-atom-number', atom.number);
      g.attr('data-atom-id', atom.id);
      // draw a white circle underneath the text to clean up bonds
      g.append('svg:circle')
          // hack: magic number for scaling
          .attr('r', Math.ceil(avgL / 3))
          .attr('fill', 'white')
          .attr('opacity', '1');
      // draw a transparent circle underneath the text for highlighting
      g.append('svg:circle')
          // hack: magic number for scaling
          .attr('r', Math.ceil(avgL / 2))
          .attr('fill-opacity', '0')
          .attr('class', 'overlay');
      // draw the text string
      g.append('text')
          // hack: magic number for scaling
          .attr('dy', Math.ceil(avgL / 4.5))
          .attr('text-anchor', 'middle')
          .attr('font-family', 'sans-serif')
          // hack: magic number for scaling
          .attr('font-size', Math.ceil(avgL / 1.5))
          .attr('fill', atom.color())
          .text(atom.symbol);
      // Draw atom id
      g.append('text')
          // hack: magic number for scaling
          .attr('dy', Math.ceil(avgL / 2) + 2)
          // .attr('dx', Math.ceil(avgL / 3))
          .attr('text-anchor', 'middle')
          .attr('font-family', 'sans-serif')
          // hack: magic number for scaling
          .attr('font-size', Math.ceil(avgL / 2.5))
          .attr('stroke', '#FFF')
          .attr('stroke-width', '2px')
          .attr('fill', '#595')
          .attr('paint-order', 'stroke')
          .classed('atom-id', true)
          .text(atom.display_id);

      if (atom.charge !== 0) {
        let c = atom.charge;
        if (c < 0) {
          c = (c === -1) ? '-' : (c + '-');
        } else {
          c = (c === +1) ? '+' : (c + '+');
        }
        g.append('text')
            .attr('dx', +1 * Math.ceil(avgL / 3))
            .attr('dy', -1 * Math.ceil(avgL / 4.5))
            .attr('text-anchor', 'left')
            .attr('font-family', 'sans-serif')
            // hack: magic number for scaling (half of symbol size)
            .attr('fill', atom.color())
            .attr('font-size', Math.ceil(avgL / 3))
            .text(c);
      }

      if (atom.mass !== 0) {
        g.append('text')
            .attr('dx', -2 * Math.ceil(avgL / 3))
            .attr('dy', -1 * Math.ceil(avgL / 4.5))
            .attr('text-anchor', 'left')
            .attr('font-family', 'sans-serif')
            // hack: magic number for scaling (half of symbol size)
            .attr('font-size', Math.ceil(avgL / 3))
            .attr('fill', atom.color())
            .text(atom.mass);
      }
    })
  };

  /**
   * Calculates the Euclidean distance between two points.
   *
   * @param {number} x1 A x value of first point
   * @param {number} y1 A y value of first point
   * @param {number} x2 A x value of second point
   * @param {number} y2 A y value of second point
   * @returns {number} the Euclidean distance
   */
  const distance = function (x1, y1, x2, y2) {
    return Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));
  };

  SVStructure.prototype.create_bond_functions = function() {
    const x = this.x_scale;
    const y = this.y_scale;
    /**
     * d3v7 line function using the SVG path mini language to draw a plain bond.
     */
    this.plainBond = d3v7.svg.line()
        .interpolate(function (points) {
          let path = points[0][0] + ',' + points[0][1];
          for (let i = 1; i < points.length; i++) {
            if (i % 2 === 0) {
              path += 'M' + points[i][0] + ',' + points[i][1];
            } else {
              path += 'L' + points[i][0] + ',' + points[i][1];
            }
          }
          return path;
        })
        .x(function (d) {
          return x(d[0]);
        })
        .y(function (d) {
          return y(d[1]);
        });

    /**
     * d3v7 line function using the SVG path mini language to draw a wedge bond.
     */
    this.wedgeBond = d3v7.svg.line()
        .x(function (d) {
          return x(d[0]);
        })
        .y(function (d) {
          return y(d[1]);
        });

    /**
     * d3v7 line function using the SVG path mini language to draw a hash bond.
     */
    this.hashBond = d3v7.svg.line()
        .interpolate(function (points) {
          let path = points[0][0] + ',' + points[0][1];
          for (let i = 1; i < points.length; i++) {
            if (i % 2 === 0) {
              path += 'M' + points[i][0] + ',' + points[i][1];
            } else {
              path += 'L' + points[i][0] + ',' + points[i][1];
            }
          }
          return path;
        })
        .x(function (d) {
          return x(d[0]);
        })
        .y(function (d) {
          return y(d[1]);
        });

    /**
     * d3v7 line function using the SVG path mini language to draw a wiggly bond.
     */
    this.wigglyBond = d3v7.svg.line()
        .interpolate('cardinal')
        .x(function (d) {
          return x(d[0]);
        })
        .y(function (d) {
          return y(d[1]);
        });
  }


  /*
   * Atom properties containing the CPK color values.
   */
  AtomColors = {
    H: '#000000',
    He: '#FFC0CB',
    Li: '#B22222',
    B: '#00FF00',
    C: '#000000',
    N: '#8F8FFF',
    O: '#F00000',
    F: '#DAA520',
    Na: '#0000FF',
    Mg: '#228B22',
    Al: '#808090',
    Si: '#DAA520',
    P: '#FFA500',
    S: '#FFC832',
    Cl: '#00FF00',
    Ca: '#808090',
    Ti: '#808090',
    Cr: '#808090',
    Mn: '#808090',
    Fe: '#FFA500',
    Ni: '#A52A2A',
    Cu: '#A52A2A',
    Zn: '#A52A2A',
    Br: '#A52A2A',
    Ag: '#808090',
    I: '#A020F0',
    Ba: '#FFA500',
    Au: '#DAA520'
  };

  /////////////////////////////////////////////////////////////////////////////
  // Structure Properties (setters/getters)
  /////////////////////////////////////////////////////////////////////////////
  Object.defineProperties(SVStructure.prototype, {
    'selected_atoms': {
      get: function() {
        const self = this;
        const selected = new JSV.SVSet();
        this.container.selectAll('.atom.selected').each(function(){
          const atom_id = d3v7.select(this).attr('data-atom-id');
          selected.push(self.atoms(atom_id));
        });
        return selected
      },
      set: function(atoms) {
        const self = this;
        const container = this.container;
        container.selectAll('.atom').classed('selected', false);
        atoms.forEach(function(atom) {
          container.select('.atom[data-atom-id="' + atom.id + '"]').classed('selected', true);
        });
        self.add_partially_selected_highlighting(atoms);
      }
    }
  });

  SVStructure.prototype.add_partially_selected_highlighting = function(atoms) {
    const self = this;
    const container = this.container;
    container.selectAll('.atom').classed('selected-partially', false);
    if (self.nmr_1H()) {
      const hydrogens = self.hydrogens_from_atoms(atoms);
      let parents = hydrogens.map(function (h) {
        return h.bonded_atoms(1)
      });
      parents = new JSV.SVSet(parents).unique();
      parents.forEach(function(p) {
        let all_Hs_in_list = true;
        const child_Hs = p.bonded_Hs();
        child_Hs.forEach(function(child_H) {
          if (!hydrogens.contains(child_H)) {
            all_Hs_in_list = false;
          }
        });
        if (!all_Hs_in_list) {
          container.select('.atom[data-atom-id="' + p.id + '"]').classed('selected-partially', true);
        }
      });
    }
  }

  SVStructure.prototype.read_data = function (data, format) {
    if (format == 'mol') {
      this.read_mol(data);
    } else if (format == 'nmrml') {
      this.read_nmrml(data);
    } else {
      console.log('Format not recognized: ' + format + '. Must be one of the following: nmrml, mol')
    }
  }

  // Read structure from Chemical Markup Language (nmrml)
  // MUST start with <structure> tag
  // Requires x2js
  // <structure>
  //   <atomList>
  //     <atom id="a1" elementType="C" x="0.6667733333333334" y="-0.38490666666666673"/>
  //     <atom id="a2" elementType="C" x="-0.6667733333333334" y="-1.1550933333333335"/>
  //   </atomList>
  //   <bondList>
  //     <bond atomRefs="a1 a2" order="1"/>
  //   </bondList>
  // </structure>
  SVStructure.prototype.read_nmrml = function (nmrml) {

    const self = this;
    if (!window.X2JS) {
      console.log("X2JS needs to be installed to read nmrML: https://github.com/abdmob/x2js");
      return
    }
    const x2js = new X2JS();
    const json = x2js.xml_str2json(nmrml);
    this.data = nmrml;
    // Empyt any atoms and bonds
    this._atoms = new JSV.SVSet();
    this._bonds = new JSV.SVSet();
    // Validate
    if (!(json.structure && json.structure.atomList && json.structure.atomList.atom && Array.isArray(json.structure.atomList.atom))) {
      console.log('nmrML not formatted correctly: no atoms found');
      return
    }
    if (!(json.structure && json.structure.bondList && json.structure.bondList.bond && Array.isArray(json.structure.bondList.bond))) {
      console.log('nmrML not formatted correctly: no bonds found');
      return
    }

    // Parse atoms
    json.structure.atomList.atom.forEach(function(json_atom, index) {
      const atom = new Atom({
        id: json_atom._id.replace(/\D/g, ''),
        x: parseFloat(json_atom._x),
        y: parseFloat(json_atom._y),
        symbol: json_atom._elementType,
        number: index + 1, // base-1 atom-index
      });
      atom.display_id = atom.id;
      self._atoms.push(atom);
    })

    // Parse Bonds
    json.structure.bondList.bond.forEach(function(json_bond) {
      const atom_ids = json_bond._atomRefs.split(/\s+/);
      const a1 = self.atoms(atom_ids[0].replace(/\D/g, ''));
      const a2 = self.atoms(atom_ids[1].replace(/\D/g, ''));
      const bond = new Bond({
        a1: a1,
        a2: a2,
        order: parseInt(json_bond._order)
        // values 0 (plain),1 (wedge),4 (wiggly),6 (hash)
        // stereo: parseInt(json_bond._??)
      });
      self._bonds.push(bond);
      // Connect atoms with bonds and connected atoms
      a1._bonds.push(bond);
      a2._bonds.push(bond);
      a1._bonded_atoms.push(a2);
      a2._bonded_atoms.push(a1);
    });
    if(this.use_jsmol) this.generate_molfile(json)


    // self.addAtomDisplayIDs();
    self.draw();
  }

  SVStructure.prototype.generate_molfile = function(json){
    console.log("Generating molfile...")
    let self = this;
    let atom_coordinate = function(coord){
      let formatted = "  "
      if(typeof coord === "undefined"){
        formatted += "  0.0000"
      } else {
        coord = parseFloat(coord).toFixed(4)
        if (coord >= 0) formatted += " ";
        if (Math.abs(coord) < 10) formatted += " ";
        formatted += `${coord}`
      }
      return formatted
    }

    self.mol_file = `\nJSpectraViewer\n\n ${json.structure.atomList.atom.length} ${json.structure.bondList.bond.length} 0 0 0 0 0 0 0 0999 V2000`
    json.structure.atomList.atom.forEach(function(json_atom) {
      self.mol_file += `\n${atom_coordinate(json_atom._x)}${atom_coordinate(json_atom._y)}${atom_coordinate(json_atom._z)}`
      self.mol_file += ` ${json_atom._elementType}   0  0  0  0  0  0  0  0  0  0  0  0`
    })

    json.structure.bondList.bond.forEach(function(json_bond) {
      let atom_ids = json_bond._atomRefs.split(/\s+/);
      const id_1 = parseInt(atom_ids[0].replace(/\D/g,''))
      const id_2 = parseInt(atom_ids[1].replace(/\D/g,''))
      self.mol_file += (id_1 < 10 ) ? `\n  ${id_1}` : `\n ${id_1}`
      self.mol_file += (id_2 < 10 ) ? `  ${id_2}` : ` ${id_2}`
      self.mol_file += `  ${json_bond._order}  0  0  0  0`
    })
    self.mol_file += '\nM  END'
    return
  }


  SVStructure.prototype.read_mol = function (molfile) {
    let props;
    const self = this;
    const lines = molfile.split(/\r\n|\n/),
        counter = lines[3].match(/\d+/g),
        nAtoms = parseFloat(counter[0]),
        nBonds = parseFloat(counter[1]);
    const offset = 4; // the first three lines belong to the header block
    this.title = lines[1];
    this.data = molfile;
    // Empyt any atoms and bonds
    this._atoms = new JSV.SVSet();
    this._bonds = new JSV.SVSet();
    // Parse Atom Block
    for (let i = offset; i < nAtoms + offset; i++) {
      let atom = lines[i].match(/-*\d+\.\d+|\w+/g);
      atom = new Atom({
        x: parseFloat(atom[0]),
        y: parseFloat(atom[1]),
        symbol: atom[3],
        number: i - offset + 1, // base-1 atom-index
        mass: 0,    // deprecated
        charge: 0   // deprecated
      });
      // atom.id = 'a' + atom.number;
      atom.id = atom.number;
      atom.display_id = atom.number;
      this._atoms.push(atom);
    }
    // Parse Bond Block
    for (let j = nAtoms + offset; j < nAtoms + nBonds + offset; j++) {
      let bond = lines[j].match(/\d+/g);
      const a1 = this.atoms(parseInt(bond[0]));
      const a2 = this.atoms(parseInt(bond[1]));
      bond = new Bond({
        a1: a1,
        a2: a2,
        // values 1, 2, 3
        order: parseInt(bond[2]),
        // values 0 (plain),1 (wedge),4 (wiggly),6 (hash)
        stereo: parseInt(bond[3])
      });
      this._bonds.push(bond);
      // Connect atoms with bonds and connected atoms
      a1._bonds.push(bond);
      a2._bonds.push(bond);
      a1._bonded_atoms.push(a2);
      a2._bonded_atoms.push(a1);
    }
    // Parse Properties Block
    for (let k = (nAtoms + nBonds) + offset; k < lines.length; k++) {
      if (lines[k].indexOf('M  ISO') !== -1) {
        props = lines[k].match(/-*\d+/g);
        for (let l = 0, m = 1; l < props[0]; l++, m += 2) {
          this.atoms(props[m]).mass = parseInt(props[m + 1], 10);
        }
      } else if (lines[k].indexOf('M  CHG') !== -1) {
        props = lines[k].match(/-*\d+/g);
        for (let l = 0, m = 1; l < props[0]; l++, m += 2) {
          atoms[props[m] - 1].charge = parseInt(props[m + 1], 10);
          this.atoms(props[m]).charge = parseInt(props[m + 1], 10);
        }
      }
    }
    // self.addAtomDisplayIDs();
    self.draw();
  }

  SVStructure.prototype.loadJsmolScript = function(url, callback){
    let script = document.createElement("script")
    script.type = "text/javascript";

    if (script.readyState){  //IE
      script.onreadystatechange = function(){
        if (script.readyState === "loaded" ||
            script.readyState === "complete"){
          script.onreadystatechange = null;
          callback();
        }
      };
    }
    else {  //Others
      script.onload = function(){
        callback();
      };
    }
    script.src = url;
    document.getElementsByTagName("head")[0].appendChild(script);
  }

  SVStructure.prototype.atom_picked = function(){
    // let sv = window.sv;
    // let assignment_table = sv.assignment_table
    // let structure = window.sv.structure;

    let sv = window.sv;
    console.log("this", this, window)
    console.log("sv", sv)
    let assignment_table = sv.assignment_table
    let structure = this
    console.log("atom_picked", structure, this.structure, sv)

    let index  = arguments[2] + 1;
    let atoms = new JSV.SVSet(structure.atoms(index));
    let peaks = new JSV.SVSet();

    Jmol.script(jmol_jspectraviewer, `select carbon; colour halos grey; select atomno = ${index}; colour halos blue;`)

    let cluster = assignment_table.cluster_from_atoms(atoms);

    if (cluster) {
      peaks = cluster.peaks();
      atoms = cluster.atoms();
    }

    // Select peaks in viewer
    sv.selection._elements = peaks.unique();
    sv.draw();

    // Select peaks and atoms in table
    assignment_table.selected_peaks = peaks;
    assignment_table.selected_atoms = structure.LCA_from_nmr_atoms(atoms);

    // Select cluster row in table
    assignment_table.table.selectAll('tbody tr').classed('selected', false);
    if (cluster) {
      assignment_table.table.select('tbody tr[data-cluster-path-id='+cluster.path_id()+']').classed('selected', true);
    }
  }

  SVStructure.prototype.run_jsmol = function(){
    // Height and width of the JSmol applet
    let parent_div = this.container
    let width = parseFloat(parent_div.style('width'))-10, height = parseFloat(parent_div.style('height'))-30

    let load_type = "INLINE"

    let labels = `labels off; select ${(this.nmr_nucleus === "13C") ? 'carbon' : 'hydrogen'}; label %a;`
    let show_hydrogens = this.show_hydrogen ? 'set showHydrogens TRUE;' : 'set showHydrogens FALSE;'
    let background = "background white;";
    let callbacks = 'set PickCallback "sv.structure.atom_picked";'
    let molfile = this.mol_file;

    let Info = {
      width: width,
      height: height,
      debug: false,
      color : "#EAF0F2",
      use: "HTML5",   // JAVA HTML5 WEBGL are all options
      j2sPath: "/jsmol/j2s", // this needs to point to where the j2s directory is.
      multipleBondSpacing : 0.9,
      zoomlarge: true,
      allowJavaScript: true
    };

    Info.script = 'load ' + load_type + ' "' + molfile +
        '"; color labels black; font labels 20; set antialiasDisplay; wireframe 15; spacefill 55; select carbon; halos on; colour halos grey;' +
        labels + show_hydrogens + background + callbacks
    $("#view-3d").html(Jmol.getAppletHtml('jmol_jspectraviewer', Info))

    this.atom_picked()

  }

  /**
   * Parses the molfile, extracting the molecule title from the
   * header block, two dimensional coordinates, symbol, charge,
   * and mass difference information extracted from the atom block,
   * connectivity and stereo information from the bond block.
   *
   * @param {string} molfile A URL to the MDL molfile (REST web service)
   * @param {string} id An element identifier or the element itself
   */
  SVStructure.prototype.draw = function () {
    let self = this;
    this.graph = this.initSVG();   // layout SVG
    this.avgL = 0;
    if(!this.use_jsmol) {
      this.drawBonds();
      this.drawAtoms();
      this.addInteractivity();
    } else{
      this.container.append('div').attr('id', 'view-3d').attr('style', 'position: relative; z-index: 0;')
      try {
        self.run_jsmol()
      }
      catch(e){
        self.loadJsmolScript("/jsmol/JSmol.min.js", function(){
          self.run_jsmol()
          self.initialize_jsmol_atom_picking()
        })
      }
    }
    this.toggleH(this.show_hydrogen);
    this.toggleAtomNumbers(this.show_atom_numbers);
  };

  SVStructure.prototype.initialize_jsmol_atom_picking = function(){

  }

  SVStructure.prototype.atoms = function(term) {
    return this._atoms.get(term)
  }

  SVStructure.prototype.bonds = function(term) {
    return this._bonds.get(term)
  }

  SVStructure.prototype.addInteractivity = function () {
    const self = this;
    const container = this.container;
    container.on('mouseenter', function() {
      container.selectAll('.atom').style('cursor', 'default');
      if (self.nmr_1H()) {
        container.selectAll('.atom.nmr-1h')
            .classed('highlight', true)
            .style('cursor', 'pointer');
      } else if (self.nmr_13C()) {
        container.selectAll('.atom.nmr-13c')
            .classed('highlight', true)
            .style('cursor', 'pointer');
      }
    });
    container.on('mouseleave', function() {
      container.selectAll('.atom').classed('highlight', false);
    });

    container.selectAll('.atom').on('click', function() {
      const atom_id = d3v7.select(this).attr('data-atom-id');
      let clicked_atom;
      if (atom_id) {
        clicked_atom = self.atoms(atom_id);
        if (self.nmr_1H()) {
          self.select_clicked_atoms_for_1H_nmr(clicked_atom, d3v7.event.shiftKey);
        } else  if (self.nmr_13C()) {
          self.select_clicked_atoms_for_13C_nmr(clicked_atom, d3v7.event.shiftKey);
        }
      }
      self.trigger('structure-click');
      d3v7.event.stopPropagation();
    });

    container.on('click', function() {
      self.selected_atoms = [];
      self.trigger('structure-click');
    });
  }

  SVStructure.prototype.select_clicked_atoms_for_1H_nmr = function (clicked_atom, shift_key) {
    const self = this;
    let selected_atoms = this.selected_atoms;
    if (shift_key) {
      // Prevent selection of text when shift-clicking
      window.getSelection().removeAllRanges()
      if (selected_atoms.contains(clicked_atom)) {
        if (clicked_atom.is('H')) {
          const parent = clicked_atom.bonded_atoms(1);
          selected_atoms = selected_atoms.remove(parent).remove(clicked_atom);
        } else {
          clicked_atom.bonded_Hs().forEach(function(bonded_H) {
            selected_atoms = selected_atoms.remove(bonded_H);
          });
          selected_atoms = selected_atoms.remove(clicked_atom);
        }
      } else {
        selected_atoms.push(clicked_atom);
      }
    } else {
      selected_atoms = new JSV.SVSet(clicked_atom);
    }
    self.selected_atoms = selected_atoms;

  }

  SVStructure.prototype.select_clicked_atoms_for_13C_nmr = function (clicked_atom, shift_key) {
    const self = this;
    let selected_atoms = this.selected_atoms;
    if (shift_key) {
      // Prevent selection of text when shift-clicking
      window.getSelection().removeAllRanges()
      if (selected_atoms.contains(clicked_atom)) {
        selected_atoms = selected_atoms.remove(clicked_atom);
      } else {
        selected_atoms.push(clicked_atom);
      }
    } else {
      selected_atoms = new JSV.SVSet(clicked_atom);
    }
    self.selected_atoms = selected_atoms;
  }

  // toggle H visibility
  SVStructure.prototype.toggleH = function (show) {
    this.show_hydrogen = (show === undefined) ? !this.show_hydrogen : show;
    if (this.show_hydrogen) {
      d3v7.selectAll('.atom-H, .bond-H').style('display', 'inline');
      this.button_toggle_H.classed('button-on', true)
      this.button_toggle_H.text('Hide H')
      if(typeof Jmol !== 'undefined') Jmol.script(jmol_jspectraviewer, "select all; set showHydrogens TRUE;")
    } else {
      d3v7.selectAll('.atom-H, .bond-H').style('display', 'none');
      this.button_toggle_H.classed('button-on', false)
      this.button_toggle_H.text('Show H')
      if(typeof Jmol !== 'undefined') Jmol.script(jmol_jspectraviewer, "select all; set showHydrogens FALSE;")
    }
  }

  SVStructure.prototype.toggleAtomNumbers = function (show) {
    this.show_atom_numbers = (show === undefined) ? !this.show_atom_numbers : show;
    if (this.show_atom_numbers) {
      d3v7.selectAll('.atom-id').style('display', 'inline');
      this.button_toggle_numbers.classed('button-on', true);
      this.button_toggle_numbers.text('Hide #');
      if(typeof Jmol !== 'undefined'){
        if(this.nmr_nucleus === "13C"){
          Jmol.script(jmol_jspectraviewer, "select carbon; labels %a;")
        } else{
          Jmol.script(jmol_jspectraviewer, "select hydrogen; labels %a;")
        }
      }
    } else {
      d3v7.selectAll('.atom-id').style('display', 'none');
      this.button_toggle_numbers.classed('button-on', false);
      this.button_toggle_numbers.text('Show #');
      if(typeof Jmol !== 'undefined'){
        if(this.nmr_nucleus === "13C"){
          Jmol.script(jmol_jspectraviewer, "select carbon; labels %e;")
        } else{
          Jmol.script(jmol_jspectraviewer, "select hydrogen; labels %e;")
        }
      }
    }
  }

  // Create atom display numbers using modified Morgan Algorithm
  // Note: the display ids are only visible in the structure and in the
  // assignment table. They have no effect on the underlying atom ids.
  // So the method can be changed without worrying about the ids stored in the nmrML.
  SVStructure.prototype.addAtomDisplayIDs = function () {
    const self = this;
    const atoms = new JSV.SVSet(self.atoms().filter(function (a) {
      return !a.is('H')
    }));
    atoms.each(function() { this.morgan_num = 1; });
    let morgan_numbers = new JSV.SVSet();
    let unique_size = -1;
    while (unique_size != morgan_numbers.unique().length) {
      // Reset numbers
      unique_size = morgan_numbers.unique().length;
      morgan_numbers = new JSV.SVSet();
      // Calculate morgan numbers for each atom
      atoms.forEach(function(atom) {
        let temp = 0;
        atom.bonded_atoms().each(function() {
          if (!this.is('H')) {
            temp += this.morgan_num;
          }
        });
        atom.morgan_temp = temp;
      });
      atoms.forEach(function(atom) {
        atom.morgan_num = atom.morgan_temp;
        morgan_numbers.push(atom.morgan_num);
      });
    }
    atoms.order_by('morgan_num', true);

    let current_atom = atoms[0];
    let current_id = 1;
    let next_atom;
    while (current_atom) {
      current_atom.display_id = current_id;
      let possible_atoms = current_atom.bonded_atoms().filter(function (a) {
        return !a.is('H') && a.display_id == undefined
      });
      possible_atoms = new JSV.SVSet(possible_atoms);
      if (possible_atoms.length > 0) {
        next_atom = possible_atoms.order_by('morgan_num', true)[0];
      } else {
        next_atom = atoms.find(function(a) { return a.display_id == undefined });
      }
      current_atom = next_atom;
      current_id += 1;
    }
    this.addHydrogenDisplayIDs();
  }

  SVStructure.prototype.addHydrogenDisplayIDs = function () {
    const indexMap = ['a', 'b', 'c', 'd'];
    this.atoms().forEach(function(atom) {
      if (atom.is('H')) { return; }
      atom.bonded_Hs().forEach(function(hydrogen, index) {
        hydrogen.display_id = atom.display_id + indexMap[index];
      });
    });
  }

  SVStructure.prototype.hydrogens_from_atoms = function (atoms) {
    const hydrogens = new JSV.SVSet();
    atoms.forEach(function(atom) {
      if (atom.toString() === 'Atom') {
        if (atom.is('H')) {
          hydrogens.push(atom);
        } else {
          hydrogens.merge(atom.bonded_Hs());
        }
      }
    });
    return hydrogens.unique()
  }

  SVStructure.prototype.nmr_atoms_from_atoms = function (atoms) {
    const self = this;
    if (self.nmr_1H()) {
      return self.hydrogens_from_atoms(atoms);
    }
    if (self.nmr_13C()) {
      return new JSV.SVSet( atoms.filter(function(atom) { return atom.is('C'); }) ).unique();
    }
  }

  // LCA: Least Common Ancestor
  // Atoms can be hydrogens and/or LCA atoms
  // Return a simplified list of atoms to represent the significant hydrogens.
  // If a Carbon has 2 hydrogens and they are both in the list of atoms, then the Carbon atom
  // is included instead of the hydrogens.
  // If only one of the 2 hydrogens is in the list, then only the one hydrogen atom is included
  SVStructure.prototype.LCA_from_hydrogens = function (atoms) {
    const lca = new JSV.SVSet();
    if (!atoms || atoms.empty()) { return lca }
    const hydrogens = this.hydrogens_from_atoms(atoms);
    let parents = hydrogens.map(function (h) {
      return h.bonded_atoms(1)
    });
    parents = new JSV.SVSet(parents).unique();
    parents.forEach(function(p) {
      let all_Hs_in_list = true;
      const listed_Hs = new JSV.SVSet();
      const child_Hs = p.bonded_Hs();
      child_Hs.forEach(function(child_H) {
        if (hydrogens.contains(child_H)) {
          listed_Hs.push(child_H);
        } else {
          all_Hs_in_list = false;
        }
      });
      if (all_Hs_in_list) {
        lca.push(p);
      } else {
        lca.merge(listed_Hs);
      }
    });

    return lca.unique()
  }


  SVStructure.prototype.LCA_from_nmr_atoms = function (atoms) {
    const self = this;
    if (self.nmr_1H()) {
      return self.hydrogens_from_atoms(atoms);
    }
    if (self.nmr_13C()) {
      return new JSV.SVSet( atoms.filter(function(atom) { return atom.is('C'); }) ).unique();
    }
  }

  SVStructure.prototype.LCA_and_hydrogens = function (atoms) {
    const hydrogens = this.hydrogens_from_atoms(atoms);
    const lca = this.LCA_from_hydrogens(atoms);
    return hydrogens.merge(lca).unique();
  }

  SVStructure.prototype.LCA_and_nmr_atoms = function (atoms) {
    const self = this;
    if (self.nmr_1H()) {
      return self.LCA_and_hydrogens(atoms);
    }
    if (self.nmr_13C()) {
      return new JSV.SVSet( atoms.filter(function(atom) { return atom.is('C'); }) ).unique();
    }
  }

  function Atom(options) {
    options = options || {};
    this.x = JSV.default_for(options.x, 0);
    this.y = JSV.default_for(options.y, 0);
    this.id = options.id;
    this.symbol = JSV.default_for(options.symbol, 0);
    this.index = JSV.default_for(options.index, 0);
    this.number = JSV.default_for(options.number, 1);
    this.mass = JSV.default_for(options.mass, 0);
    this.charge = JSV.default_for(options.charge, 0);
    this._bonds = new JSV.SVSet();
    this._bonded_Hs = new JSV.SVSet();
    this._bonded_atoms = new JSV.SVSet();
  }

  Atom.prototype.toString = function() {
    return "Atom"
  }

  Atom.prototype.bonds = function(term) {
    return this._bonds.get(term)
  }

  Atom.prototype.bonded_atoms = function(term) {
    return this._bonded_atoms.get(term)
  }

  Atom.prototype.bonded_Hs = function(term) {
    const filtered = this.bonded_atoms().filter(function (atom) {
      return atom.is('H')
    });
    return new JSV.SVSet(filtered)
  }

  Atom.prototype.is = function(symbol) {
    return this.symbol == symbol
  }

  Atom.prototype.isBondedTo = function(symbol) {
    return this.bonded_atoms().some(function(a) { return a.symbol == symbol })
  }

  Atom.prototype.color = function() {
    return d3v7.rgb(AtomColors[this.symbol]);
  }

  function Bond(options) {
    options = options || {};
    this.a1 = options.a1
    this.a2 = options.a2
    this.order = JSV.default_for(options.order, 1);
    this.stereo = JSV.default_for(options.stereo, 0);
    this._atoms = new JSV.SVSet([this.a1, this.a2]);
  }

  Bond.prototype.toString = function() {
    return "Bond"
  }

  Bond.prototype.atoms = function(term) {
    return this._atoms.get(term)
  }

  JSV.SVStructure = SVStructure;

})(JSpectraViewer);

//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Assignment Table
//////////////////////////////////////////////////////////////////////////////
// NOTE: requires Sortable to move peaks and atom badges
// https://github.com/RubaXa/Sortable
(function(JSV) {

  /**
   * The SVAssignments object controls peak and atom assignments of clusters on
   * Viewer. The [Sortable](https://github.com/RubaXa/Sortable) library is
   * required to move peak and atom badges.
   * The following options can be set when creating a
   * [SpectraViewer](SpectraViewer.js.html):
   *
   *  Option                | Default     | Description
   *  ----------------------|-------------------------------------------------
   *  container_id          | _undefined_ | ID of dom element where the structure will be draw (e.g. '#my-structure')
   *  nmr_nucleus           | 1H          | One of '1H' or '13C'
   *  edit_mode             | false       | Can the assignments be manually edited?
   *
   *
   * @param {Object} sv The [SpectraViewer](SpectraViewer.js.html) object
   * @param {Object} options Options for how Assignment Table should work. Described below.
   * @return {SVSelection}
   */
  function SVAssignments(sv, options = {}) {
    const self = this;
    this.sv = sv;
    this.struct = sv.structure;
    this.container_id = options.container_id;
    this.container = d3v7.select(this.container_id);
    this.table = this.container.append('table').classed('jsv-assignment-table', true);
    this.nmr_nucleus = JSV.default_for(options.nmr_nucleus, '1H'); // Sets up table header
    if (!window.Sortable) {
      console.log("Sortable needs to be installed to make peaks/atoms dragable: https://github.com/RubaXa/Sortable");
      this.sortable = false;
    }
    this.add_sv_events();
    if (this.struct) {
      this.add_struct_events();
    }
    this.edit_mode = options.edit_mode; // calls update_table
  }

  SVAssignments.prototype.nmr_1H = function () {
    return this.nmr_nucleus && this.nmr_nucleus.toUpperCase() == '1H'
  }

  SVAssignments.prototype.nmr_13C = function () {
    return this.nmr_nucleus && this.nmr_nucleus.toUpperCase() == '13C'
  }


  SVAssignments.prototype.get_columns = function() {
    const self = this;
    const struct = self.struct;
    const formatter = d3v7.format('.2f');

    const create_atoms_html = function(atoms, atom_refs) {
      let html = "";
      if ( atoms && atoms.length > 0) {
        const lca_atoms = struct.LCA_from_nmr_atoms(atoms);
        html = lca_atoms.map(function(atom) {
          return "<div class='jsv-badge atom-badge' data-atom-id='"+atom.id+"'>"+atom.display_id+"</div>"
        }).join('');
      } else if (atom_refs && atom_refs.length > 0) {
        html =  atom_refs.map(function(ref) {
          return "<div class='jsv-badge atom-badge' data-atom-id=''>"+ref+"</div>"
        }).join('');
      }
      return html
    }

    const create_add_atoms_html = function(atoms, index) {
      return "<div class='add-atoms-container'><a class='add-atoms jsv-btn' href='javascript:void(0)'>+</a><div>";
    }

    const create_peak_centers_html = function(peaks) {
      return peaks.reverse().map(function(peak) {
        return "<span class='jsv-badge peak-badge' data-peak-id='"+peak.path_id()+"'>"+formatter(peak.center)+"</span>"
      }).join('')
    }

    const columns = [
      { heading: 'Row No.', class: 'cluster-number center', html: function(row)  { return row.number }},
      { heading: 'Cluster Midpoint', class: 'center', html: function(row)  { return formatter(row.center)}},
      { heading: 'No. Peaks', class: 'center', html: function(row)  { return row.peak_num }},
      { heading: 'Coupling Type', class: 'center', html: function(row)  { return row.multiplet_type }},
      { heading: "No. H's", class: 'center', html: function(row)  { return row.atoms.length }},
      { heading: 'Atom No.', class: 'atom-cell', html: function(row)  { return create_atoms_html(row.atoms, row.atom_refs) }},
      { heading: '', class: 'add-atom-cell', html: function(row, i)  { return create_add_atoms_html(row.atoms, i) }},
      { heading: 'Peak Centers (ppm)', class: 'peak-cell', html: function(row)  { return create_peak_centers_html(row.peaks) }}
    ];
    if (self.nmr_13C()) {
      columns[4].heading = "No. C's";
    }
    if (!struct) {
      // Remove H's, Atoms and '' columns
      columns.splice(4, 3);
    }
    return columns
  }

  // Table code concept: https://vis4.net/blog/posts/making-html-tables-in-d3v7-doesnt-need-to-be-a-pain/
  SVAssignments.prototype.update_table_header = function() {
    this.table.select('thead').remove();
    this.table.append('thead')
        .append('tr')
        .append('th')
        .attr('class', 'center')
        .attr('style', 'font-size: larger')
        .attr('colspan', this.columns.length)
        .text("Peak Assignment Table")
    this.table.select('thead')
        .append('tr')
        .selectAll('th')
        .data(this.columns).enter()
        .append('th')
        .attr('class', function(col) { return col.class })
        .text(function(col) { return col.heading });
  }

  SVAssignments.prototype.update_table = function() {
    // console.log('TABLE UPDATED')
    const self = this;
    const struct = this.struct;
    const table = this.table;
    const columns = this.columns;
    // Get row data from SV
    rows = [];
    // Make the cluster navigator static
    this.sv._cluster_navigation.static_compounds = this.sv.compounds();

    this.sv.clusters().each(function(index) {
      row = {
        path_id: this.path_id(),
        number: index + 1,
        center: this.center(),
        peak_num: this.peaks().length,
        peaks: this.peaks(),
        multiplet_type: this.multiplet_type,
        atoms: this.atoms(),
        atom_refs: this.atom_refs
      };
      rows.push(row);
    });
    // Remove old data
    table.select('tbody').remove();
    // Add new data
    if (rows.length > 0) {
      table.append('tbody')
          .selectAll('tr')
          .data(rows).enter()
          .append('tr').attr('data-cluster-path-id', function(row) { return row.path_id })
          .selectAll('td')
          .data(function(row, i) {
            return columns.map(function(col) {
              const cell = {};
              Object.keys(col).forEach(function(key) {
                cell[key] = (typeof col[key] == 'function') ? col[key](row, i) : col[key];
              });
              return cell
            });
          }).enter()
          .append('td')
          .html(function(col) { return col.html })
          .attr('class', function(col) { return col.class });
    } else {
      table.append('tbody').append('tr').append('td')
          .attr('colspan', columns.length)
          .style('text-align', 'center')
          .html('No Clusters')
    }

    // Add table events
    this.add_table_events();

    // Adjust Atom Badge colors
    d3v7.selectAll('.atom-badge').each(function() {
      const badge = d3v7.select(this);
      const atom = struct.atoms(badge.text());
      if (atom) {
        badge.classed('atom-badge-symbol-'+ atom.symbol, true);
      }
    });

    if (self.edit_mode) {
      this.make_sortable();
    } else {
      // Hide add atoms button, which are visible by default
      table.selectAll('.add-atoms').style('display', 'none');
    }
  }

  SVAssignments.prototype.make_sortable = function() {
    const self = this;
    const table = this.table;
    const struct = this.struct;
    table.classed('editable', true);
    // Make Atoms sortable
    table.selectAll('tbody tr .atom-cell').each(function() {
      Sortable.create(this, {
        draggable: '.atom-badge', group: 'atoms', animation: 150, ghostClass: 'badge-ghost',
        onAdd: function(evt) {
          let atom;
          const new_cluster = self.cluster_from_table_child(evt.item);
          const old_cluster = self.cluster_from_table_child(evt.from);
          // console.log(old_cluster.path_id() + ' -> ' + new_cluster.path_id());
          const atom_id = d3v7.select(evt.item).attr('data-atom-id');
          if (atom_id) {
            atom = struct.atoms(atom_id);
          }
          if (atom && new_cluster && old_cluster) {
            const moved_atoms = struct.nmr_atoms_from_atoms([atom]);
            atoms = new_cluster.atoms().merge(moved_atoms).unique();
            new_cluster._atoms = atoms;
            old_cluster._atoms = new JSV.SVSet(old_cluster._atoms.filter(function(a) { return !moved_atoms.contains(a) }));
            self.update_table();
          }
          self.select_peaks_and_atoms(self.selected_atoms);
        },
        onEnd: function(evt) {
          const atom_id = d3v7.select(evt.item).attr('data-atom-id');
          if (atom_id) {
            atom = struct.atoms(atom_id);
            self.selected_atoms = new JSV.SVSet(atom);
            self.select_peaks_and_atoms(self.selected_atoms);
          }
        }
      })
    });

    // Make peaks sortable
    table.selectAll('tbody tr td.peak-cell').each(function() {
      Sortable.create(this, {
        draggable: '.peak-badge', group: 'peaks', animation: 150, ghostClass: 'badge-ghost',
        onAdd: function(evt) {
          let peak;
          const new_cluster = self.cluster_from_table_child(evt.item);
          const old_cluster = self.cluster_from_table_child(evt.from);
          // console.log(old_cluster.path_id() + ' -> ' + new_cluster.path_id());
          const peak_id = d3v7.select(evt.item).attr('data-peak-id');
          if (peak_id) {
            peak = sv.peaks(peak_id);
          }
          if (peak && new_cluster && old_cluster) {
            // old_cluster._peaks = new JSV.SVSet(old_cluster._peaks.filter(function(p) { return p != peak }));
            // Remove peak from previous cluster
            peak.delete();
            // Add peak to cluster and update
            new_cluster.add_peak(peak);
            new_cluster.update();
            if (sv._cluster_navigation) {
              sv._cluster_navigation.update_table();
            }
            self.update_table();
          }
          self.select_peaks_and_atoms(self.selected_peaks);
        },
        onEnd: function(evt) {
          const peak_id = d3v7.select(evt.item).attr('data-peak-id');
          if (peak_id) {
            peak = sv.peaks(peak_id);
            self.selected_peaks = new JSV.SVSet(peak);
            self.select_peaks_and_atoms(self.selected_peaks);
          }
        }
      })

    });
  }

  SVAssignments.prototype.add_table_events = function() {
    const self = this;
    const table = this.table;
    const sv = this.sv;
    const struct = this.struct;

    table.selectAll('tbody tr').on('click', function() {
      table.selectAll('tbody tr').classed('selected', false);
      d3v7.select(this).classed('selected', true);
      const path_id = d3v7.select(this).attr('data-cluster-path-id');
      const cluster = sv.clusters(path_id);
      self.select_peaks_and_atoms(cluster);
      sv.move_to_peaks(cluster.peaks(), sv._cluster_navigation.speed, 2);
    })

    table.selectAll('tbody .atom-badge').on('click', function() {
      let selected_atoms = self.selected_atoms;
      const atom_id = d3v7.select(this).attr('data-atom-id');
      const clicked_atom = struct.atoms(atom_id);
      if (d3v7.event.shiftKey) {
        window.getSelection().removeAllRanges()
        if (selected_atoms.contains(clicked_atom)) {
          selected_atoms = selected_atoms.remove(clicked_atom);
        } else {
          selected_atoms.push(clicked_atom);
        }
      } else {
        selected_atoms = new JSV.SVSet(clicked_atom);
      }
      self.selected_atoms = selected_atoms;
      self.select_peaks_and_atoms(self.selected_atoms);
      sv.move_to_peaks(self.cluster_from_atoms(self.selected_atoms).peaks(), sv._cluster_navigation.speed, 2);
      d3v7.event.stopPropagation();
    })

    table.selectAll('tbody .peak-badge').on('click', function() {
      let selected_peaks = self.selected_peaks;
      const peak_id = d3v7.select(this).attr('data-peak-id');
      const clicked_peak = sv.peaks(peak_id);
      if (d3v7.event.shiftKey) {
        window.getSelection().removeAllRanges()
        if (selected_peaks.contains(clicked_peak)) {
          selected_peaks = selected_peaks.remove(clicked_peak);
        } else {
          selected_peaks.push(clicked_peak);
        }
      } else {
        selected_peaks = new JSV.SVSet(clicked_peak);
      }
      self.selected_peaks = selected_peaks;
      self.select_peaks_and_atoms(self.selected_peaks);
      sv.move_to_peaks(self.selected_peaks, sv._cluster_navigation.speed, 2);
      d3v7.event.stopPropagation();
    })

    // Add Atoms
    table.selectAll('tbody tr .add-atoms').on('click', function() {
      const cluster = self.cluster_from_table_child(this);
      // Add to spectra viewer
      if (cluster) {
        cluster._atoms = struct.nmr_atoms_from_atoms(struct.selected_atoms);
      }
      d3v7.event.stopPropagation();
      // Add to table cell
      self.update_table();
      self.select_peaks_and_atoms(cluster);
      return false
    });

  }

  SVAssignments.prototype.add_sv_events = function() {
    const self = this;
    const struct = self.struct;
    const sv = self.sv;
    sv.on('adjust-end.assign', function() {
      self.update_table();
      self.select_peaks_and_atoms(sv.selection.peaks());
    });
    sv.on('adjust-peak-created.assign', function(peak) {
      const cluster = peak.cluster();
      if (cluster.peaks().length == 1) {
        cluster._atoms = struct.nmr_atoms_from_atoms(struct.selected_atoms);
      }
    });
    sv.on('selection-add.assign', function() {
      self.select_peaks_and_atoms(sv.selection.peaks());
    });
    sv.on('selection-remove.assign', function() {
      self.select_peaks_and_atoms(sv.selection.peaks());
    });
    sv.on('selection-clear.assign', function() {
      self.select_peaks_and_atoms(undefined);
    });
  }

  SVAssignments.prototype.add_struct_events = function() {
    const self = this;
    const struct = this.struct;
    struct.on('structure-click.assign', function() {
      struct.selected_atoms = struct.LCA_and_nmr_atoms(struct.selected_atoms);
      self.select_peaks_and_atoms(struct.selected_atoms);
    });
  }

  // Provide a single cluster or a SVSet of peaks or atoms
  // If a single cluster or the peaks and atoms make up a single cluster
  // then atoms, peaks and the cluster row will be selected.
  // If no cluster can be selected then just the peaks or atoms are selected.
  SVAssignments.prototype.select_peaks_and_atoms = function(selection) {
    const self = this;
    const sv = this.sv;
    const struct = this.struct;
    const table = this.table;
    let cluster;
    let peaks = new JSV.SVSet();
    let atoms = new JSV.SVSet();

    // Determine what selection is
    if (selection) {
      if (selection.toString() == 'Cluster') {
        cluster = selection;
      } else if (selection.toString() == 'SVSet' || Array.isArray(selection)) {
        const first_item = selection[0];
        if (first_item) {
          if (selection[0].toString() == 'Atom') {
            atoms = selection;
          } else if (selection[0].toString() == 'Peak') {
            peaks = selection;
          }
        }
      } else {
        console.log("Unknown Selection")
      }
    }

    // Determine peaks, atoms, cluster
    if (peaks.present()) {
      cluster = this.cluster_from_peaks(peaks);
    }
    if (atoms.present()) {
      cluster = this.cluster_from_atoms(atoms);
    }
    if (cluster) {
      peaks = cluster.peaks();
      atoms = cluster.atoms();
    }

    // Select atoms in structure
    if (struct) {
      if(struct.use_jsmol && typeof Jmol !== "undefined") {
        if(struct.nmr_1H()) {
          let selected = struct.LCA_from_hydrogens(atoms);
          Jmol.script(jmol_jspectraviewer, `select carbon; colour halos grey; select atomno = ${selected[0].id.replace(/\D/g, '')}; colour halos blue;`)
        } else {
          Jmol.script(jmol_jspectraviewer, `select carbon; colour halos grey; select atomno = ${atoms[0].id.replace(/\D/g, '')}; colour halos blue;`)
        }
      }
      else {
        struct.selected_atoms = struct.LCA_and_nmr_atoms(atoms);
      }
    }
    // Select peaks in viewer
    sv.selection._elements = peaks.unique();
    sv.draw();
    // Select peaks and atoms in table
    self.selected_peaks = peaks;
    if (struct) {
      self.selected_atoms = struct.LCA_from_nmr_atoms(atoms);
    }
    // Select cluster row in table
    table.selectAll('tbody tr').classed('selected', false);
    if (cluster) {
      table.select('tbody tr[data-cluster-path-id='+cluster.path_id()+']').classed('selected', true);
    }
  }

  SVAssignments.prototype.cluster_from_peaks = function(peaks) {
    let cluster;
    this.sv.clusters().forEach(function(c) {
      if (c.peaks().equals(peaks)) {
        cluster = c;
      }
    });
    return cluster
  }

  SVAssignments.prototype.cluster_from_atoms = function(atoms) {
    let cluster;
    const struct = this.struct;
    const nmr_atoms = struct.nmr_atoms_from_atoms(atoms);
    this.sv.clusters().forEach(function(c) {
      const cluster_nmr_atoms = struct.nmr_atoms_from_atoms(c.atoms());
      if (cluster_nmr_atoms.equals(nmr_atoms)) {
        cluster = c;
      }
    });
    return cluster
  }

  // Given an element in a table row, find the cluster represented by the row.
  SVAssignments.prototype.cluster_from_table_child = function(element) {
    if (!element) { return; }
    let cluster;
    let found = false;
    let currentNode = element;
    while (!found) {
      localName = currentNode.localName.toUpperCase();
      if (localName == 'TR') {
        found = true;
        const path_id = d3v7.select(currentNode).attr('data-cluster-path-id');
        if (path_id) {
          cluster = this.sv.clusters(path_id);
        }
      } else if (localName == 'TABLE') {
        found = true
      } else {
        currentNode = currentNode.parentNode;
        if (!currentNode) {
          found = true;
        }
      }
    }
    return cluster
  }

  /////////////////////////////////////////////////////////////////////////////
  // Assignment Properties (setters/getters)
  /////////////////////////////////////////////////////////////////////////////
  Object.defineProperties(SVAssignments.prototype, {
    'selected_atoms': {
      get: function() {
        const self = this;
        const selected = new JSV.SVSet();
        this.container.selectAll('.atom-badge.selected').each(function(){
          const atom_id = d3v7.select(this).attr('data-atom-id');
          selected.push(self.struct.atoms(atom_id));
        });
        return selected
      },
      set: function(atoms) {
        const self = this;
        const container = this.container;
        container.selectAll('.atom-badge').classed('selected', false);
        atoms.forEach(function(atom) {
          container.selectAll('.atom-badge[data-atom-id="' + atom.id + '"]').classed('selected', true);
        });
      }
    },
    'selected_peaks': {
      get: function() {
        const self = this;
        const selected = new JSV.SVSet();
        this.container.selectAll('.peak-badge.selected').each(function(){
          const peak_id = d3v7.select(this).attr('data-peak-id');
          selected.push(self.sv.peaks(peak_id));
        });
        return selected
      },
      set: function(peaks) {
        const self = this;
        const container = this.container;
        container.selectAll('.peak-badge').classed('selected', false);
        peaks.forEach(function(peak) {
          container.select('.peak-badge[data-peak-id="' + peak.path_id() + '"]').classed('selected', true);
        });
      }
    },
    'edit_mode': {
      get: function() {
        return this._edit_mode;
      },
      set: function(mode) {
        const self = this;
        const sv = self.sv;
        self._edit_mode = mode;
        if (self._edit_mode) {
          sv.selection.element_type = 'peak';
          sv.selection.allow_multiselect = true;
          sv.selection.allow_adjust = true;
          sv.selection.allow_width_adjust = true;
          sv.selection.allow_peak_creation = true;
          if (sv.highlight) sv.highlight.element_type = 'peak';
        } else {
          sv.selection.element_type = 'cluster';
          sv.selection.allow_multiselect = true;
          sv.selection.allow_adjust = false;
          sv.selection.allow_width_adjust = false;
          sv.selection.allow_peak_creation = false;
          if (sv.highlight) sv.highlight.element_type = 'cluster';
        }
        self.update_table();
        self.select_peaks_and_atoms(sv.selection.peaks());
        sv.draw();
      }
    },
    'nmr_nucleus': {
      get: function() {
        return this._nmr_nucleus;
      },
      set: function(nucleus) {
        this._nmr_nucleus = nucleus;
        this.columns = this.get_columns();
        if(this.struct) this.struct.nmr_nucleus = nucleus;
        this.update_table_header();
        this.update_table();
      }
    }
  });



  JSV.SVAssignments = SVAssignments;

})(JSpectraViewer);
//////////////////////////////////////////////////////////////////////////////
// SpectraViewer Assignment Table
//////////////////////////////////////////////////////////////////////////////
// NOTE: requires Sortable to move peaks and atom badges
// https://github.com/RubaXa/Sortable
(function(JSV) {

  /**
   * The SVAssignments2D object controls peak and atom assignments of clusters on
   * the 2D Viewer. The [Sortable](https://github.com/RubaXa/Sortable) library is
   * required to move peak and atom badges.
   * The following options can be set when creating a
   * [SpectraViewer](SpectraViewer.js.html):
   *
   *  Option                | Default     | Description
   *  ----------------------|-------------------------------------------------
   *  container_id          | _undefined_ | ID of dom element where the structure will be draw (e.g. '#my-structure')
   *  edit_mode             | false       | Can the Assignments2D be manually edited?
   *
   *
   * @param {Object} sv The [SpectraViewer](SpectraViewer.js.html) object
   * @param {Object} options Options for how Assignment Table should work. Described below.
   * @return {SVSelection}
   */
  function SVAssignments2D(sv, options= {}) {
    const self = this;
    this.sv = sv;
    this.struct = sv.structure;
    this.container_id = options.container_id;
    this.container = d3v7.select(this.container_id);
    this.table = this.container.append('table').classed('jsv-assignment-table', true);
    this.nmr_nucleus = "2D";
    if (!window.Sortable) {
      console.log("Sortable needs to be installed to make peaks/atoms dragable: https://github.com/RubaXa/Sortable");
      this.sortable = false;
    }
    this.add_sv_events();
    if (this.struct) {
      this.add_struct_events();
    }
    this.edit_mode = options.edit_mode; // calls update_table
  }

  SVAssignments2D.prototype.nmr_1H = function () {
    return this.nmr_nucleus && this.nmr_nucleus.toUpperCase() == '1H'
  }

  SVAssignments2D.prototype.nmr_13C = function () {
    return this.nmr_nucleus && this.nmr_nucleus.toUpperCase() == '13C'
  }


  SVAssignments2D.prototype.get_columns = function() {
    const self = this;
    const struct = self.struct;
    const formatter = d3v7.format('.2f');

    const create_atoms_html = function(atoms, atom_refs) {
      let html = "";
      if ( atoms && atoms.length > 0) {
        const lca_atoms = struct.LCA_from_nmr_atoms(atoms);
        html = lca_atoms.map(function(atom) {
          return "<div class='jsv-badge atom-badge' data-atom-id='"+atom.id+"'>"+atom.display_id+"</div>"
        }).join('');
      } else if (atom_refs && atom_refs.length > 0) {
        html =  atom_refs.map(function(ref) {
          return "<div class='jsv-badge atom-badge' data-atom-id=''>"+ref+"</div>"
        }).join('');
      }
      return html
    }

    const create_add_atoms_html = function(atoms, index) {
      return "<div class='add-atoms-container'><a class='add-atoms jsv-btn' href='javascript:void(0)'>+</a><div>";
    }

    const create_peak_centers_html = function(peaks) {
      return peaks.reverse().map(function(peak) {
        return "<span class='jsv-badge peak-badge' data-peak-id='"+peak.path_id()+"'>"+formatter(peak.center)+"</span>"
      }).join('')
    }

    const columns = [
      { heading: 'Row No.', class: 'cluster-number center', html: function(row)  { return row.number }},
      { heading: 'Dir. Dim. Center', class: 'center', html: function(row)  { return formatter(row.peaks[0])}},
      { heading: 'Indir. Dim. Center', class: 'center', html: function(row)  { return formatter(row.peaks[1])}},
      { heading: 'Coupling Type', class: 'center', html: function(row)  { return row.multiplet_type }},
      { heading: 'Dir. Dim. Atom No.', class: 'atom-cell center', html: function(row)  { return create_atoms_html(row.atoms, row.dir_dim_atom_refs) }},
      { heading: 'Indir. Dim. Atom No.', class: 'atom-cell center', html: function(row)  { return create_atoms_html(row.atoms, row.indir_dim_atom_refs) }}
    ];
    // if (!struct) {
    //   // Remove H's, Atoms and '' columns
    //   columns.splice(4, 3);
    // }
    return columns
  }

  // Table code concept: https://vis4.net/blog/posts/making-html-tables-in-d3v7-doesnt-need-to-be-a-pain/
  SVAssignments2D.prototype.update_table_header = function() {
    this.table.select('thead').remove();
    this.table.append('thead')
        .append('tr')
        .append('th')
        .attr('class', 'center')
        .attr('style', 'font-size: larger')
        .attr('colspan', this.columns.length)
        .text("Peak Assignment Table")
    this.table.select('thead')
        .append('tr')
        .selectAll('th')
        .data(this.columns).enter()
        .append('th')
        .attr('class', function(col) { return col.class })
        .text(function(col) { return col.heading });
  }

  SVAssignments2D.prototype.update_table = function() {
    // console.log('TABLE UPDATED')
    const self = this;
    const struct = this.struct;
    const table = this.table;
    const columns = this.columns;
    // Get row data from SV
    rows = [];
    // Make the cluster navigator static
    this.sv._cluster_navigation.static_compounds = this.sv.compounds();  // _cluster_navigation is undefined

    this.sv.clusters().each(function(index) {
      row = {
        path_id: this.path_id(),
        number: index + 1,
        center: this.center(),
        peak_num: this.peaks().length,
        peaks: this.peaks(),
        multiplet_type: this.multiplet_type,
        atoms: this.atoms(),
        dir_dim_atom_refs: this.dir_dim_atom_refs(),
        indir_dim_atom_refs: this.indir_dim_atom_refs()
      };
      rows.push(row);
    });
    // Remove old data
    table.select('tbody').remove();
    // Add new data
    if (rows.length > 0) {
      table.append('tbody')
          .selectAll('tr')
          .data(rows).enter()
          .append('tr').attr('data-cluster-path-id', function(row) { return row.path_id })
          .selectAll('td')
          .data(function(row, i) {
            return columns.map(function(col) {
              const cell = {};
              Object.keys(col).forEach(function(key) {
                cell[key] = (typeof col[key] == 'function') ? col[key](row, i) : col[key];
              });
              return cell
            });
          }).enter()
          .append('td')
          .html(function(col) { return col.html })
          .attr('class', function(col) { return col.class });
    } else {
      table.append('tbody').append('tr').append('td')
          .attr('colspan', columns.length)
          .style('text-align', 'center')
          .html('No Clusters')
    }

    // Add table events
    this.add_table_events();

    // Adjust Atom Badge colors
    d3v7.selectAll('.atom-badge').each(function() {
      const badge = d3v7.select(this);
      const atom = struct.atoms(badge.text());
      if (atom) {
        badge.classed('atom-badge-symbol-'+ atom.symbol, true);
      }
    });

    if (self.edit_mode) {
      this.make_sortable();
    } else {
      // Hide add atoms button, which are visible by default
      table.selectAll('.add-atoms').style('display', 'none');
    }
  }

  SVAssignments2D.prototype.make_sortable = function() {
    const self = this;
    const table = this.table;
    const struct = this.struct;
    table.classed('editable', true);
    // Make Atoms sortable
    table.selectAll('tbody tr .atom-cell').each(function() {
      Sortable.create(this, {
        draggable: '.atom-badge', group: 'atoms', animation: 150, ghostClass: 'badge-ghost',
        onAdd: function(evt) {
          let atom;
          const new_cluster = self.cluster_from_table_child(evt.item);
          const old_cluster = self.cluster_from_table_child(evt.from);
          // console.log(old_cluster.path_id() + ' -> ' + new_cluster.path_id());
          const atom_id = d3v7.select(evt.item).attr('data-atom-id');
          if (atom_id) {
            atom = struct.atoms(atom_id);
          }
          if (atom && new_cluster && old_cluster) {
            const moved_atoms = struct.nmr_atoms_from_atoms([atom]);
            atoms = new_cluster.atoms().merge(moved_atoms).unique();
            new_cluster._atoms = atoms;
            old_cluster._atoms = new JSV.SVSet(old_cluster._atoms.filter(function(a) { return !moved_atoms.contains(a) }));
            self.update_table();
          }
          self.select_peaks_and_atoms(self.selected_atoms);
        },
        onEnd: function(evt) {
          const atom_id = d3v7.select(evt.item).attr('data-atom-id');
          if (atom_id) {
            atom = struct.atoms(atom_id);
            self.selected_atoms = new JSV.SVSet(atom);
            self.select_peaks_and_atoms(self.selected_atoms);
          }
        }
      })
    });

    // Make peaks sortable
    table.selectAll('tbody tr td.peak-cell').each(function() {
      Sortable.create(this, {
        draggable: '.peak-badge', group: 'peaks', animation: 150, ghostClass: 'badge-ghost',
        onAdd: function(evt) {
          let peak;
          const new_cluster = self.cluster_from_table_child(evt.item);
          const old_cluster = self.cluster_from_table_child(evt.from);
          // console.log(old_cluster.path_id() + ' -> ' + new_cluster.path_id());
          const peak_id = d3v7.select(evt.item).attr('data-peak-id');
          if (peak_id) {
            peak = sv.peaks(peak_id);
          }
          if (peak && new_cluster && old_cluster) {
            // old_cluster._peaks = new JSV.SVSet(old_cluster._peaks.filter(function(p) { return p != peak }));
            // Remove peak from previous cluster
            peak.delete();
            // Add peak to cluster and update
            new_cluster.add_peak(peak);
            new_cluster.update();
            if (sv._cluster_navigation) {
              sv._cluster_navigation.update_table();
            }
            self.update_table();
          }
          self.select_peaks_and_atoms(self.selected_peaks);
        },
        onEnd: function(evt) {
          const peak_id = d3v7.select(evt.item).attr('data-peak-id');
          if (peak_id) {
            peak = sv.peaks(peak_id);
            self.selected_peaks = new JSV.SVSet(peak);
            self.select_peaks_and_atoms(self.selected_peaks);
          }
        }
      })

    });
  }

  SVAssignments2D.prototype.add_table_events = function() {
    const self = this;
    const table = this.table;
    const sv = this.sv;
    const struct = this.struct;

    table.selectAll('tbody tr').on('click', function() {
      table.selectAll('tbody tr').classed('selected', false);
      d3v7.select(this).classed('selected', true);
      const path_id = d3v7.select(this).attr('data-cluster-path-id');
      const cluster = sv.clusters(path_id);
      self.select_peaks_and_atoms(cluster);
      sv.move_to_peaks(cluster.peaks(), sv._cluster_navigation.speed, 2);
    })

    table.selectAll('tbody .atom-badge').on('click', function() {
      let selected_atoms = self.selected_atoms;
      const atom_id = d3v7.select(this).attr('data-atom-id');
      const clicked_atom = struct.atoms(atom_id);
      if (d3v7.event.shiftKey) {
        window.getSelection().removeAllRanges()
        if (selected_atoms.contains(clicked_atom)) {
          selected_atoms = selected_atoms.remove(clicked_atom);
        } else {
          selected_atoms.push(clicked_atom);
        }
      } else {
        selected_atoms = new JSV.SVSet(clicked_atom);
      }
      self.selected_atoms = selected_atoms;
      self.select_peaks_and_atoms(self.selected_atoms);
      sv.move_to_peaks(self.cluster_from_atoms(self.selected_atoms).peaks(), sv._cluster_navigation.speed, 2);
      d3v7.event.stopPropagation();
    })

    table.selectAll('tbody .peak-badge').on('click', function() {
      let selected_peaks = self.selected_peaks;
      const peak_id = d3v7.select(this).attr('data-peak-id');
      const clicked_peak = sv.peaks(peak_id);
      if (d3v7.event.shiftKey) {
        window.getSelection().removeAllRanges()
        if (selected_peaks.contains(clicked_peak)) {
          selected_peaks = selected_peaks.remove(clicked_peak);
        } else {
          selected_peaks.push(clicked_peak);
        }
      } else {
        selected_peaks = new JSV.SVSet(clicked_peak);
      }
      self.selected_peaks = selected_peaks;
      self.select_peaks_and_atoms(self.selected_peaks);
      sv.move_to_peaks(self.selected_peaks, sv._cluster_navigation.speed, 2);
      d3v7.event.stopPropagation();
    })

    // Add Atoms
    table.selectAll('tbody tr .add-atoms').on('click', function() {
      const cluster = self.cluster_from_table_child(this);
      // Add to spectra viewer
      if (cluster) {
        cluster._atoms = struct.nmr_atoms_from_atoms(struct.selected_atoms);
      }
      d3v7.event.stopPropagation();
      // Add to table cell
      self.update_table();
      self.select_peaks_and_atoms(cluster);
      return false
    });

  }

  SVAssignments2D.prototype.add_sv_events = function() {
    const self = this;
    const struct = self.struct;
    const sv = self.sv;
    sv.on('adjust-end.assign', function() {
      self.update_table();
      self.select_peaks_and_atoms(sv.selection.peaks());
    });
    sv.on('adjust-peak-created.assign', function(peak) {
      const cluster = peak.cluster();
      if (cluster.peaks().length == 1) {
        cluster._atoms = struct.nmr_atoms_from_atoms(struct.selected_atoms);
      }
    });
    sv.on('selection-add.assign', function() {
      self.select_peaks_and_atoms(sv.selection.peaks());
    });
    sv.on('selection-remove.assign', function() {
      self.select_peaks_and_atoms(sv.selection.peaks());
    });
    sv.on('selection-clear.assign', function() {
      self.select_peaks_and_atoms(undefined);
    });
  }

  SVAssignments2D.prototype.add_struct_events = function() {
    const self = this;
    const struct = this.struct;
    struct.on('structure-click.assign', function() {
      struct.selected_atoms = struct.LCA_and_nmr_atoms(struct.selected_atoms);
      self.select_peaks_and_atoms(struct.selected_atoms);
    });
  }

  // Provide a single cluster or a SVSet of peaks or atoms
  // If a single cluster or the peaks and atoms make up a single cluster
  // then atoms, peaks and the cluster row will be selected.
  // If no cluster can be selected then just the peaks or atoms are selected.
  SVAssignments2D.prototype.select_peaks_and_atoms = function(selection) {
    const self = this;
    const sv = this.sv;
    const struct = this.struct;
    const table = this.table;
    let cluster;
    let peaks = new JSV.SVSet();
    let atoms = new JSV.SVSet();

    // Determine what selection is
    if (selection) {
      if (selection.toString() == 'Cluster') {
        cluster = selection;
      } else if (selection.toString() == 'SVSet' || Array.isArray(selection)) {
        const first_item = selection[0];
        if (first_item) {
          if (selection[0].toString() == 'Atom') {
            atoms = selection;
          } else if (selection[0].toString() == 'Peak') {
            peaks = selection;
          }
        }
      } else {
        console.log("Unknown Selection")
      }
    }

    // Determine peaks, atoms, cluster
    if (peaks.present()) {
      cluster = this.cluster_from_peaks(peaks);
    }
    if (atoms.present()) {
      cluster = this.cluster_from_atoms(atoms);
    }
    if (cluster) {
      peaks = cluster.peaks();
      atoms = cluster.atoms();
    }

    // Select atoms in structure
    if (struct) {
      if(struct.use_jsmol && typeof Jmol !== "undefined") {
        if(struct.nmr_1H()) {
          let selected = struct.LCA_from_hydrogens(atoms);
          Jmol.script(jmol_jspectraviewer, `select carbon; colour halos grey; select atomno = ${selected[0].id.replace(/\D/g, '')}; colour halos blue;`)
        } else {
          Jmol.script(jmol_jspectraviewer, `select carbon; colour halos grey; select atomno = ${atoms[0].id.replace(/\D/g, '')}; colour halos blue;`)
        }
      }
      else {
        struct.selected_atoms = struct.LCA_and_nmr_atoms(atoms);
      }
    }
    // Select peaks in viewer
    sv.selection._elements = peaks.unique();
    sv.draw();
    // Select peaks and atoms in table
    self.selected_peaks = peaks;
    if (struct) {
      self.selected_atoms = struct.LCA_from_nmr_atoms(atoms);
    }
    // Select cluster row in table
    table.selectAll('tbody tr').classed('selected', false);
    if (cluster) {
      table.select('tbody tr[data-cluster-path-id='+cluster.path_id()+']').classed('selected', true);
    }
  }

  SVAssignments2D.prototype.cluster_from_peaks = function(peaks) {
    let cluster;
    this.sv.clusters().forEach(function(c) {
      if (c.peaks().equals(peaks)) {
        cluster = c;
      }
    });
    return cluster
  }

  SVAssignments2D.prototype.cluster_from_atoms = function(atoms) {
    let cluster;
    const struct = this.struct;
    const nmr_atoms = struct.nmr_atoms_from_atoms(atoms);
    this.sv.clusters().forEach(function(c) {
      const cluster_nmr_atoms = struct.nmr_atoms_from_atoms(c.atoms());
      if (cluster_nmr_atoms.equals(nmr_atoms)) {
        cluster = c;
      }
    });
    return cluster
  }

  // Given an element in a table row, find the cluster represented by the row.
  SVAssignments2D.prototype.cluster_from_table_child = function(element) {
    if (!element) { return; }
    let cluster;
    let found = false;
    let currentNode = element;
    while (!found) {
      localName = currentNode.localName.toUpperCase();
      if (localName == 'TR') {
        found = true;
        const path_id = d3v7.select(currentNode).attr('data-cluster-path-id');
        if (path_id) {
          cluster = this.sv.clusters(path_id);
        }
      } else if (localName == 'TABLE') {
        found = true
      } else {
        currentNode = currentNode.parentNode;
        if (!currentNode) {
          found = true;
        }
      }
    }
    return cluster
  }

  /////////////////////////////////////////////////////////////////////////////
  // Assignment Properties (setters/getters)
  /////////////////////////////////////////////////////////////////////////////
  Object.defineProperties(SVAssignments2D.prototype, {
    'selected_atoms': {
      get: function() {
        const self = this;
        const selected = new JSV.SVSet();
        this.container.selectAll('.atom-badge.selected').each(function(){
          const atom_id = d3v7.select(this).attr('data-atom-id');
          selected.push(self.struct.atoms(atom_id));
        });
        return selected
      },
      set: function(atoms) {
        const self = this;
        const container = this.container;
        container.selectAll('.atom-badge').classed('selected', false);
        atoms.forEach(function(atom) {
          container.selectAll('.atom-badge[data-atom-id="' + atom.id + '"]').classed('selected', true);
        });
      }
    },
    'selected_peaks': {
      get: function() {
        const self = this;
        const selected = new JSV.SVSet();
        this.container.selectAll('.peak-badge.selected').each(function(){
          const peak_id = d3v7.select(this).attr('data-peak-id');
          selected.push(self.sv.peaks(peak_id));
        });
        return selected
      },
      set: function(peaks) {
        const self = this;
        const container = this.container;
        container.selectAll('.peak-badge').classed('selected', false);
        peaks.forEach(function(peak) {
          container.select('.peak-badge[data-peak-id="' + peak.path_id() + '"]').classed('selected', true);
        });
      }
    },
    'edit_mode': {
      get: function() {
        return this._edit_mode;
      },
      set: function(mode) {
        const self = this;
        const sv = self.sv;
        self._edit_mode = mode;
        if (self._edit_mode) {
          sv.selection.element_type = 'peak';
          sv.selection.allow_multiselect = true;
          sv.selection.allow_adjust = true;
          sv.selection.allow_width_adjust = true;
          sv.selection.allow_peak_creation = true;
          if (sv.highlight) sv.highlight.element_type = 'peak';
        } else {
          sv.selection.element_type = 'cluster';
          sv.selection.allow_multiselect = true;
          sv.selection.allow_adjust = false;
          sv.selection.allow_width_adjust = false;
          sv.selection.allow_peak_creation = false;
          if (sv.highlight) sv.highlight.element_type = 'cluster';
        }
        self.update_table();
        // self.select_peaks_and_atoms(sv.selection.peaks());
        sv.draw();
      }
    },
    'nmr_nucleus': {
      get: function() {
        return this._nmr_nucleus;
      },
      set: function(nucleus) {
        this._nmr_nucleus = nucleus;
        this.columns = this.get_columns();
        if(this.struct) this.struct.nmr_nucleus = nucleus;
        this.update_table_header();
        this.update_table();
      }
    }
  });



  JSV.SVAssignments2D = SVAssignments2D;

})(JSpectraViewer);
JSpectraViewer.initialize();
