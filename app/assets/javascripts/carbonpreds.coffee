# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/
$ ->
  $('.carbon-preds-example-loader').click (evt) ->
    # get smiles
    smiles = $(this).data("smiles")

    # find old iframe
    old = document.getElementsByTagName("iframe")[0];

    # load new marvin and set it up to update the hidden field
    load_marvin_instance("#moldbi-new-draw").then (sketcherInstance) ->
      sketcherInstance.on 'molchange', ->
        sketcherInstance.isEmpty().then ((empty_canvas) ->
          if empty_canvas == true
            $('#moldbi-structure-input').text('').trigger('change')
          else
            sketcherInstance.exportStructure('smiles').then ((source) ->
              $('#moldbi-structure-input').text(source).trigger('change')
            ), (error) ->
              alert "Molecule export failed:" + error
        ), (error) ->
          alert "Empty canvas check failed:" + error
      sketcherInstance.importStructure("smiles", smiles)
      # delete the old iframe
      old.remove()

    # Set the form params
    $('#carbonpred_nucleus').val("13C").change();
    $('#carbonpred_chemical_shift_reference').val("TMS").change()

$ ->
  if $('#new_carbonpred').length
    # Make sure the form will not submit
    $('#moldbi-structure-input').prop('required', true);

    # flash an alert for the structure input
    $('[type="submit"]').click (evt) ->
      if !$('#moldbi-structure-input').val()
        alert("Please add a structure for prediction.")