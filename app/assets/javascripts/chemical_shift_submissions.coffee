# Place all the behaviors and hooks related to the matching controller here.
# All this logic will automatically be available in application.js.
# You can use CoffeeScript in this file: http://coffeescript.org/

# Make sure the form will not submit
$ ->
  if $('#new_chemical_shift_submission').length
    $('#moldbi-structure-input').prop('required', true);

    # flash an alert for the structure input
    $('[type="submit"]').click (evt) ->
      if !$('#moldbi-structure-input').val()
        alert("Please add a structure for deposition.")