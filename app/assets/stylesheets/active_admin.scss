// SASS variable overrides must be declared before loading up Active Admin's styles.
//
// To view the variables that Active Admin provides, take a look at
// `app/assets/stylesheets/active_admin/mixins/_variables.scss` in the
// Active Admin source.
//
// For example, to change the sidebar width:
// $sidebar-width: 242px;

// Active Admin's got SASS!
@import "select2";
@import "active_admin/mixins";
@import "active_admin/base";
@import "wigu/active_admin_theme";
@import 'moldbi/moldbi';

// Overriding any non-variable SASS must be done after the fact.
// For example, to change the default status-tag color:
//
//   .status_tag { background: #6090DB; }

.formatted-select {
  height: 30px;
  -webkit-appearance: menulist-button;
}

.formatted-select-wide {
  height: 30px;
  -webkit-appearance: menulist-button;
  width: 800px;
}

.hidden {
  display: none;
}

.structure-editor-container {
  padding-bottom: 10px;
  .moldbi-marvin4js {
    height: 400px;
  }
  img.chemaxon-logo {
    height: 2rem;
    float: right;
  }
}

.padded-inputs {
  li {
    padding: 5px 0px !important;
  }
  li.boolean {
    label {
      top: -5px;
      position: relative;
    }
  }
}

#moldbi-sketch {
  width: 600px;
  height: 400px;
  border: 1px solid #e6e9ee;
  border-radius: 3px;
  padding: 4px;
}

#footer {
  display: none;
}
