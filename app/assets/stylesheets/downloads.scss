.downloads {
  table {
    th.data-set-col { width: 40%; }
    th.release-col { width: 20%; }
    th.download-col { width: 20%; }
    th.size-col { width: 10%; }
  }
}
.download-list li {
  height: 25px;
}

.download-list {
  list-style-type: none;
  padding: 0;
}

.alert-citation {
  margin-top: 2em;
  background: #515151;
  color: white;
  a { color: white; text-decoration: underline;}

  blockquote.primary-citation {
    font-size: 15px;
    border-left: 5px solid silver;
    margin-bottom: 0;
    padding-bottom: 0;
    margin-top: 0;
    padding-top: 0;
    a { color: white; text-decoration: underline;}
  }
}

//.alert-underconstruction {
//  margin-top: 2em;
//  //background: #8EE4AF;
//  font-size: 19px;
//  color: white;
//  //alert {background: #8EE4AF;}
//}

.alert-underconstruction {
  margin-top: 2em;
  background: #515151;
  color: white;
  font-size: 14px;
  a { color: white; text-decoration: underline;}

  blockquote.primary-citation {
    font-size: 15px;
    border-left: 5px solid silver;
    margin-bottom: 0;
    padding-bottom: 0;
    margin-top: 0;
    padding-top: 0;
    a { color: white; text-decoration: underline;}
  }
}