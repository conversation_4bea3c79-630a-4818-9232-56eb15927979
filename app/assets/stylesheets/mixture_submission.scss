/*
// Filename: mixture_submissions.scss
// Description: Contains all the CSS classes to format the mixture submission deposition
// 
// Public Classes:
//    - tooltip-format
//    - tolltip-format-arrow
//    - box-format
//    - box-header
//
// Author: <PERSON>   Creation Date: 2022/05/12
// Changes:
//
*/

// Class to format the inside of each tooltip
.tooltip-format + .tooltip > .tooltip-inner {
    background-color: white;
    color: black;
    font-weight: bold;
    border-radius: 5px;
    padding: 5px;
    border-style: outset;
    font-size: 15px;
    text-align: left;
}

// Remove the arrows from the tooltip
.tooltip-format-arrow + .tooltip > .tooltip-arrow {
    visibility: hidden;
}

// Class to format each box in the mixture deposition submission form
.box-format {
    width: 100%;
    height: 0%;
    font-size: 18px;
    margin-left: 0px;
}

.box-header {
    margin-left: 10px;
    width: auto;
    font-weight: bold;
    padding-right: 5px;
}

table.table-upload {
    max-width: 0%;
}

.cell-header {
    background-color: #8EE4AF;
    text-align: left;
    padding: 5px;
}

.cell-border {
    border: 1px solid black;
    & tr {
        border: 1px solid black;
      }
    & th {
        border: 1px solid black;
      }
    & td {
        border: 1px solid black;
      }
}

.submission-border {
    border: 1px solid lightgrey;
    & tr {
        border: 1px solid lightgrey;
    }
    & th {
        border: 1px solid lightgrey;
    }
    & td {
        border: 1px solid lightgrey;
    }
}

.cell-body {
    & .white-col {
        background-color: white;
        padding: 5px;
      }
    
    & input[type="text"] {
        text-align: left;
        width: 100%;
      }  
}

#drop_zone.active {
    background-color: #e9ecef !important; /* Light grey background when dragging a file over */
    border-color: green !important; 
}