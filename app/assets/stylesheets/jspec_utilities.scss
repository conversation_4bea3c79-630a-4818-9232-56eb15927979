#file-selector {
    box-shadow: inset 0 0.2rem 1rem rgba(#000, .25);
}

.btn-npmrd {
    background-color: #5cdb95;
    color: #05386b;
}
.bg-header-npmrd {
    background-color: #8ee4af;
}
.feedback {
    color: #2f5c87;
    background-color: #5cdb95;
    display: inline-block;
    padding-left: 0.5rem;
}
.w-100 { width: 100% !important; }
.w-75 { width: 75% !important; }
.w-50 { width: 50% !important; }
.w-25 { width: 25% !important; }
.h-100 { height: 100% !important; }
.h-75 { height: 75% !important; }
.h-50 { height: 50% !important; }
.h-25 { height: 25% !important; }

.border-0 { border: 0 !important; }
.border { border: 1px solid #ccc !important; }
.border-dashed { border: 3px dashed #8ee4af !important; }

.rounded { border-radius: 0.25rem !important; }
.rounded-1 { border-radius: 0.25rem !important; }
.rounded-2 { border-radius: 0.5rem !important; }
.rounded-3 { border-radius: 1rem !important; }

.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.px-1 { padding-left: 0.25rem !important; padding-right: 0.25rem !important; }
.px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
.px-3 { padding-left: 1rem !important; padding-right: 1rem !important; }

.gap-1 { gap: 0.25rem !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 1rem !important; }
.me-1 { margin-right: 0.25rem !important; }
.me-2 { margin-right: 0.5rem !important; }
.me-3 { margin-right: 1rem !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }

.d-flex { display: flex !important; }
.flex-column { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-around { justify-content: space-around !important; }
.align-items-center { align-items: center !important; }
.align-items-start { align-items: start !important; }
.align-items-end { align-items: end !important; }
.align-items-baseline { align-items: baseline !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }



