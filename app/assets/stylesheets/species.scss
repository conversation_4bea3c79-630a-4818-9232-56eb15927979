table.table.species-phylogeny-table {
  th{
    font-size: larger;
    text-align: center;
  }
  text-align: center;
}

.species-browse-name {
  font-size: large;
}

table.table.table-striped.table-condensed.species-table {
  th{
    font-size: large;
  }

  td.natural-product-link {
    width: 15%;
    vertical-align: middle;

    .btn {
      margin-bottom: 1em;
    }

    .cas {
      font-size: 12px;
      margin-top: 0.6em;
      margin-left: 1.4em;
    }
  }

  td.natural-product-structure {
    width: 20%;
  }


  td.natural-product-name {
    vertical-align: middle;
    width: 50%
  }

  td.natural-product-formula {
    vertical-align: middle;
    width: 15%
  }


  .tbody {
    td {
      vertical-align: middle;
    }
  }
}

.ui-autocomplete {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  float: left;
  display: none;
  min-width: 160px;
  _width: 160px;
  padding: 4px 0;
  margin: 2px 0 0 0;
  list-style: none;
  background-color: #ffffff;
  border-color: #ccc;
  border-color: rgba(0, 0, 0, 0.2);
  border-style: solid;
  border-width: 1px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  border-radius: 5px;
  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  -webkit-background-clip: padding-box;
  -moz-background-clip: padding;
  background-clip: padding-box;
  *border-right-width: 2px;
  *border-bottom-width: 2px;

  .ui-menu-item > a.ui-corner-all {
    display: block;
    padding: 3px 15px;
    clear: both;
    font-weight: normal;
    line-height: 18px;
    color: #555555;
    white-space: nowrap;
  }
}
.ui-widget-content .ui-state-focus {
  color: #ffffff;
  text-decoration: none;
  background-color: #0088cc;
  border-radius: 0px;
  -webkit-border-radius: 0px;
  -moz-border-radius: 0px;
  background-image: none;
}

.ui-helper-hidden-accessible {
  display: none;
}