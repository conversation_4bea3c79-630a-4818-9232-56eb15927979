.intro {
  // Allows the height of the div to be calculated 
  // by the height of the float elements
  overflow: hidden;
  font-size: 17px;
}

.intro-top {
  // Allows the height of the div to be calculated 
  // by the height of the float elements
  overflow: hidden;
  font-size: 17px;
  float:left;
  width: 65%;

}

.add {
  // Allows the height of the div to be calculated 
  // by the height of the float elements
  overflow: hidden;
  font-size: 15px;
  float:right;
  width: 30%;
  text-align:center;
}

.center-header {
  text-align:center;
}

.left-text {
  display: inline-block;
  text-align: left;
}

.home-spectra {
  float:right;
  margin: 0 0 10px 10px;
  border: 1px solid grey;
}

.home-add {
  display: block;
  margin-left: auto;
  margin-right: auto;
  border: 1px solid grey;
}

.intro h2 {
  margin-top: 10px;
}

.btn-container {
  width: 100%;
  text-align: center;
}

.btn-spaced {
  margin: 0 5px;
}

.form-help {
  color: #737373;
  display: inline;
}

.form-label {
  clear: both;
}

.form-input {
  clear: both;
  padding-left: 25px;
  margin: 5px 0 15px 0;
  font-size: 20px;
}

label {
  font-size: 20px;
}
input {
  margin-left: 1%;
  font-size: 20px;
}
.form-list li {
  clear: both;
  margin-bottom: 15px;
}

.form-list label {
  padding: 0 15px 0 0;
}

.radio-label {
  margin: 0 10px 0 5px;
  font-size: smaller;
}

.radio-div-fixed {
  float: left;
  display: inline;
  width: 180px;
}

.radio-div {
  float: left;
  display: inline;
}

// .radio-div-last {
//   float: left;
//   display: inline;
// }

.paper-data-link {
  float: left;
  width: 200px;
}

.button-clear {
  clear: both;
  margin-bottom: 20px;
}

.reset-button {
  float: right;
}

.example-button {
  float: left;
  width: 120px;
  text-align: center;
}

.example-button p {
  font-size: 12px;
  white-space: pre-line;
}

.submit-button {
  float: left;
  // margin-left: 40px;
}

// .submit-or-run {
//   float: left;
//   margin: 0 20px;
//   font-size: 12px;
//   padding-top: 8px;
//   font-style: italic;
//   font-weight: bold;
// }

.example-section {
  margin: 20px 0 15px 0;
}

.float-text {
  float: left;
}

.clear {
  clear: both;
}

#current-stage-title {
  text-align: center;
}

.quantities-table .number {
  text-align: right;
}

.quantities-table .below-threshold {
  font-style: italic;
  color: grey;
}

.quantities-table tr.show-compound td {
  background-color: rgb(250, 200, 200) !important;
}

// .quantities-table .center {
//   text-align: center;
// }

.quantities-table {
  cursor: pointer;
}

.quantities-table th, .filter-list th {
  text-align: center;
}

.quantities-section {
  margin: 25px 0;
}

.dataTables_wrapper {
  clear: both;
}

.download-csv {
  float: right;
  margin-bottom: 5px;
}

.download-nmrml {
  float: right;
  margin-bottom: 5px;
}

.tab-pane {
  margin: 15px 0;
}

.filter-list-wrapper .dataTables_filter input {
  width: 10em;
}

// .filter-list .sorting {
//   background-position: center left !important;
// }

// Hack to get sort icons closer to the Header
// the dataTable must have '"bJQueryUI": true' in order to get
// the required markup
.ui-icon-triangle-1-s, .ui-icon-triangle-1-n, .ui-icon-carat-2-n-s {
  width: 20px;
  height: 20px;
  vertical-align: -20%;
  display: inline-block;
  background-position: center;
  background-repeat: no-repeat;
}
.ui-icon-triangle-1-n {
  background-image: image-url('dataTables/sort_asc.png')
}
.ui-icon-triangle-1-s {
  background-image: image-url('dataTables/sort_desc.png')
}
.ui-icon-carat-2-n-s {
  background-image: image-url('dataTables/sort_both.png')
}

.reference-library-wrapper {
  // width: 600px;
}

// table.filter-list.table {
//   width: 100% !important;

// }

// Reduce padding and margin to free up space under alert
#custom-filter-list .alert {
  padding: 10px;
  margin-bottom: 10px;
}

#custom-filter-list .status-experimental {
  font-style: italic;
  color: grey;
}

#custom-filter-list .subset-not-verified {
  font-size: smaller;
  color: grey;
}

// .methods {
//   padding-left: 19px;
// }

.table-title {
  font-weight: bold;
  margin: 20px 0 5px 0;
}

.footnote-number {
  float: left;
  clear: both;
  width: 2%;
}

.footnote-text {
  float: left;
  margin-bottom: 10px;
  width: 98%;
}

.method-image {
  margin: 15px 0;
}

.glyphicon-new-window {
  color: silver;
  font-size: smaller;
}

// Center the spectra viewer
div#spectra-viewer {
  margin-left: auto;
  margin-right: auto;
}

#spectra-container {
  background-color: white;
  padding-top: 5px;
}

div#spectra-container.sticky {
  width: 100%;
  position: fixed;
  top: 50px;
  left: 0;
  z-index: 10;
}

#spectra-container hr {
  margin: 0;
  border-top: 0px rgb(200,200,200) solid;
}

hr.drop-shadow {
  box-shadow: 0 3px 3px 3px rgba(150,150,150,0.2);
}

#spectra-resize-bar {
  margin-top: 5px;
  height: 10px;
  border-top: 1px solid rgb(180,180,180);
  border-bottom: 1px solid rgb(180,180,180);
  background-color: rgb(245,245,245);
  cursor: ns-resize;
}

img.drag-icon {
  display: block;
  margin-top: 1px;
  margin-left: auto;
  margin-right: auto;
}

#spectra-controls {
  // margin-top: 5px;
  margin-left: auto;
  margin-right: auto;
}

#spectra-controls a {
  margin-right: 5px;
}

.help.tool-tip {
  color: grey;
}

.table-center th,
.table-center td {
  text-align: center;
}

.well-version {
  padding: 7px;
}

.version-container {
  text-align: center;
}

.version {
  display: inline-block;
  padding-right: 20px;
  padding-left: 20px;
  font-family: monaco, Consolas, 'Lucida Console', monospace;
  font-size: 14px;
}

.important-message {
  padding: 15px;
  font-weight: bold;
  background-color: #FFE066;
  border: 1px dashed #FF9900;
  margin: 5px 5px 10px 5px;
  border-radius: 5px;
  font-size: 16px;
  a {
    color: #333;
    font-style: italic;
    // text-decoration: underline;
    &:hover {
      color: #555;
    }
  }
}

// top should be the same height as the navbar
.anchor {
  display: block;
  position: relative;
  top: -50px;
  visibility: hidden;
}

.form-wrapper {
  font-size: 17px;
}

.pending_status {
   color: red;
}

.accepted_status {
  color: green;
}

.validation_message {
  color: red;
}

.label-font-size {
  font-size: 20px;
  word-wrap: break-word;
}
.nav-link.disabled {
  color: #6c757d;
  pointer-events: none;
  cursor: default;
}
#savenmrfile-details.modal {
  margin-top: 60px;
}


.btn-savenmrfile-details {
  @extend .btn;
  @extend .btn-xs;
  @extend .btn-default;
  float: right;
}

.nmr-file-card {
 
  padding: 20px;
  
}

.float-right {
  float: right;
}

.btn-outline-danger {
  background-color: #FFFFFF;
  border-radius: 5px;
  border: 2px solid #DC3545;
  color: #DC3545;
  padding: 2px 6px;
}
.btn-outline-danger:hover {
  background-color: #DC3545;
  color: #FFFFFF;
}

table.padded {
  & td {
    padding: 5px;
  }

  & p {
    margin: 0;
  }
}


.chemical-shifts-alert-radio {
  padding-top: 5px;

  & input[type="radio"] {
    margin-left: 10px;
    margin-right: 40px;
  }
}

.grid-container {
  display: grid;
  grid-template-columns: minmax(500px, 30%) 1fr;
  grid-column-gap: 20px;
}

.grid-item-table {
  overflow-x: auto; // allow horizontal scrolling of responsive table
}

table.chemical-shifts {
  width: 100%;

  & th {
    background-color: #8EE4AF;
    text-align: center;
    padding: 5px;
  }

  & .grey-col {
    text-align: center;
    background-color: #D3D3D3;
  }

  & .white-col {
    background-color: white;
  }

  & input[type="text"] {
    margin-left: 0;
    width: 100%;
  }
}

.chemical-shifts > tbody > tr > td, .chemical-shifts > tbody > tr > th, .chemical-shifts > tfoot > tr > td, .chemical-shifts > tfoot > tr > th, .chemical-shifts > thead > tr > td, .chemical-shifts > thead > tr > th {
  border: 1px solid black;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 150px; // encompass for taller navbar (shorter screens)
  min-width: 500px; // ensure image is large enough to see
  max-width: 29vw; // 1 less than 30 to contain within grid (set at 30%)
}

.hide-overflow {
  overflow: hidden;
}

.atom-col-head {
  text-align: center;
  vertical-align: top;
}
.atom-col-head-verification {
  vertical-align: top;
}
//p {
  //width: 200px;
  //border: 1px solid;
  //width: 200px;

  /* BOTH of the following are required for text-overflow */
  //white-space: nowrap;
  //overflow: hidden;
//}
//.overflow-ellipsis {
  //text-overflow: ellipsis;
//}
.select-table{
  width: 90px;
  overflow: hidden;
  white-space:nowrap; 
  text-overflow: ellipsis;
}
.select-wrapper {
  //position: relative;
  &::after {
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 100%;
    content: "";
    background: linear-gradient(to right, transparent, white);
    pointer-events: none;
  }
}
.regular {
  display: inline-block;
  margin: 10px 0 0;
  .select {
    color: #000;
  }
}

// Class to remove the spin box in number fields
.no-spin::-webkit-inner-spin-button, .no-spin::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
  margin: 0 !important;
}

.no-spin {
  -moz-appearance:textfield !important;
}

//// Fix an issue where numbering modal cannot scroll
.modal { overflow: auto !important; }

// for div to align renumbering button with next button
#renumber_buttons_div {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.hide-row {
  display: none;
}

.tooltip-inner {
  max-width: 350px;
  min-width: 200px;
  /* If max-width does not work, try using width instead */
  width: 350px;
}