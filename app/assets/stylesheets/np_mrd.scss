body {
  position: relative;
}
main {
  margin-top: 61px;
}

#load-screen {
  position: fixed;
  top: 0;
  left: 0;
  background: rgba(0,0,0,0.5);
  z-index: 5;
  width: 100%;
  height: 100%;
  text-align: center;
  display: none;
  img.loader {
    margin-top: 100px;
    margin-left: auto;
    margin-right: auto;
    width: 200px;
  }
}

.navbar-default {
  background-color: $np-mrd_primary_green;
  border-color: transparent;
  font-family: 'Maven Pro', sans-serif;
  font-size: 16px;
}
.navbar-default .navbar-brand {
  color: $np-mrd_primary_blue;
}
.navbar-default .navbar-brand:hover, .navbar-default .navbar-brand:focus {
  color: $np-mrd_primary_beige;
}
.navbar-default .navbar-text {
  color: #ffffff;
}
.navbar-default .navbar-nav > li > a {
  color: $np-mrd_primary_blue;
}
.primary-blue {
  color: $np-mrd_primary_blue;
}
.navbar-default .navbar-nav {
  margin-left: 20px;
}

.navbar-default .navbar-nav > li > a:hover, .navbar-default .navbar-nav > li > a:focus {
  color: $np-mrd_primary_beige;
}
.navbar-default .navbar-nav > .active > a, .navbar-default .navbar-nav > .active > a:hover, .navbar-default .navbar-nav > .active > a:focus {
  color: $np-mrd_primary_green;
  background-color: $np-mrd_primary_beige;
}
.navbar-default .navbar-nav > .open > a, .navbar-default .navbar-nav > .open > a:hover, .navbar-default .navbar-nav > .open > a:focus {
  color: $np-mrd_primary_green;
  background-color: $np-mrd_primary_beige;
}
.navbar-default .navbar-toggle {
  border-color: $np-mrd_primary_beige;
}
.navbar-default .navbar-toggle:hover, .navbar-default .navbar-toggle:focus {
  background-color: $np-mrd_primary_beige;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: #ffffff;
}
.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
  border-color: #ffffff;
}
.navbar-default .navbar-link {
  color: #ffffff;
}
.navbar-default .navbar-link:hover {
  color: $np-mrd_primary_green;
}

@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #ffffff;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: $np-mrd_primary_green;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover, .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: $np-mrd_primary_green;
    background-color: $np-mrd_primary_beige;
  }
}

.navbar-brand {
  font-size: 28px !important;
  float: left;
  display: block;
  height: 48px;
  font-weight: bold;
  padding: 15px 5px;
  margin-left: 0px !important;
}

.navbar-icon {
  float: left;
  display: block;
  margin: 10px 0px 10px 0px;
  background-image: image-url('np-mrd_icon_blue.svg');
  background-size: 30px 30px;
  background-repeat: no-repeat;
  height: 30px;
  width: 40px;
  &:hover {
    background-image: image-url('np-mrd_icon_white.svg');
  }
}

// Collapse the navbar before it goes to two lines
@media (max-width: 1009px) {
  .navbar-header {
      float: none;
      margin-right: 0 !important;
      margin-left: 0 !important;
  }
  .navbar-toggle {
      display: block;
  }
  .navbar-collapse.collapse:not(.in) {
      display: none !important;
  }
  .navbar-collapse {
      border-top: 1px solid transparent;
      box-shadow: inset 0 1px 0 rgba(255,255,255,0.1);
  }
  .navbar-nav {
      float: none !important;
      margin: 7.5px -15px;
  }
  .navbar-nav>li {
      float: none;
  }
  .navbar-nav>li>a {
      padding-top: 10px;
      padding-bottom: 10px;
  }
}

.navbar-form {
  .btn-search, select {
    margin-left: .3em;
  }
}

// Fix for navbar input-group width being 100%
// https://github.com/twbs/bootstrap/issues/9950
.navbar-form .input-group-btn,
.navbar-form .input-group-addon {
  width: auto;
}

// Images used for sorting tables
.sort-link {
  white-space: nowrap;
  .img-sort-link {
    margin-left: 0.2em;
  }
}

.home-intro {
  @extend .well;
  overflow: hidden;
}

blockquote.references {
  font-size: 13px;
}

.btn-default, .wishart-feedback-link > a {
  color: white !important;
  background-color: $np-mrd_primary_between !important;
  border: 1px solid $np-mrd_primary_green !important;
  &:hover {
    color: white !important;
    background-color: $np-mrd_primary_green !important;
    border: 1px solid $np-mrd_primary_green !important;
  }
}

.wishart-feedback-link > a {
  padding: 5px;
  border-radius: 5px;
}

.btn-primary, .btn-card {
  color: white;
  background-color: $np-mrd_primary_green;
  border: 1px solid $np-mrd_primary_green;
  &:hover {
    color: white;
    background-color: $np-mrd_primary_green;
    border: 1px solid $np-mrd_primary_green;
  }
}

.btn-icon {
  color: $np-mrd_primary_between;
  &:hover {
    color: $np-mrd_primary_green;
  }
  font-size: 2em;
}

.page-header {
  border-bottom: 1px solid $np-mrd_primary_green;
}

.pagination > li > a,
.pagination > li > span {
    color: $np-mrd_primary_between;
}

.pagination > li > a:hover, .pagination > li > a:focus,
.pagination > li > span:hover,
.pagination > li > span:focus {
    color: $np-mrd_primary_green;
    background-color: $np-mrd_primary_blue_light;
}

.pagination > .active > a,
.pagination > .active > a:hover, .pagination > .active > a:focus,
.pagination > .active > span,
.pagination > .active > span:hover,
.pagination > .active > span:focus {
    background-color: $np-mrd_primary_between;
    border-color: $np-mrd_primary_between;
}

.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
    color: $np-mrd_primary_green;
}

.content-table > tbody > tr > th.divider {
  background: $np-mrd_primary_green;
  color: #FFFFFF;
  font-weight: normal;
  text-shadow: none;
}

footer {
  hr {
    border-top: 1px solid $np-mrd_primary_green;
  }
}

.sidenav {
  .affix {
    top: 60px;
  }
}

@media (max-width: $screen-xs-max) {
  .sidenav .affix {
    position: static;
  }
}

#flash_notice {
  padding: 15px;
  background-color: #FFF3CD;
  font-weight: bold;
  font-size: 16px;
  color: #907116;

  & .message {
    font-size: 20px;
  }
}


// Center the spectra viewer
div#spectra-viewer {
  margin-left: auto;
  margin-right: auto;
}

#spectra-container {
  background-color: white;
  padding-top: 5px;
}

div#spectra-container.sticky {
  width: 100%;
  position: fixed;
  top: 50px;
  left: 0;
  z-index: 10;
}

#spectra-container hr {
  margin: 0;
  border-top: 0px rgb(200,200,200) solid;
}

.quality_img{
  padding: 1ex 0 1ex ;
  float: right;
  overflow: auto;
}
.quality_sticky{
  width: 100%;
  height: auto;
}

.table-head-reposition{
  display: block;
  position: relative;
  top: -60px;
  visibility: hidden;
  height: 2px;
}

#reset-div > p, #reset-div > #back-link {
  margin-left: 18px;
}