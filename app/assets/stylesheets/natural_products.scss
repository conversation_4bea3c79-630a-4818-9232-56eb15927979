// Natural Products index
table.natural-products {
  tbody {
    td { vertical-align: middle; }
  }
  td.natural-product-link {
    text-align: center;
    vertical-align: middle;
    .btn {
      margin-bottom: 1em;
    }
    .cas {
      font-size: 12px;
      margin-top: 0.6em;
    }
  }
  td.natural-product-name {
    word-break: break-all;
  }
  td.natural-product-inchi {
    width: 40px;
    word-wrap:break-word;
  }
  td.natural-product-structure {
    img {
      @extend .thumbnail !optional;
      margin-bottom: 0;
      width: 75%;
      height: auto;}
  }
}

table.depositions {
  thead {
    th { width: 12.5%; }
  }
}

.hidden-list {
  display: none;
}

.hidden-link {
  display: none;
}

// Natural Products show
table.species-mappings {
  margin-bottom: 0;

  tbody {
    tr {
      td { padding: .4em .5em }
    }
  }

  td:nth-child(1) {
    width: 50%;
  }

  td:nth-child(2) {
    width: 25%;
  }

  td:nth-child(3) {
    width: 25%;
  }
}

// Override btn-group's last element to be flat so we can add another btn to the group
.no-radius {
  border-radius: 0 0 0 0 !important;
}

.results-box {
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  padding: 1em 2em;
  margin-top: 1em;
  margin-bottom: 1em;
  overflow: hidden;
}

.panel-heading {
  font-weight: bold;
}

#spectra-viewer {
  width: 100%;
}

.spec-table th {
  text-align: right;
}

.spec-table th, .spec-table td {
  padding: 0 5px;
}

td > table.table {
  table-layout: fixed;
}

// Filter option sizing
.checkbox > label {
  font-size: 1.25em;
}

.structure-link-addons > .btn {
  &:first-of-type {
    margin-left: -1px;
  }
  &:not(:last-of-type) {
    border-radius: 0 0 0 0 !important;
  }
  &:last-of-type {
    border-radius: 0 3px 3px 0 !important;
  }
}
