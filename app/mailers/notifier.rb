class Notifier < ActionMailer::Base
  default from: '<EMAIL>'


  # Subject can be set in your I18n file at config/locales/en.yml
  # with the following lookup:
  #
  #   en.notifier.password_reset_instructions.subject
  #
  def deliver_password_reset_instructions(user)
    @edit_password_reset_url = edit_password_reset_url(user.perishable_token)

    mail to: user.email, subject: 'Password Reset Instructions'
  end

  def account_verification_instructions(user)
    @confirm_account_url = user_verification_url(user.perishable_token)

    mail to: user.email, subject: 'Validate your details'
  end

  def finished_job(email, file_name, project_name, excel_path, batch_id)
    @link_to_edit = URI.join(root_url, batch_submission_path(batch_id))
    attachments["#{project_name}.xlsx"] = File.read(excel_path)
    @file_name = file_name
    mail to: email, subject: 'Bulk Offline Upload Status'
  end

  def completed_deposition(email, submission_id, attached_files)
    @submission_id = submission_id
    attached_files.each {|filename, file_path| attachments[filename.to_s] = File.read(file_path)}
    mail to: email, subject: "Completed Submission for #{submission_id}"
  end

  def completed_external_deposition(collection)
    @submission_collection = collection
    mail to: collection.user_email, subject: "Completed Submission to NP-MRD (#{collection.submission_id})"
  end
end
